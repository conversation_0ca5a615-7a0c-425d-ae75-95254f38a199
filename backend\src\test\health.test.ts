import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import request from 'supertest'
import express from 'express'
import { PrismaClient } from '@prisma/client'
import dotenv from 'dotenv'

// Load test environment
dotenv.config({ path: '.env.test' })

// Create a minimal test app
const app = express()
app.use(express.json())

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Server is healthy',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
  })
})

// Database health check endpoint
app.get('/health/db', async (req, res) => {
  try {
    const prisma = new PrismaClient()
    await prisma.$queryRaw`SELECT 1`
    await prisma.$disconnect()
    res.status(200).json({
      success: true,
      message: 'Database connection is healthy',
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Database connection failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    })
  }
})

describe('API Health Checks', () => {
  let prisma: PrismaClient

  beforeAll(async () => {
    prisma = new PrismaClient()
  })

  afterAll(async () => {
    await prisma.$disconnect()
  })

  it('should respond to health check', async () => {
    const response = await request(app).get('/health')
    expect(response.status).toBe(200)
    expect(response.body.success).toBe(true)
    expect(response.body.message).toBe('Server is healthy')
    expect(response.body.timestamp).toBeDefined()
  })

  it('should connect to database', async () => {
    const response = await request(app).get('/health/db')
    expect(response.status).toBe(200)
    expect(response.body.success).toBe(true)
    expect(response.body.message).toBe('Database connection is healthy')
  })

  it('should verify database tables exist', async () => {
    const tables = await prisma.$queryRaw<Array<{ name: string }>>`
      SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name NOT LIKE '_prisma_%';
    `
    
    expect(tables.length).toBeGreaterThan(0)
    
    // Check for essential tables
    const tableNames = tables.map(t => t.name)
    expect(tableNames).toContain('User')
    expect(tableNames).toContain('Patient')
    expect(tableNames).toContain('LabResult')
    expect(tableNames).toContain('Appointment')
  })
})
