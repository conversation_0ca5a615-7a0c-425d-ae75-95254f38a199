# TypeScript Errors Analysis and Resolution Plan

## Current Status
- **Initial errors**: 84 errors in 14 files
- **Current errors**: ~70 errors in 13 files
- **Progress**: Small files resolved, major structural issues remain

## Resolved Issues ✅
1. **JWT utils** - Fixed secret type assertions
2. **Middleware auth** - Fixed role comparison logic
3. **PatientService** - Added missing DatabaseError import
4. **Minimal server** - Fixed return statements in route handlers

## Major Issues Requiring Resolution

### 1. Controller Return Type Issues (Priority: HIGH)
**Problem**: All controller methods return `Promise<Response>` instead of `Promise<void>`

**Affected Files**:
- `src/controllers/labResultController.ts` (8 errors)
- `src/controllers/notificationController.ts` (8 errors) 
- `src/controllers/patientController.ts` (3 errors)
- `src/routes/auth.ts` (3 errors)
- `src/routes/notifications.ts` (3 errors)
- `src/routes/patients.ts` (6 errors)

**Solution**: Change all `return res.status().json()` to `res.status().json(); return;`

**Example Fix**:
```typescript
// BEFORE (incorrect)
static async createPatient(req: AuthRequest, res: Response, next: NextFunction) {
  if (!req.user) {
    return res.status(401).json({ success: false, error: 'Auth required' });
  }
  // ...
}

// AFTER (correct)
static async createPatient(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
  if (!req.user) {
    res.status(401).json({ success: false, error: 'Auth required' });
    return;
  }
  // ...
}
```

### 2. Missing Service Methods (Priority: HIGH)
**Problem**: Controllers reference methods that don't exist in services

**Missing Methods**:
- `LabResultService.updateLabResult()` - Referenced in labResultController.ts:201
- `NotificationService.getNotificationById()` - Referenced in notificationController.ts:95
- `NotificationService.markAllAsRead()` - Referenced in notificationController.ts:135
- `NotificationService.deleteNotification()` - Referenced in notificationController.ts:155
- `PatientService.searchPatients()` - Referenced in patientController.ts:192

**Solution**: Implement these missing methods in their respective service classes

### 3. Prisma Schema Mismatches (Priority: CRITICAL)
**Problem**: Code references database fields that don't exist in the Prisma schema

**Missing Fields**:
- `LabResult.createdBy` - Used in labResultService.ts:61
- `LabResult.creator` - Used in labResultService.ts:193, 251, 333
- `Notification.channel` - Used in notificationService.ts:50
- `Notification.status` - Used in notificationService.ts:239, 434, 464, 477
- `User.phone` - Used in notificationService.ts:65, 446
- `RecurringAppointment.providerId` - Used in recurringAppointmentService.ts:79
- `RecurringAppointment.provider` - Used in recurringAppointmentService.ts:101, 433, 496, 523

**Solution Options**:
1. **Update Prisma Schema** (Recommended): Add missing fields to schema.prisma
2. **Modify Code**: Remove references to non-existent fields

### 4. Service Method Parameter Mismatches (Priority: MEDIUM)
**Problem**: Controllers call service methods with wrong number of parameters

**Examples**:
- `NotificationService.getNotifications()` expects 3 args, called with 2
- `NotificationService.markAsRead()` expects 3 args, called with 2

### 5. Type Definition Issues (Priority: MEDIUM)
**Problem**: Some interfaces don't match validation schemas

**Examples**:
- `CreateRecurringAppointmentData.isActive` - Property doesn't exist but code tries to access it
- Priority enum mismatch: Code uses "CRITICAL" but type only allows "URGENT"

## Recommended Resolution Order

### Phase 1: Database Schema Updates
1. Update `prisma/schema.prisma` to add missing fields
2. Run `npx prisma generate` to update client types
3. Run `npx prisma db push` to update database

### Phase 2: Service Method Implementation
1. Implement missing service methods
2. Fix parameter mismatches in existing methods

### Phase 3: Controller Return Types
1. Fix all controller methods to return `Promise<void>`
2. Update route handler signatures

### Phase 4: Type Definitions
1. Align interfaces with validation schemas
2. Fix enum mismatches

## Prisma Schema Updates Needed

```prisma
model LabResult {
  // ... existing fields
  createdBy String
  creator   User   @relation(fields: [createdBy], references: [id])
}

model Notification {
  // ... existing fields
  channel String @default("IN_APP")
  status  String @default("PENDING")
}

model User {
  // ... existing fields
  phone String?
}

model RecurringAppointment {
  // ... existing fields
  providerId String
  provider   User   @relation(fields: [providerId], references: [id])
}
```

## Detailed Fix Commands

### Controller Return Type Fixes (Quick Wins)
```bash
# Files to fix return statements:
# - src/controllers/labResultController.ts (lines 45, 72, 99, 126, 162, 193, 221)
# - src/controllers/notificationController.ts (lines 169, 195, 238)
# - src/controllers/patientController.ts (lines 140, 165)

# Pattern: Change "return res.status().json()" to "res.status().json(); return;"
```

### Missing Service Methods to Implement

#### LabResultService.updateLabResult()
```typescript
static async updateLabResult(
  id: string,
  data: Partial<CreateLabResultData>,
  updatedBy: string,
  auditData?: { ipAddress?: string; userAgent?: string }
): Promise<ApiResponse<{ labResult: any }>> {
  // Implementation needed
}
```

#### NotificationService Methods
```typescript
static async getNotificationById(id: string, userId: string): Promise<ApiResponse<any>> {
  // Implementation needed
}

static async markAllAsRead(userId: string): Promise<ApiResponse<any>> {
  // Implementation needed
}

static async deleteNotification(id: string, userId: string): Promise<ApiResponse<any>> {
  // Implementation needed
}
```

#### PatientService.searchPatients()
```typescript
static async searchPatients(
  term: string,
  userId: string,
  userRole: string
): Promise<ApiResponse<any>> {
  // Implementation needed
}
```

### Critical Prisma Schema Fields to Add

```prisma
model LabResult {
  id            String   @id @default(cuid())
  patientId     String
  testType      String
  testDate      DateTime
  orderedBy     String
  labName       String?
  results       String
  normalRanges  String?
  flags         String?
  notes         String?
  status        String   @default("COMPLETED")
  createdBy     String   // ADD THIS
  isDeleted     Boolean  @default(false)
  deletedAt     DateTime?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  patient       Patient  @relation(fields: [patientId], references: [id])
  creator       User     @relation("LabResultCreator", fields: [createdBy], references: [id]) // ADD THIS
}

model Notification {
  id            String   @id @default(cuid())
  recipientId   String
  type          String
  title         String
  message       String
  priority      String   @default("MEDIUM")
  channel       String   @default("IN_APP") // ADD THIS
  status        String   @default("PENDING") // ADD THIS
  scheduledFor  DateTime @default(now())
  isRead        Boolean  @default(false)
  isProcessed   Boolean  @default(false)
  processedAt   DateTime?
  patientId     String?
  appointmentId String?
  labResultId   String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  recipient     User     @relation("NotificationRecipient", fields: [recipientId], references: [id])
}

model User {
  id                    String   @id @default(cuid())
  username              String   @unique
  email                 String   @unique
  password              String
  firstName             String
  lastName              String
  phone                 String?  // ADD THIS
  role                  String   @default("CLINICIAN")
  isActive              Boolean  @default(true)
  lastLoginAt           DateTime?
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  // Relations
  createdLabResults     LabResult[] @relation("LabResultCreator") // ADD THIS
  notifications         Notification[] @relation("NotificationRecipient") // ADD THIS
}

model RecurringAppointment {
  id              String   @id @default(cuid())
  patientId       String
  providerId      String   // ADD THIS
  startDate       DateTime
  endDate         DateTime?
  duration        Int
  type            String
  frequency       String
  interval        Int      @default(1)
  dayOfWeek       Int?
  dayOfMonth      Int?
  timeSlot        String
  notes           String?
  isActive        Boolean  @default(true)
  maxOccurrences  Int?
  isDeleted       Boolean  @default(false)
  deletedAt       DateTime?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  patient         Patient  @relation(fields: [patientId], references: [id])
  provider        User     @relation("RecurringAppointmentProvider", fields: [providerId], references: [id]) // ADD THIS
}
```
PS C:\Users\<USER>\projects\7> cd psychiatry-app/backend && npx tsc --noEmit
src/controllers/labResultController.ts:45:9 - error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.

45         return res.status(401).json({
           ~~~~~~

src/controllers/labResultController.ts:72:9 - error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.

72         return res.status(401).json({
           ~~~~~~

src/controllers/labResultController.ts:99:9 - error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.

99         return res.status(401).json({
           ~~~~~~

src/controllers/labResultController.ts:126:9 - error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.

126         return res.status(401).json({
            ~~~~~~

src/controllers/labResultController.ts:162:9 - error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.

162         return res.status(401).json({
            ~~~~~~

src/controllers/labResultController.ts:193:9 - error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.

193         return res.status(401).json({
            ~~~~~~

src/controllers/labResultController.ts:201:45 - error TS2551: Property 'updateLabResult' does not exist on type 'typeof LabResultService'. Did you mean 'createLabResult'?    

201       const result = await LabResultService.updateLabResult(
                                                ~~~~~~~~~~~~~~~

  src/services/labResultService.ts:20:16
    20   static async createLabResult(
                      ~~~~~~~~~~~~~~~
    'createLabResult' is declared here.

src/controllers/labResultController.ts:221:9 - error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.

221         return res.status(401).json({
            ~~~~~~

src/controllers/notificationController.ts:75:48 - error TS2554: Expected 3 arguments, but got 2.

75       const result = await NotificationService.getNotifications(req.user!.id, query);
                                                  ~~~~~~~~~~~~~~~~

  src/services/notificationService.ts:110:5
    110     userRole: string
            ~~~~~~~~~~~~~~~~
    An argument for 'userRole' was not provided.

src/controllers/notificationController.ts:95:48 - error TS2551: Property 'getNotificationById' does not exist on type 'typeof NotificationService'. Did you mean 'getNotifications'?

95       const result = await NotificationService.getNotificationById(req.params.id, req.user!.id);
                                                  ~~~~~~~~~~~~~~~~~~~

  src/services/notificationService.ts:97:16
    97   static async getNotifications(
                      ~~~~~~~~~~~~~~~~
    'getNotifications' is declared here.

src/controllers/notificationController.ts:115:48 - error TS2554: Expected 3 arguments, 
but got 2.

115       const result = await NotificationService.markAsRead(req.params.id, req.user!.id);
                                                   ~~~~~~~~~~

  src/services/notificationService.ts:219:5
    219     userRole: string
            ~~~~~~~~~~~~~~~~
    An argument for 'userRole' was not provided.

src/controllers/notificationController.ts:135:48 - error TS2551: Property 'markAllAsRead' does not exist on type 'typeof NotificationService'. Did you mean 'markAsRead'?     

135       const result = await NotificationService.markAllAsRead(req.user!.id);        
                                                   ~~~~~~~~~~~~~

  src/services/notificationService.ts:216:16
    216   static async markAsRead(
                       ~~~~~~~~~~
    'markAsRead' is declared here.

src/controllers/notificationController.ts:155:48 - error TS2551: Property 'deleteNotification' does not exist on type 'typeof NotificationService'. Did you mean 'createNotification'?

155       const result = await NotificationService.deleteNotification(req.params.id, req.user!.id);
                                                   ~~~~~~~~~~~~~~~~~~

  src/services/notificationService.ts:21:16
    21   static async createNotification(
                      ~~~~~~~~~~~~~~~~~~
    'createNotification' is declared here.

src/controllers/notificationController.ts:169:9 - error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.

169         return res.status(401).json({
            ~~~~~~

src/controllers/notificationController.ts:195:9 - error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.

195         return res.status(401).json({
            ~~~~~~

src/controllers/notificationController.ts:238:9 - error TS2322: Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.

238         return res.status(401).json({
            ~~~~~~

src/controllers/patientController.ts:140:9 - error TS2345: Argument of type '{ ipAddress: string; userAgent: string; }' is not assignable to parameter of type 'string'.      

140         { ipAddress: req.ip, userAgent: req.get('User-Agent') }
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

src/controllers/patientController.ts:165:9 - error TS2345: Argument of type '{ ipAddress: string; userAgent: string; }' is not assignable to parameter of type 'string'.      

165         { ipAddress: req.ip, userAgent: req.get('User-Agent') }
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

src/controllers/patientController.ts:192:43 - error TS2339: Property 'searchPatients' does not exist on type 'typeof PatientService'.

192       const result = await PatientService.searchPatients(term, req.user!.id, req.user!.role);
                                              ~~~~~~~~~~~~~~

src/routes/auth.ts:35:25 - error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<...>' 
is not assignable to parameter of type 'Application<Record<string, any>>'.
      Type '(req: Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<...>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.

35 router.post('/refresh', AuthController.refreshToken);
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~

  node_modules/@types/express-serve-static-core/index.d.ts:168:5
    168     (path: PathParams, subApplication: Application): T;
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    The last overload is declared here.

src/routes/auth.ts:50:33 - error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: AuthRequest, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<Response<any, Record<string, any>>>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(req: AuthRequest, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<Response<any, Record<string, any>>>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 
'Promise<void>'.
            Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
50 router.get('/me', authenticate, AuthController.getCurrentUser);
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  node_modules/@types/express-serve-static-core/index.d.ts:157:5
    157     <
            ~
    158         P = ParamsDictionary,
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    ...
    166         ...handlers: Array<RequestHandlerParams<P, ResBody, ReqBody, ReqQuery, 
LocalsObj>>
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    167     ): T;
        ~~~~~~~~~
    The last overload is declared here.

src/routes/auth.ts:58:70 - error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: AuthRequest, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<Response<any, Record<string, any>>>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(req: AuthRequest, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<Response<any, Record<string, any>>>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 
'Promise<void>'.
            Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
58 router.put('/change-password', authenticate, passwordResetRateLimit, AuthController.changePassword);
                                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  node_modules/@types/express-serve-static-core/index.d.ts:157:5
    157     <
            ~
    158         P = ParamsDictionary,
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    ...
    166         ...handlers: Array<RequestHandlerParams<P, ResBody, ReqBody, ReqQuery, 
LocalsObj>>
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    167     ): T;
        ~~~~~~~~~
    The last overload is declared here.

src/routes/notifications.ts:51:17 - error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: AuthRequest, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<Response<any, Record<string, any>>>' is not assignable to parameter of type 'Application<Record<string, any>>'.
      Type '(req: AuthRequest, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<Response<any, Record<string, any>>>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.

51 router.get('/', NotificationController.getNotifications);
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  node_modules/@types/express-serve-static-core/index.d.ts:168:5
    168     (path: PathParams, subApplication: Application): T;
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    The last overload is declared here.

src/routes/notifications.ts:59:62 - error TS2769: No overload matches this call.       
  The last overload gave the following error.
    Argument of type '(req: AuthRequest, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<Response<any, Record<string, any>>>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(req: AuthRequest, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<Response<any, Record<string, any>>>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 
'Promise<void>'.
            Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
59 router.post('/', authorize(['ADMIN', 'CLINICIAN', 'STAFF']), NotificationController.createNotification);
                                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  node_modules/@types/express-serve-static-core/index.d.ts:157:5
    157     <
            ~
    158         P = ParamsDictionary,
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    ...
    166         ...handlers: Array<RequestHandlerParams<P, ResBody, ReqBody, ReqQuery, 
LocalsObj>>
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    167     ): T;
        ~~~~~~~~~
    The last overload is declared here.

src/routes/notifications.ts:66:25 - error TS2769: No overload matches this call.       
  The last overload gave the following error.
    Argument of type '(req: AuthRequest, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<Response<any, Record<string, any>>>' is not assignable to parameter of type 'Application<Record<string, any>>'.
      Type '(req: AuthRequest, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<Response<any, Record<string, any>>>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.

66 router.put('/:id/read', NotificationController.markAsRead);
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  node_modules/@types/express-serve-static-core/index.d.ts:168:5
    168     (path: PathParams, subApplication: Application): T;
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    The last overload is declared here.

src/routes/patients.ts:22:22 - error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: AuthRequest, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<Response<any, Record<string, any>>>' is not assignable to parameter of type 'Application<Record<string, any>>'.
      Type '(req: AuthRequest, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<Response<any, Record<string, any>>>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.

22 router.get('/stats', PatientController.getPatientStats);
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  node_modules/@types/express-serve-static-core/index.d.ts:168:5
    168     (path: PathParams, subApplication: Application): T;
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    The last overload is declared here.

src/routes/patients.ts:30:17 - error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: AuthRequest, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<Response<any, Record<string, any>>>' is not assignable to parameter of type 'Application<Record<string, any>>'.
      Type '(req: AuthRequest, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<Response<any, Record<string, any>>>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.

30 router.get('/', PatientController.getPatients);
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  node_modules/@types/express-serve-static-core/index.d.ts:168:5
    168     (path: PathParams, subApplication: Application): T;
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    The last overload is declared here.

src/routes/patients.ts:38:53 - error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: AuthRequest, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<Response<any, Record<string, any>>>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(req: AuthRequest, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<Response<any, Record<string, any>>>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 
'Promise<void>'.
            Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
38 router.post('/', authorize(['ADMIN', 'CLINICIAN']), PatientController.createPatient);
                                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ 

  node_modules/@types/express-serve-static-core/index.d.ts:157:5
    157     <
            ~
    158         P = ParamsDictionary,
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    ...
    166         ...handlers: Array<RequestHandlerParams<P, ResBody, ReqBody, ReqQuery, 
LocalsObj>>
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    167     ): T;
        ~~~~~~~~~
    The last overload is declared here.

src/routes/patients.ts:45:20 - error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: AuthRequest, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<Response<any, Record<string, any>>>' is not assignable to parameter of type 'Application<Record<string, any>>'.
      Type '(req: AuthRequest, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<Response<any, Record<string, any>>>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.

45 router.get('/:id', PatientController.getPatientById);
                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  node_modules/@types/express-serve-static-core/index.d.ts:168:5
    168     (path: PathParams, subApplication: Application): T;
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    The last overload is declared here.

src/routes/patients.ts:52:55 - error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: AuthRequest, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<Response<any, Record<string, any>>>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(req: AuthRequest, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<Response<any, Record<string, any>>>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 
'Promise<void>'.
            Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
52 router.put('/:id', authorize(['ADMIN', 'CLINICIAN']), PatientController.updatePatient);
                                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  node_modules/@types/express-serve-static-core/index.d.ts:157:5
    157     <
            ~
    158         P = ParamsDictionary,
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    ...
    166         ...handlers: Array<RequestHandlerParams<P, ResBody, ReqBody, ReqQuery, 
LocalsObj>>
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    167     ): T;
        ~~~~~~~~~
    The last overload is declared here.

src/routes/patients.ts:59:45 - error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type '(req: AuthRequest, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<Response<any, Record<string, any>>>' is not assignable to parameter of type 'RequestHandlerParams<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
      Type '(req: AuthRequest, res: Response<any, Record<string, any>>, next: NextFunction) => Promise<Response<any, Record<string, any>>>' is not assignable to type 'RequestHandler<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
        Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 'void | Promise<void>'.
          Type 'Promise<Response<any, Record<string, any>>>' is not assignable to type 
'Promise<void>'.
            Type 'Response<any, Record<string, any>>' is not assignable to type 'void'.
59 router.delete('/:id', authorize(['ADMIN']), PatientController.deletePatient);       
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  node_modules/@types/express-serve-static-core/index.d.ts:157:5
    157     <
            ~
    158         P = ParamsDictionary,
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    ...
    166         ...handlers: Array<RequestHandlerParams<P, ResBody, ReqBody, ReqQuery, 
LocalsObj>>
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    167     ): T;
        ~~~~~~~~~
    The last overload is declared here.

src/services/labResultService.ts:61:9 - error TS2353: Object literal may only specify known properties, and 'createdBy' does not exist in type '(Without<LabResultCreateInput, LabResultUncheckedCreateInput> & LabResultUncheckedCreateInput) | (Without<...> & LabResultCreateInput)'.

61         createdBy,
           ~~~~~~~~~

  node_modules/.prisma/client/index.d.ts:6789:5
    6789     data: XOR<LabResultCreateInput, LabResultUncheckedCreateInput>
             ~~~~
    The expected type comes from property 'data' which is declared here on type '{ select?: LabResultSelect<DefaultArgs>; omit?: LabResultOmit<DefaultArgs>; include?: LabResultInclude<DefaultArgs>; data: (Without<...> & LabResultUncheckedCreateInput) | (Without<...> & LabResultCreateInput); }'

src/services/labResultService.ts:193:11 - error TS2353: Object literal may only specify known properties, and 'creator' does not exist in type 'LabResultInclude<DefaultArgs>'.

193           creator: {
              ~~~~~~~

  node_modules/.prisma/client/index.d.ts:6738:5
    6738     include?: LabResultInclude<ExtArgs> | null
             ~~~~~~~
    The expected type comes from property 'include' which is declared here on type '{ select?: LabResultSelect<DefaultArgs>; omit?: LabResultOmit<DefaultArgs>; include?: LabResultInclude<DefaultArgs>; ... 5 more ...; distinct?: LabResultScalarFieldEnum | LabResultScalarFieldEnum[]; }'

src/services/labResultService.ts:251:9 - error TS2353: Object literal may only specify 
known properties, and 'creator' does not exist in type 'LabResultInclude<DefaultArgs>'.
251         creator: {
            ~~~~~~~

  node_modules/.prisma/client/index.d.ts:6634:5
    6634     include?: LabResultInclude<ExtArgs> | null
             ~~~~~~~
    The expected type comes from property 'include' which is declared here on type '{ select?: LabResultSelect<DefaultArgs>; omit?: LabResultOmit<DefaultArgs>; include?: LabResultInclude<DefaultArgs>; ... 5 more ...; distinct?: LabResultScalarFieldEnum | LabResultScalarFieldEnum[]; }'

src/services/labResultService.ts:333:9 - error TS2353: Object literal may only specify 
known properties, and 'creator' does not exist in type 'LabResultInclude<DefaultArgs>'.
333         creator: {
            ~~~~~~~

  node_modules/.prisma/client/index.d.ts:6738:5
    6738     include?: LabResultInclude<ExtArgs> | null
             ~~~~~~~
    The expected type comes from property 'include' which is declared here on type '{ select?: LabResultSelect<DefaultArgs>; omit?: LabResultOmit<DefaultArgs>; include?: LabResultInclude<DefaultArgs>; ... 5 more ...; distinct?: LabResultScalarFieldEnum | LabResultScalarFieldEnum[]; }'

src/services/notificationService.ts:50:9 - error TS2353: Object literal may only specify known properties, and 'channel' does not exist in type '(Without<NotificationCreateInput, NotificationUncheckedCreateInput> & NotificationUncheckedCreateInput) | (Without<...> & NotificationCreateInput)'.

50         channel: data.channel || 'IN_APP',
           ~~~~~~~

  node_modules/.prisma/client/index.d.ts:9471:5
    9471     data: XOR<NotificationCreateInput, NotificationUncheckedCreateInput>      
             ~~~~
    The expected type comes from property 'data' which is declared here on type '{ select?: NotificationSelect<DefaultArgs>; omit?: NotificationOmit<DefaultArgs>; include?: NotificationInclude<DefaultArgs>; data: (Without<...> & NotificationUncheckedCreateInput) | (Without<...> & NotificationCreateInput); }'

src/services/notificationService.ts:65:13 - error TS2353: Object literal may only specify known properties, and 'phone' does not exist in type 'UserSelect<DefaultArgs>'.     

65             phone: true,
               ~~~~~

src/services/notificationService.ts:239:9 - error TS2353: Object literal may only specify known properties, and 'status' does not exist in type '(Without<NotificationUpdateInput, NotificationUncheckedUpdateInput> & NotificationUncheckedUpdateInput) | (Without<...> & NotificationUpdateInput)'.

239         status: 'READ',
            ~~~~~~

  node_modules/.prisma/client/index.d.ts:9525:5
    9525     data: XOR<NotificationUpdateInput, NotificationUncheckedUpdateInput>      
             ~~~~
    The expected type comes from property 'data' which is declared here on type '{ select?: NotificationSelect<DefaultArgs>; omit?: NotificationOmit<DefaultArgs>; include?: NotificationInclude<DefaultArgs>; data: (Without<...> & NotificationUncheckedUpdateInput) | (Without<...> & NotificationUpdateInput); where: NotificationWhereUniqueInput; }'  

src/services/notificationService.ts:381:9 - error TS2353: Object literal may only specify known properties, and 'creator' does not exist in type 'LabResultInclude<DefaultArgs>'.

381         creator: {
            ~~~~~~~

  node_modules/.prisma/client/index.d.ts:6634:5
    6634     include?: LabResultInclude<ExtArgs> | null
             ~~~~~~~
    The expected type comes from property 'include' which is declared here on type '{ select?: LabResultSelect<DefaultArgs>; omit?: LabResultOmit<DefaultArgs>; include?: LabResultInclude<DefaultArgs>; ... 5 more ...; distinct?: LabResultScalarFieldEnum | LabResultScalarFieldEnum[]; }'

src/services/notificationService.ts:407:30 - error TS2551: Property 'patient' does not 
exist on type '{ notes: string; patientId: string; status: string; id: string; createdAt: Date; updatedAt: Date; testType: string; testDate: Date; orderedBy: string; labName: string; results: string; normalRanges: string; flags: string; isDeleted: boolean; deletedAt: Date; }'. Did you mean 'patientId'?

407       recipientId: labResult.patient.id,
                                 ~~~~~~~

src/services/notificationService.ts:411:7 - error TS2322: Type '"HIGH" | "CRITICAL" | "MEDIUM"' is not assignable to type '"HIGH" | "LOW" | "MEDIUM" | "URGENT"'.
  Type '"CRITICAL"' is not assignable to type '"HIGH" | "LOW" | "MEDIUM" | "URGENT"'.  

411       priority,
          ~~~~~~~~

  src/types/index.ts:153:3
    153   priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
          ~~~~~~~~
    The expected type comes from property 'priority' which is declared here on type 'CreateNotificationData'

src/services/notificationService.ts:414:28 - error TS2551: Property 'patient' does not 
exist on type '{ notes: string; patientId: string; status: string; id: string; createdAt: Date; updatedAt: Date; testType: string; testDate: Date; orderedBy: string; labName: string; results: string; normalRanges: string; flags: string; isDeleted: boolean; deletedAt: Date; }'. Did you mean 'patientId'?

414       patientId: labResult.patient.id,
                               ~~~~~~~

src/services/notificationService.ts:434:9 - error TS2353: Object literal may only specify known properties, and 'status' does not exist in type 'NotificationWhereInput'.     

434         status: 'PENDING',
            ~~~~~~

  node_modules/.prisma/client/index.d.ts:9424:5
    9424     where?: NotificationWhereInput
             ~~~~~
    The expected type comes from property 'where' which is declared here on type '{ select?: NotificationSelect<DefaultArgs>; omit?: NotificationOmit<DefaultArgs>; include?: 
NotificationInclude<DefaultArgs>; ... 5 more ...; distinct?: NotificationScalarFieldEnum | NotificationScalarFieldEnum[]; }'

src/services/notificationService.ts:446:13 - error TS2353: Object literal may only specify known properties, and 'phone' does not exist in type 'UserSelect<DefaultArgs>'.    

446             phone: true,
                ~~~~~

src/services/notificationService.ts:464:13 - error TS2353: Object literal may only specify known properties, and 'status' does not exist in type '(Without<NotificationUpdateInput, NotificationUncheckedUpdateInput> & NotificationUncheckedUpdateInput) | (Without<...> & NotificationUpdateInput)'.

464             status: 'SENT',
                ~~~~~~

  node_modules/.prisma/client/index.d.ts:9525:5
    9525     data: XOR<NotificationUpdateInput, NotificationUncheckedUpdateInput>      
             ~~~~
    The expected type comes from property 'data' which is declared here on type '{ select?: NotificationSelect<DefaultArgs>; omit?: NotificationOmit<DefaultArgs>; include?: NotificationInclude<DefaultArgs>; data: (Without<...> & NotificationUncheckedUpdateInput) | (Without<...> & NotificationUpdateInput); where: NotificationWhereUniqueInput; }'  

src/services/notificationService.ts:477:13 - error TS2353: Object literal may only specify known properties, and 'status' does not exist in type '(Without<NotificationUpdateInput, NotificationUncheckedUpdateInput> & NotificationUncheckedUpdateInput) | (Without<...> & NotificationUpdateInput)'.

477             status: 'FAILED',
                ~~~~~~

  node_modules/.prisma/client/index.d.ts:9525:5
    9525     data: XOR<NotificationUpdateInput, NotificationUncheckedUpdateInput>      
             ~~~~
    The expected type comes from property 'data' which is declared here on type '{ select?: NotificationSelect<DefaultArgs>; omit?: NotificationOmit<DefaultArgs>; include?: NotificationInclude<DefaultArgs>; data: (Without<...> & NotificationUncheckedUpdateInput) | (Without<...> & NotificationUpdateInput); where: NotificationWhereUniqueInput; }'  

src/services/notificationService.ts:479:31 - error TS2339: Property 'metadata' does not exist on type '{ message: string; patientId: string; type: string; title: string; id: 
string; createdAt: Date; updatedAt: Date; labResultId: string; appointmentId: string; recipientId: string; priority: string; scheduledFor: Date; isRead: boolean; isProcessed: boolean; processedAt: Date; }'.

479               ...notification.metadata,
                                  ~~~~~~~~

src/services/notificationService.ts:577:7 - error TS2615: Type of property 'AND' circularly references itself in mapped type '{ [K in keyof { AND?: NotificationScalarWhereWithAggregatesInput | NotificationScalarWhereWithAggregatesInput[]; ... 16 more ...; labResultId?: string | StringNullableWithAggregatesFilter<...>; }]: Or<...> extends 1 ? { ...; }[K] extends infer TK ? GetHavingFields<...> : never : {} extends FieldPaths<...> ? 
never : ...'.

577       prisma.notification.groupBy({
          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
578         by: ['status'],
    ~~~~~~~~~~~~~~~~~~~~~~~
...
580         _count: { status: true },
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
581       }),
    ~~~~~~~~

src/services/notificationService.ts:577:7 - error TS2615: Type of property 'NOT' circularly references itself in mapped type '{ [K in keyof { AND?: NotificationScalarWhereWithAggregatesInput | NotificationScalarWhereWithAggregatesInput[]; ... 16 more ...; labResultId?: string | StringNullableWithAggregatesFilter<...>; }]: Or<...> extends 1 ? { ...; }[K] extends infer TK ? GetHavingFields<...> : never : {} extends FieldPaths<...> ? 
never : ...'.

577       prisma.notification.groupBy({
          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
578         by: ['status'],
    ~~~~~~~~~~~~~~~~~~~~~~~
...
580         _count: { status: true },
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
581       }),
    ~~~~~~~~

src/services/notificationService.ts:577:7 - error TS2615: Type of property 'OR' circularly references itself in mapped type '{ [K in keyof { AND?: NotificationScalarWhereWithAggregatesInput | NotificationScalarWhereWithAggregatesInput[]; ... 16 more ...; labResultId?: string | StringNullableWithAggregatesFilter<...>; }]: Or<...> extends 1 ? { ...; }[K] extends infer TK ? GetHavingFields<...> : never : {} extends FieldPaths<...> ? never : ...'.

577       prisma.notification.groupBy({
          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
578         by: ['status'],
    ~~~~~~~~~~~~~~~~~~~~~~~
...
580         _count: { status: true },
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
581       }),
    ~~~~~~~~

src/services/patientService.ts:10:62 - error TS2305: Module '"@/utils/errors"' has no exported member 'DatabaseError'.

10 import { NotFoundError, ValidationError, AuthorizationError, DatabaseError } from '@/utils/errors';
                                                                ~~~~~~~~~~~~~

src/services/recurringAppointmentService.ts:79:9 - error TS2353: Object literal may only specify known properties, and 'providerId' does not exist in type '(Without<RecurringAppointmentCreateInput, RecurringAppointmentUncheckedCreateInput> & RecurringAppointmentUncheckedCreateInput) | (Without<...> & RecurringAppointmentCreateInput)'.

79         providerId: data.providerId,
           ~~~~~~~~~~

  node_modules/.prisma/client/index.d.ts:10792:5
    10792     data: XOR<RecurringAppointmentCreateInput, RecurringAppointmentUncheckedCreateInput>
              ~~~~
    The expected type comes from property 'data' which is declared here on type '{ select?: RecurringAppointmentSelect<DefaultArgs>; omit?: RecurringAppointmentOmit<DefaultArgs>; include?: RecurringAppointmentInclude<...>; data: (Without<...> & RecurringAppointmentUncheckedCreateInput) | (Without<...> & RecurringAppointmentCreateInput); }'       

src/services/recurringAppointmentService.ts:101:9 - error TS2353: Object literal may only specify known properties, and 'provider' does not exist in type 'RecurringAppointmentInclude<DefaultArgs>'.

101         provider: true,
            ~~~~~~~~

  node_modules/.prisma/client/index.d.ts:10788:5
    10788     include?: RecurringAppointmentInclude<ExtArgs> | null
              ~~~~~~~
    The expected type comes from property 'include' which is declared here on type '{ select?: RecurringAppointmentSelect<DefaultArgs>; omit?: RecurringAppointmentOmit<DefaultArgs>; include?: RecurringAppointmentInclude<...>; data: (Without<...> & RecurringAppointmentUncheckedCreateInput) | (Without<...> & RecurringAppointmentCreateInput); }'    

src/services/recurringAppointmentService.ts:127:7 - error TS2741: Property 'appointments' is missing in type '{ recurringAppointment: { patient: { firstName: string; lastName: string; dateOfBirth: Date; gender: string; phone: string | null; email: string | null; address: string | null; occupation: string | null; ... 15 more ...; createdBy: string; }; ... 17 more ...; maxOccurrences: number | null; }; }' but required in type '{ recurringAppointment: any; appointments: any[]; }'.

127       data: {
          ~~~~

  src/services/recurringAppointmentService.ts:25:55
    25   ): Promise<ApiResponse<{ recurringAppointment: any; appointments: any[] }>> { 
                                                             ~~~~~~~~~~~~
    'appointments' is declared here.
  src/types/index.ts:189:3
    189   data?: T;
          ~~~~
    The expected type comes from property 'data' which is declared here on type 'ApiResponse<{ recurringAppointment: any; appointments: any[]; }>'

src/services/recurringAppointmentService.ts:165:44 - error TS2339: Property 'providerId' does not exist on type '{ notes: string; isActive: boolean; patientId: string; duration: number; type: string; id: string; createdAt: Date; updatedAt: Date; isDeleted: boolean; deletedAt: Date; startDate: Date; ... 5 more ...; maxOccurrences: number; }'.     

165           providerId: recurringAppointment.providerId!,
                                               ~~~~~~~~~~

src/services/recurringAppointmentService.ts:168:11 - error TS2322: Type 'string' is not assignable to type '"OTHER" | "INITIAL_CONSULTATION" | "FOLLOW_UP" | "THERAPY_SESSION" | "MEDICATION_REVIEW" | "CRISIS_INTERVENTION" | "GROUP_THERAPY" | "FAMILY_THERAPY" | "PSYCHOLOGICAL_TESTING"'.

168           type: recurringAppointment.type,
              ~~~~

  src/types/index.ts:132:3
    132   type: 'INITIAL_CONSULTATION' | 'FOLLOW_UP' | 'THERAPY_SESSION' | 'MEDICATION_REVIEW' | 'CRISIS_INTERVENTION' | 'GROUP_THERAPY' | 'FAMILY_THERAPY' | 'PSYCHOLOGICAL_TESTING' | 'OTHER';
          ~~~~
    The expected type comes from property 'type' which is declared here on type 'CreateAppointmentData'

src/services/recurringAppointmentService.ts:169:39 - error TS2339: Property 'title' does not exist on type '{ notes: string; isActive: boolean; patientId: string; duration: number; type: string; id: string; createdAt: Date; updatedAt: Date; isDeleted: boolean; 
deletedAt: Date; startDate: Date; ... 5 more ...; maxOccurrences: number; }'.

169           title: recurringAppointment.title || undefined,
                                          ~~~~~

src/services/recurringAppointmentService.ts:170:45 - error TS2339: Property 'description' does not exist on type '{ notes: string; isActive: boolean; patientId: string; duration: number; type: string; id: string; createdAt: Date; updatedAt: Date; isDeleted: boolean; deletedAt: Date; startDate: Date; ... 5 more ...; maxOccurrences: number; }'.    

170           description: recurringAppointment.description || undefined,
                                                ~~~~~~~~~~~

src/services/recurringAppointmentService.ts:171:42 - error TS2339: Property 'location' 
does not exist on type '{ notes: string; isActive: boolean; patientId: string; duration: number; type: string; id: string; createdAt: Date; updatedAt: Date; isDeleted: boolean; deletedAt: Date; startDate: Date; ... 5 more ...; maxOccurrences: number; }'.       

171           location: recurringAppointment.location || undefined,
                                             ~~~~~~~~

src/services/recurringAppointmentService.ts:172:43 - error TS2339: Property 'isVirtual' does not exist on type '{ notes: string; isActive: boolean; patientId: string; duration: number; type: string; id: string; createdAt: Date; updatedAt: Date; isDeleted: boolean; deletedAt: Date; startDate: Date; ... 5 more ...; maxOccurrences: number; }'.      

172           isVirtual: recurringAppointment.isVirtual,
                                              ~~~~~~~~~

src/services/recurringAppointmentService.ts:173:51 - error TS2339: Property 'virtualMeetingUrl' does not exist on type '{ notes: string; isActive: boolean; patientId: string; duration: number; type: string; id: string; createdAt: Date; updatedAt: Date; isDeleted: boolean; deletedAt: Date; startDate: Date; ... 5 more ...; maxOccurrences: number; }'.

173           virtualMeetingUrl: recurringAppointment.virtualMeetingUrl || undefined,  
                                                      ~~~~~~~~~~~~~~~~~

src/services/recurringAppointmentService.ts:177:33 - error TS2339: Property 'providerId' does not exist on type '{ notes: string; isActive: boolean; patientId: string; duration: number; type: string; id: string; createdAt: Date; updatedAt: Date; isDeleted: boolean; deletedAt: Date; startDate: Date; ... 5 more ...; maxOccurrences: number; }'.     

177         }, recurringAppointment.providerId!);
                                    ~~~~~~~~~~

src/services/recurringAppointmentService.ts:181:26 - error TS2339: Property 'calculateNextOccurrence' does not exist on type 'typeof RecurringAppointmentService'.

181       currentDate = this.calculateNextOccurrence(
                             ~~~~~~~~~~~~~~~~~~~~~~~

src/services/recurringAppointmentService.ts:184:30 - error TS2339: Property 'interval' 
does not exist on type '{ notes: string; isActive: boolean; patientId: string; duration: number; type: string; id: string; createdAt: Date; updatedAt: Date; isDeleted: boolean; deletedAt: Date; startDate: Date; ... 5 more ...; maxOccurrences: number; }'.       

184         recurringAppointment.interval || 1
                                 ~~~~~~~~

src/services/recurringAppointmentService.ts:250:14 - error TS2339: Property 'isActive' 
does not exist on type 'Partial<CreateRecurringAppointmentData>'.

250     if (data.isActive !== undefined) updateData.isActive = data.isActive;
                 ~~~~~~~~

src/services/recurringAppointmentService.ts:250:65 - error TS2339: Property 'isActive' 
does not exist on type 'Partial<CreateRecurringAppointmentData>'.

250     if (data.isActive !== undefined) updateData.isActive = data.isActive;
                                                                    ~~~~~~~~

src/services/recurringAppointmentService.ts:433:11 - error TS2353: Object literal may only specify known properties, and 'provider' does not exist in type 'RecurringAppointmentInclude<DefaultArgs>'.

433           provider: {
              ~~~~~~~~

  node_modules/.prisma/client/index.d.ts:10741:5
    10741     include?: RecurringAppointmentInclude<ExtArgs> | null
              ~~~~~~~
    The expected type comes from property 'include' which is declared here on type '{ select?: RecurringAppointmentSelect<DefaultArgs>; omit?: RecurringAppointmentOmit<DefaultArgs>; ... 6 more ...; distinct?: RecurringAppointmentScalarFieldEnum | RecurringAppointmentScalarFieldEnum[]; }'

src/services/recurringAppointmentService.ts:496:9 - error TS2353: Object literal may only specify known properties, and 'provider' does not exist in type 'RecurringAppointmentInclude<DefaultArgs>'.

496         provider: {
            ~~~~~~~~

  node_modules/.prisma/client/index.d.ts:10593:5
    10593     include?: RecurringAppointmentInclude<ExtArgs> | null
              ~~~~~~~
    The expected type comes from property 'include' which is declared here on type '{ select?: RecurringAppointmentSelect<DefaultArgs>; omit?: RecurringAppointmentOmit<DefaultArgs>; include?: RecurringAppointmentInclude<...>; where: RecurringAppointmentWhereUniqueInput; }'

src/services/recurringAppointmentService.ts:523:9 - error TS2353: Object literal may only specify known properties, and 'provider' does not exist in type 'RecurringAppointmentInclude<DefaultArgs>'.

523         provider: {
            ~~~~~~~~

  node_modules/.prisma/client/index.d.ts:10741:5
    10741     include?: RecurringAppointmentInclude<ExtArgs> | null
              ~~~~~~~
    The expected type comes from property 'include' which is declared here on type '{ select?: RecurringAppointmentSelect<DefaultArgs>; omit?: RecurringAppointmentOmit<DefaultArgs>; ... 6 more ...; distinct?: RecurringAppointmentScalarFieldEnum | RecurringAppointmentScalarFieldEnum[]; }'

src/utils/jwt.ts:26:14 - error TS2769: No overload matches this call.
  Overload 1 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: null, options?: SignOptions & { algorithm: "none"; }): string', gave the following error.
    Argument of type 'string' is not assignable to parameter of type 'null'.
  Overload 2 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: Buffer<ArrayBufferLike> | Secret | PrivateKeyInput | JsonWebKeyInput, options?: SignOptions): string', gave the following error.
    Type 'string' is not assignable to type 'number | StringValue'.
  Overload 3 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: Buffer<ArrayBufferLike> | Secret | PrivateKeyInput | JsonWebKeyInput, callback: SignCallback): void', gave the following error.
    Object literal may only specify known properties, and 'expiresIn' does not exist in type 'SignCallback'.

26   return jwt.sign(payload, JWT_SECRET, {
                ~~~~

  node_modules/@types/jsonwebtoken/index.d.ts:43:5
    43     expiresIn?: StringValue | number;
           ~~~~~~~~~
    The expected type comes from property 'expiresIn' which is declared here on type 'SignOptions'

src/utils/jwt.ts:40:14 - error TS2769: No overload matches this call.
  Overload 1 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: null, options?: SignOptions & { algorithm: "none"; }): string', gave the following error.
    Argument of type 'string' is not assignable to parameter of type 'null'.
  Overload 2 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: Buffer<ArrayBufferLike> | Secret | PrivateKeyInput | JsonWebKeyInput, options?: SignOptions): string', gave the following error.
    Type 'string' is not assignable to type 'number | StringValue'.
  Overload 3 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: Buffer<ArrayBufferLike> | Secret | PrivateKeyInput | JsonWebKeyInput, callback: SignCallback): void', gave the following error.
    Object literal may only specify known properties, and 'expiresIn' does not exist in type 'SignCallback'.

40   return jwt.sign(payload, JWT_REFRESH_SECRET, {
                ~~~~

  node_modules/@types/jsonwebtoken/index.d.ts:43:5
    43     expiresIn?: StringValue | number;
           ~~~~~~~~~
    The expected type comes from property 'expiresIn' which is declared here on type 'SignOptions'


Found 71 errors in 11 files.

Errors  Files
     8  src/controllers/labResultController.ts:45
     8  src/controllers/notificationController.ts:75
     3  src/controllers/patientController.ts:140
     3  src/routes/auth.ts:35
     3  src/routes/notifications.ts:51
     6  src/routes/patients.ts:22
     4  src/services/labResultService.ts:61
    15  src/services/notificationService.ts:50
     1  src/services/patientService.ts:10
    18  src/services/recurringAppointmentService.ts:79
     2  src/utils/jwt.ts:26
## Next Steps
1. **CRITICAL**: Update Prisma schema with missing fields
2. Run `npx prisma generate && npx prisma db push`
3. Implement missing service methods
4. Fix controller return types
5. Run `npx tsc --noEmit` to verify all errors resolved
