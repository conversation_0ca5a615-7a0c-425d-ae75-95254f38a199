const http = require('http');

console.log('🧪 Running frontend health check test...');

// Test if frontend is accessible
const options = {
  hostname: 'localhost',
  port: 5173,
  path: '/',
  method: 'GET',
  timeout: 5000
};

const req = http.request(options, (res) => {
  let data = '';
  
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('✅ Frontend response received');
    console.log('Status Code:', res.statusCode);
    console.log('Content-Type:', res.headers['content-type']);
    
    if (res.statusCode === 200) {
      console.log('✅ Frontend health check PASSED');
      console.log('✅ Frontend is accessible');
      
      // Check if it looks like HTML content
      if (data.includes('<html') || data.includes('<!DOCTYPE')) {
        console.log('✅ Frontend is serving HTML content');
      } else {
        console.log('⚠️ Frontend response doesn\'t look like HTML');
      }
      
      process.exit(0);
    } else {
      console.log('❌ Frontend health check FAILED - Invalid status code');
      console.log('Expected: 200, Got:', res.statusCode);
      process.exit(1);
    }
  });
});

req.on('error', (error) => {
  console.log('❌ Frontend health check FAILED - Connection error');
  console.log('Error:', error.message);
  console.log('Make sure the frontend is running on port 5173');
  console.log('You can start it with: cd frontend && npm run dev');
  process.exit(1);
});

req.on('timeout', () => {
  console.log('❌ Frontend health check FAILED - Request timeout');
  console.log('Frontend may be unresponsive');
  req.destroy();
  process.exit(1);
});

req.end();
