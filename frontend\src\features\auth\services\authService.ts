import axios from 'axios';
import type { LoginCredentials, AuthResponse, User } from '../types';

// Create a separate axios instance for auth to avoid circular dependencies
const API_BASE_URL = 'http://localhost:3002';
const authApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Token management
const TOKEN_KEYS = {
  ACCESS_TOKEN: 'accessToken',
  REFRESH_TOKEN: 'refreshToken',
  USER: 'user',
};

export const authService = {
  // Authentication methods
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    // Check if the email field contains a username instead
    const loginData: any = { password: credentials.password };
    
    // If email doesn't contain @ symbol, treat it as a username
    if (credentials.email.includes('@')) {
      loginData.email = credentials.email;
    } else {
      loginData.username = credentials.email;
    }
    
    console.log('Sending login data:', loginData);
    
    try {
      const response = await authApi.post('/api/auth/login', loginData);
      console.log('Login response:', response.data);
      const authData = response.data.data;
      
      // Store tokens and user data
      localStorage.setItem(TOKEN_KEYS.ACCESS_TOKEN, authData.accessToken);
      localStorage.setItem(TOKEN_KEYS.REFRESH_TOKEN, authData.refreshToken);
      localStorage.setItem(TOKEN_KEYS.USER, JSON.stringify(authData.user));
      
      return {
        accessToken: authData.accessToken,
        refreshToken: authData.refreshToken,
        user: authData.user
      };
    } catch (error: any) {
      console.error('Login error details:', error.response?.data || error.message);
      throw error;
    }
  },

  async logout(): Promise<void> {
    const refreshToken = localStorage.getItem(TOKEN_KEYS.REFRESH_TOKEN);

    try {
      if (refreshToken) {
        await authApi.post('/api/auth/logout', { refreshToken });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear local storage regardless of API call success
      this.clearTokens();
    }
  },

  async refreshToken(): Promise<AuthResponse | null> {
    const refreshToken = localStorage.getItem(TOKEN_KEYS.REFRESH_TOKEN);

    if (!refreshToken) {
      return null;
    }

    try {
      const response = await authApi.post('/api/auth/refresh', { refreshToken });
      const authData = response.data.data;

      // Update stored tokens
      localStorage.setItem(TOKEN_KEYS.ACCESS_TOKEN, authData.accessToken);
      localStorage.setItem(TOKEN_KEYS.REFRESH_TOKEN, authData.refreshToken);
      localStorage.setItem(TOKEN_KEYS.USER, JSON.stringify(authData.user));

      return {
        accessToken: authData.accessToken,
        refreshToken: authData.refreshToken,
        user: authData.user
      };
    } catch (error) {
      // If refresh fails, clear tokens
      this.clearTokens();
      return null;
    }
  },

  async getCurrentUser(): Promise<User | null> {
    const token = localStorage.getItem(TOKEN_KEYS.ACCESS_TOKEN);

    if (!token) {
      return null;
    }

    try {
      const response = await authApi.get('/api/auth/me', {
        headers: { Authorization: `Bearer ${token}` }
      });
      return response.data.data;
    } catch (error) {
      // If getting current user fails, try to refresh token
      const refreshedAuth = await this.refreshToken();
      return refreshedAuth?.user || null;
    }
  },

  // Token management
  getAccessToken(): string | null {
    return localStorage.getItem(TOKEN_KEYS.ACCESS_TOKEN);
  },

  getRefreshToken(): string | null {
    return localStorage.getItem(TOKEN_KEYS.REFRESH_TOKEN);
  },

  getStoredUser(): User | null {
    const userStr = localStorage.getItem(TOKEN_KEYS.USER);
    if (!userStr) return null;
    
    try {
      return JSON.parse(userStr);
    } catch {
      return null;
    }
  },

  clearTokens(): void {
    localStorage.removeItem(TOKEN_KEYS.ACCESS_TOKEN);
    localStorage.removeItem(TOKEN_KEYS.REFRESH_TOKEN);
    localStorage.removeItem(TOKEN_KEYS.USER);
  },

  isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 < Date.now();
    } catch {
      return true;
    }
  },

  // Utility methods
  isAuthenticated(): boolean {
    const token = this.getAccessToken();
    return token !== null && !this.isTokenExpired(token);
  },

  hasRole(requiredRole: string): boolean {
    const user = this.getStoredUser();
    if (!user) return false;
    
    const roleHierarchy = {
      'ADMIN': 3,
      'CLINICIAN': 2,
      'STAFF': 1,
    };
    
    const userLevel = roleHierarchy[user.role as keyof typeof roleHierarchy] || 0;
    const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;
    
    return userLevel >= requiredLevel;
  },
};
