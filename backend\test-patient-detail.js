const axios = require('axios');

async function testPatientDetail() {
  try {
    const patientId = '285cead6-a4ee-4a25-84f3-5afe1c1d9643';
    
    console.log('Testing patient detail API for ID:', patientId);

    const response = await axios.get(`http://localhost:3002/api/patients/${patientId}`, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('Success! Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('Error fetching patient detail:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Message:', error.message);
    }
  }
}

testPatientDetail();
