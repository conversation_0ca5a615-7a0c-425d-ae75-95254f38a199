"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const bcrypt_1 = __importDefault(require("bcrypt"));
const prisma = new client_1.PrismaClient();
async function main() {
    console.log('🌱 Starting database seeding...');
    // Create admin user
    const adminPassword = await bcrypt_1.default.hash('admin123!', 12);
    const admin = await prisma.user.upsert({
        where: { username: 'admin' },
        update: {},
        create: {
            username: 'admin',
            email: '<EMAIL>',
            password: adminPassword,
            role: 'ADMIN',
            firstName: 'System',
            lastName: 'Administrator',
            isActive: true,
        },
    });
    console.log('✅ Created admin user:', admin.username);
    // Create clinician user
    const clinicianPassword = await bcrypt_1.default.hash('clinician123!', 12);
    const clinician = await prisma.user.upsert({
        where: { username: 'dr.smith' },
        update: {},
        create: {
            username: 'dr.smith',
            email: '<EMAIL>',
            password: clinicianPassword,
            role: 'CLINICIAN',
            firstName: 'Dr. Sarah',
            lastName: 'Smith',
            isActive: true,
        },
    });
    console.log('✅ Created clinician user:', clinician.username);
    // Create staff user
    const staffPassword = await bcrypt_1.default.hash('staff123!', 12);
    const staff = await prisma.user.upsert({
        where: { username: 'nurse.johnson' },
        update: {},
        create: {
            username: 'nurse.johnson',
            email: '<EMAIL>',
            password: staffPassword,
            role: 'STAFF',
            firstName: 'Mary',
            lastName: 'Johnson',
            isActive: true,
        },
    });
    console.log('✅ Created staff user:', staff.username);
    // Generate patient ID helper function
    const generatePatientId = (index) => {
        const year = new Date().getFullYear();
        const paddedIndex = String(index).padStart(3, '0');
        return `P-${year}-${paddedIndex}`;
    };
    // Create sample patients
    const samplePatients = [
        {
            patientId: generatePatientId(1),
            firstName: 'John',
            lastName: 'Doe',
            dateOfBirth: new Date('1985-03-15'),
            gender: 'MALE',
            phone: '555-0101',
            email: '<EMAIL>',
            address: JSON.stringify({
                street: '123 Main St',
                city: 'Springfield',
                state: 'IL',
                zipCode: '62701',
                country: 'USA',
            }),
            occupation: 'Software Engineer',
            education: 'BACHELORS',
            maritalStatus: 'MARRIED',
            emergencyContact: JSON.stringify({
                name: 'Jane Doe',
                phone: '555-0102',
                relationship: 'Spouse',
                email: '<EMAIL>',
            }),
            insuranceInfo: JSON.stringify({
                provider: 'Blue Cross Blue Shield',
                policyNumber: 'BC123456789',
                groupNumber: 'GRP001',
                subscriberName: 'John Doe',
            }),
            medicalHistory: 'History of anxiety and depression. No major medical conditions.',
            allergies: 'Penicillin',
            currentMeds: 'Sertraline 50mg daily',
            notes: 'Patient is cooperative and engaged in treatment.',
            createdBy: clinician.id,
        },
        {
            patientId: generatePatientId(2),
            firstName: 'Sarah',
            lastName: 'Wilson',
            dateOfBirth: new Date('1992-07-22'),
            gender: 'FEMALE',
            phone: '555-0201',
            email: '<EMAIL>',
            address: JSON.stringify({
                street: '456 Oak Ave',
                city: 'Springfield',
                state: 'IL',
                zipCode: '62702',
                country: 'USA',
            }),
            occupation: 'Teacher',
            education: 'MASTERS',
            maritalStatus: 'SINGLE',
            emergencyContact: JSON.stringify({
                name: 'Robert Wilson',
                phone: '555-0202',
                relationship: 'Father',
            }),
            insuranceInfo: JSON.stringify({
                provider: 'Aetna',
                policyNumber: 'AET987654321',
                subscriberName: 'Sarah Wilson',
            }),
            medicalHistory: 'Bipolar disorder, well-controlled with medication.',
            allergies: 'None known',
            currentMeds: 'Lithium 300mg twice daily, Lamotrigine 100mg daily',
            notes: 'Regular follow-ups for mood stabilization.',
            createdBy: clinician.id,
        },
        {
            patientId: generatePatientId(3),
            firstName: 'Michael',
            lastName: 'Brown',
            dateOfBirth: new Date('1978-11-08'),
            gender: 'MALE',
            phone: '555-0301',
            address: JSON.stringify({
                street: '789 Pine St',
                city: 'Springfield',
                state: 'IL',
                zipCode: '62703',
                country: 'USA',
            }),
            occupation: 'Construction Worker',
            education: 'HIGH_SCHOOL',
            maritalStatus: 'DIVORCED',
            emergencyContact: JSON.stringify({
                name: 'Lisa Brown',
                phone: '555-0302',
                relationship: 'Ex-wife',
            }),
            medicalHistory: 'PTSD following workplace accident. History of substance abuse (alcohol).',
            allergies: 'Latex',
            currentMeds: 'Prazosin 2mg at bedtime, Naltrexone 50mg daily',
            notes: 'Trauma-focused therapy ongoing. 6 months sober.',
            createdBy: clinician.id,
        },
    ];
    for (const patientData of samplePatients) {
        const patient = await prisma.patient.create({
            data: patientData,
        });
        console.log('✅ Created patient:', patient.patientId);
    }
    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📋 Default Login Credentials:');
    console.log('Admin: admin / admin123!');
    console.log('Clinician: dr.smith / clinician123!');
    console.log('Staff: nurse.johnson / staff123!');
}
main()
    .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
})
    .finally(async () => {
    await prisma.$disconnect();
});
