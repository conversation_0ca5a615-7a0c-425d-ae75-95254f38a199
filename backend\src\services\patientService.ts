import { PrismaClient } from '@prisma/client';
import { 
  CreatePatientData, 
  UpdatePatientData, 
  PatientQuery, 
  ApiResponse, 
  PaginatedResponse,
  AuditLogData 
} from '@/types';
import { NotFoundError, ValidationError, AuthorizationError, ServerError } from '@/utils/errors';

const prisma = new PrismaClient();

/**
 * Patient service handling all patient-related operations
 * Includes CRUD operations, search, filtering, and audit logging
 */
export class PatientService {
  /**
   * Generate unique patient ID
   */
  private static generatePatientId(): string {
    const year = new Date().getFullYear();
    const timestamp = Date.now().toString().slice(-6);
    return `P-${year}-${timestamp}`;
  }

  /**
   * Create a new patient
   */
  static async createPatient(
    data: CreatePatientData,
    createdBy: string,
    auditData?: { ipAddress?: string; userAgent?: string }
  ): Promise<ApiResponse<{ patient: any }>> {
    // Generate unique patient ID
    let patientId = this.generatePatientId();
    
    // Ensure uniqueness
    let existingPatient = await prisma.patient.findUnique({
      where: { patientId },
    });
    
    while (existingPatient) {
      patientId = this.generatePatientId();
      existingPatient = await prisma.patient.findUnique({
        where: { patientId },
      });
    }

    // Validate required fields
    if (!data.firstName || !data.lastName || !data.dateOfBirth || !data.gender) {
      throw new ValidationError('First name, last name, date of birth, and gender are required');
    }

    // Validate date of birth
    const dob = new Date(data.dateOfBirth);
    if (isNaN(dob.getTime()) || dob > new Date()) {
      throw new ValidationError('Invalid date of birth');
    }

    // Create patient
    const patient = await prisma.patient.create({
      data: {
        patientId,
        firstName: data.firstName.trim(),
        lastName: data.lastName.trim(),
        dateOfBirth: dob,
        gender: data.gender,
        phone: data.phone?.trim() || null,
        email: data.email?.trim() || null,
        address: data.address ? JSON.stringify(data.address) : null,
        occupation: data.occupation?.trim() || null,
        education: data.education || null,
        maritalStatus: data.maritalStatus || null,
        emergencyContact: data.emergencyContact ? JSON.stringify(data.emergencyContact) : null,
        insuranceInfo: data.insuranceInfo ? JSON.stringify(data.insuranceInfo) : null,
        medicalHistory: data.medicalHistory?.trim() || null,
        allergies: data.allergies?.trim() || null,
        currentMeds: data.currentMeds?.trim() || null,
        notes: data.notes?.trim() || null,
        createdBy,
      },
      include: {
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            username: true,
          },
        },
      },
    });

    // Create audit log
    await this.createAuditLog({
      userId: createdBy,
      action: 'CREATE',
      entityType: 'PATIENT',
      entityId: patient.id,
      patientId: patient.id,
      newValues: {
        patientId: patient.patientId,
        name: `${patient.firstName} ${patient.lastName}`,
        dateOfBirth: patient.dateOfBirth,
        gender: patient.gender,
      },
      ipAddress: auditData?.ipAddress,
      userAgent: auditData?.userAgent,
    });

    return {
      success: true,
      data: { patient },
      message: 'Patient created successfully',
    };
  }

  /**
   * Get patients with pagination, search, and filtering
   */
  static async getPatients(
    query: PatientQuery,
    userId: string,
    userRole: string
  ): Promise<PaginatedResponse<any>> {
    const page = parseInt(query.page || '1', 10);
    const limit = Math.min(parseInt(query.limit || '10', 10), 100); // Max 100 per page
    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      isDeleted: false,
    };

    // Role-based filtering: non-admins can only see their own patients
    if (userRole !== 'ADMIN') {
      where.createdBy = userId;
    }

    // Search functionality
    if (query.search) {
      const searchTerm = query.search.trim();
      where.OR = [
        { firstName: { contains: searchTerm, mode: 'insensitive' } },
        { lastName: { contains: searchTerm, mode: 'insensitive' } },
        { patientId: { contains: searchTerm, mode: 'insensitive' } },
        { email: { contains: searchTerm, mode: 'insensitive' } },
        { phone: { contains: searchTerm, mode: 'insensitive' } },
      ];
    }

    // Gender filter
    if (query.gender) {
      where.gender = query.gender;
    }

    // Active status filter
    if (query.isActive !== undefined) {
      where.isActive = query.isActive === 'true';
    }

    // Sorting
    const orderBy: any = {};
    if (query.sortBy) {
      const direction = query.sortOrder === 'desc' ? 'desc' : 'asc';
      orderBy[query.sortBy] = direction;
    } else {
      orderBy.createdAt = 'desc'; // Default sort
    }

    // Execute queries
    const [patients, total] = await Promise.all([
      prisma.patient.findMany({
        where,
        skip,
        take: limit,
        orderBy,
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              username: true,
            },
          },
          _count: {
            select: {
              labResults: true,
              appointments: true,
            },
          },
        },
      }),
      prisma.patient.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      success: true,
      data: patients,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }

  /**
   * Get patient by ID
   */
  static async getPatientById(
    id: string,
    userId: string,
    userRole: string
  ): Promise<ApiResponse<{ patient: any }>> {
    const patient = await prisma.patient.findFirst({
      where: {
        id,
        isDeleted: false,
        ...(userRole !== 'ADMIN' && { createdBy: userId }),
      },
      include: {
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            username: true,
          },
        },
        labResults: {
          where: { isDeleted: false },
          orderBy: { testDate: 'desc' },
          take: 5, // Latest 5 lab results
        },
        appointments: {
          where: { isDeleted: false },
          orderBy: { date: 'desc' },
          take: 5, // Latest 5 appointments
        },
        _count: {
          select: {
            labResults: { where: { isDeleted: false } },
            appointments: { where: { isDeleted: false } },
          },
        },
      },
    });

    if (!patient) {
      throw new NotFoundError('Patient not found');
    }

    // Create audit log for viewing
    await this.createAuditLog({
      userId,
      action: 'VIEW',
      entityType: 'PATIENT',
      entityId: patient.id,
      patientId: patient.id,
    });

    return {
      success: true,
      data: { patient },
    };
  }

  /**
   * Update patient
   */
  static async updatePatient(
    id: string,
    data: UpdatePatientData,
    userId: string,
    userRole: string,
    auditData?: { ipAddress?: string; userAgent?: string }
  ): Promise<ApiResponse<{ patient: any }>> {
    // Check if patient exists and user has access
    const existingPatient = await prisma.patient.findFirst({
      where: {
        id,
        isDeleted: false,
        ...(userRole !== 'ADMIN' && { createdBy: userId }),
      },
    });

    if (!existingPatient) {
      throw new NotFoundError('Patient not found');
    }

    // Validate date of birth if provided
    if (data.dateOfBirth) {
      const dob = new Date(data.dateOfBirth);
      if (isNaN(dob.getTime()) || dob > new Date()) {
        throw new ValidationError('Invalid date of birth');
      }
      data.dateOfBirth = dob.toISOString();
    }

    // Prepare update data
    const updateData: any = {};
    
    // Only update provided fields
    if (data.firstName !== undefined) updateData.firstName = data.firstName.trim();
    if (data.lastName !== undefined) updateData.lastName = data.lastName.trim();
    if (data.dateOfBirth !== undefined) updateData.dateOfBirth = new Date(data.dateOfBirth);
    if (data.gender !== undefined) updateData.gender = data.gender;
    if (data.phone !== undefined) updateData.phone = data.phone?.trim() || null;
    if (data.email !== undefined) updateData.email = data.email?.trim() || null;
    if (data.address !== undefined) updateData.address = data.address;
    if (data.occupation !== undefined) updateData.occupation = data.occupation?.trim() || null;
    if (data.education !== undefined) updateData.education = data.education;
    if (data.maritalStatus !== undefined) updateData.maritalStatus = data.maritalStatus;
    if (data.emergencyContact !== undefined) updateData.emergencyContact = data.emergencyContact;
    if (data.insuranceInfo !== undefined) updateData.insuranceInfo = data.insuranceInfo;
    if (data.medicalHistory !== undefined) updateData.medicalHistory = data.medicalHistory?.trim() || null;
    if (data.allergies !== undefined) updateData.allergies = data.allergies?.trim() || null;
    if (data.currentMeds !== undefined) updateData.currentMeds = data.currentMeds?.trim() || null;
    if (data.notes !== undefined) updateData.notes = data.notes?.trim() || null;
    if (data.isActive !== undefined) updateData.isActive = data.isActive;

    // Update patient
    const patient = await prisma.patient.update({
      where: { id },
      data: updateData,
      include: {
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            username: true,
          },
        },
      },
    });

    // Create audit log
    await this.createAuditLog({
      userId,
      action: 'UPDATE',
      entityType: 'PATIENT',
      entityId: patient.id,
      patientId: patient.id,
      oldValues: {
        firstName: existingPatient.firstName,
        lastName: existingPatient.lastName,
        // Add other relevant old values
      },
      newValues: updateData,
      ipAddress: auditData?.ipAddress,
      userAgent: auditData?.userAgent,
    });

    return {
      success: true,
      data: { patient },
      message: 'Patient updated successfully',
    };
  }

  /**
   * Soft delete patient
   */
  static async deletePatient(
    id: string,
    userId: string,
    userRole: string,
    auditData?: { ipAddress?: string; userAgent?: string }
  ): Promise<ApiResponse<null>> {
    // Check if patient exists and user has access
    const patient = await prisma.patient.findFirst({
      where: {
        id,
        isDeleted: false,
        ...(userRole !== 'ADMIN' && { createdBy: userId }),
      },
    });

    if (!patient) {
      throw new NotFoundError('Patient not found');
    }

    // Soft delete patient
    await prisma.patient.update({
      where: { id },
      data: {
        isDeleted: true,
        deletedAt: new Date(),
      },
    });

    // Create audit log
    await this.createAuditLog({
      userId,
      action: 'DELETE',
      entityType: 'PATIENT',
      entityId: id,
      patientId: id,
      oldValues: {
        patientId: patient.patientId,
        name: `${patient.firstName} ${patient.lastName}`,
      },
      ipAddress: auditData?.ipAddress,
      userAgent: auditData?.userAgent,
    });

    return {
      success: true,
      data: null,
      message: 'Patient deleted successfully',
    };
  }

  /**
   * Create audit log entry
   */
  private static async createAuditLog(data: AuditLogData): Promise<void> {
    try {
      await prisma.auditLog.create({
        data: {
          userId: data.userId,
          action: data.action,
          entityType: data.entityType,
          entityId: data.entityId,
          oldValues: data.oldValues ? JSON.stringify(data.oldValues) : null,
          newValues: data.newValues ? JSON.stringify(data.newValues) : null,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          patientId: data.patientId,
          labResultId: data.labResultId,
          appointmentId: data.appointmentId,
        },
      });
    } catch (error) {
      console.error('Failed to create audit log:', error);
    }
  }

  /**
   * Get patient statistics
   */
  static async getPatientStats(userId: string, userRole: string): Promise<ApiResponse<any>> {
    try {
      const whereClause = userRole === 'ADMIN' ? { isDeleted: false } : { isDeleted: false, createdBy: userId };

      const [totalPatients, activePatients, inactivePatients, recentPatients] = await Promise.all([
        prisma.patient.count({ where: whereClause }),
        prisma.patient.count({ where: { ...whereClause, isActive: true } }),
        prisma.patient.count({ where: { ...whereClause, isActive: false } }),
        prisma.patient.count({
          where: {
            ...whereClause,
            createdAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
            },
          },
        }),
      ]);

      return {
        success: true,
        data: {
          totalPatients,
          activePatients,
          inactivePatients,
          recentPatients,
        },
      };
    } catch (error) {
      console.error('Error getting patient stats:', error);
      throw new ServerError('Failed to retrieve patient statistics');
    }
  }

  /**
   * Search patients by term
   */
  static async searchPatients(
    term: string,
    userId: string,
    userRole: string
  ): Promise<ApiResponse<any>> {
    if (!term || term.trim().length < 2) {
      return {
        success: true,
        data: [],
        message: 'Search term must be at least 2 characters long',
      };
    }

    const searchTerm = term.trim().toLowerCase();

    const whereClause: any = {
      isDeleted: false,
      OR: [
        {
          firstName: {
            contains: searchTerm,
            mode: 'insensitive',
          },
        },
        {
          lastName: {
            contains: searchTerm,
            mode: 'insensitive',
          },
        },
        {
          patientId: {
            contains: searchTerm,
            mode: 'insensitive',
          },
        },
        {
          email: {
            contains: searchTerm,
            mode: 'insensitive',
          },
        },
        {
          phone: {
            contains: searchTerm,
            mode: 'insensitive',
          },
        },
      ],
    };

    // Non-admin users can only search their own patients
    if (userRole !== 'ADMIN') {
      whereClause.createdBy = userId;
    }

    try {
      const patients = await prisma.patient.findMany({
        where: whereClause,
        select: {
          id: true,
          patientId: true,
          firstName: true,
          lastName: true,
          dateOfBirth: true,
          gender: true,
          phone: true,
          email: true,
          isActive: true,
          createdAt: true,
          creator: {
            select: {
              firstName: true,
              lastName: true,
              username: true,
            },
          },
        },
        orderBy: [
          { lastName: 'asc' },
          { firstName: 'asc' },
        ],
        take: 50, // Limit search results
      });

      return {
        success: true,
        data: patients,
        message: `Found ${patients.length} patients matching "${term}"`,
      };
    } catch (error) {
      console.error('Error searching patients:', error);
      throw new ServerError('Failed to search patients');
    }
  }
}
