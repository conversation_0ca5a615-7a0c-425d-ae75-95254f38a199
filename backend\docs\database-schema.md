# Database Schema Documentation

## Overview

The Psychiatry Patient Management System uses a PostgreSQL database with Prisma ORM for type-safe database operations. The schema is designed with clinical safety, HIPAA compliance, and data integrity as primary concerns.

## Core Entities

### 1. User
**Purpose**: System users (clinicians, administrators, staff)

**Key Fields**:
- `id`: UUID primary key
- `username`: Unique username for login
- `email`: User email address
- `password`: Hashed password (bcrypt)
- `role`: ADMIN | CLINICIAN | STAFF
- `firstName`, `lastName`: User's name
- `isActive`: Account status
- `lastLogin`: Last login timestamp

**Security Features**:
- Passwords are hashed using bcrypt
- Role-based access control
- Account activation/deactivation
- Login tracking

### 2. Patient
**Purpose**: Patient demographic and contact information

**Key Fields**:
- `id`: UUID primary key
- `patientId`: Human-readable ID (e.g., P-2024-001)
- `firstName`, `lastName`: Patient name
- `dateOfBirth`: Patient DOB
- `gender`: MALE | FEMALE | NON_BINARY | PREFER_NOT_TO_SAY | OTHER
- `phone`, `email`: Contact information
- `address`: JSON object with address details
- `occupation`: Patient's occupation
- `education`: Education level enum
- `maritalStatus`: Marital status enum
- `emergencyContact`: JSON object with emergency contact
- `insuranceInfo`: JSON object with insurance details
- `medicalHistory`: JSON object with medical history
- `isActive`: Patient status
- `isDeleted`: Soft delete flag

**Clinical Safety Features**:
- Soft deletes preserve data integrity
- Audit trail for all changes
- Comprehensive demographic data for clinical context
- Emergency contact information readily available

### 3. LabResult
**Purpose**: Laboratory test results and clinical data

**Key Fields**:
- `id`: UUID primary key
- `patientId`: Foreign key to Patient
- `testType`: Enum of common lab test types
- `testDate`: When the test was performed
- `orderedBy`: Ordering physician name
- `labName`: Laboratory facility name
- `results`: JSON object with structured test values
- `normalRanges`: JSON object with reference ranges
- `flags`: JSON object with abnormal value alerts
- `status`: PENDING | IN_PROGRESS | COMPLETED | CANCELLED | AMENDED
- `notes`: Additional clinical notes
- `isDeleted`: Soft delete flag

**Clinical Features**:
- Structured data format for consistent interpretation
- Normal range tracking for abnormal value detection
- Status tracking for workflow management
- Amendment capability for corrected results

### 4. Appointment
**Purpose**: Patient appointments and scheduling

**Key Fields**:
- `id`: UUID primary key
- `patientId`: Foreign key to Patient
- `providerId`: Foreign key to User (provider)
- `date`: Appointment date and time
- `duration`: Duration in minutes
- `type`: Appointment type enum (consultation, therapy, etc.)
- `status`: SCHEDULED | CONFIRMED | IN_PROGRESS | COMPLETED | CANCELLED | NO_SHOW
- `notes`: Appointment notes
- `recurringAppointmentId`: Link to recurring series (optional)

**Scheduling Features**:
- Flexible appointment types for different clinical needs
- Status tracking for appointment workflow
- Integration with recurring appointment system
- Provider assignment and tracking

### 5. RecurringAppointment
**Purpose**: Recurring appointment patterns and series management

**Key Fields**:
- `id`: UUID primary key
- `patientId`: Foreign key to Patient
- `providerId`: Foreign key to User
- `startDate`, `endDate`: Series date range
- `frequency`: DAILY | WEEKLY | BIWEEKLY | MONTHLY | YEARLY
- `interval`: Frequency multiplier (e.g., every 2 weeks)
- `dayOfWeek`: Specific day for weekly patterns (0-6)
- `dayOfMonth`: Specific day for monthly patterns (1-31)
- `timeSlot`: Preferred time slot
- `maxOccurrences`: Maximum number of appointments
- `isActive`: Series status
- `isDeleted`: Soft delete flag

**Advanced Scheduling**:
- Complex recurrence patterns (specific days, intervals)
- Automatic appointment generation
- Series management and cancellation
- Clinical workflow optimization

### 6. Notification
**Purpose**: Clinical alerts and patient communication

**Key Fields**:
- `id`: UUID primary key
- `patientId`: Foreign key to Patient (optional)
- `userId`: Target user for notification
- `type`: LAB_RESULT | APPOINTMENT | MEDICATION | ALERT | REMINDER | SYSTEM
- `title`: Notification title
- `message`: Notification content
- `priority`: LOW | MEDIUM | HIGH | URGENT
- `isRead`: Read status
- `readAt`: Read timestamp
- `data`: JSON object with additional context

**Clinical Communication**:
- Priority-based alert system
- Multiple notification types for different clinical scenarios
- Read tracking for accountability
- Contextual data for actionable notifications

### 7. Assessment
**Purpose**: Clinical assessments and evaluations

**Key Fields**:
- `id`: UUID primary key
- `patientId`: Foreign key to Patient
- `assessmentType`: Type of clinical assessment
- `assessmentDate`: When assessment was conducted
- `assessor`: Conducting clinician
- `responses`: JSON object with assessment responses
- `scores`: JSON object with calculated scores
- `interpretation`: Clinical interpretation
- `recommendations`: Clinical recommendations
- `isCompleted`: Assessment completion status

**Clinical Assessment Features**:
- Structured assessment data
- Score calculation and tracking
- Clinical interpretation documentation
- Treatment recommendation tracking

### 8. AuditLog
**Purpose**: Complete audit trail for compliance and security

**Key Fields**:
- `id`: UUID primary key
- `entityType`: Type of entity modified (PATIENT, LAB_RESULT, etc.)
- `entityId`: ID of the modified entity
- `action`: CREATE | UPDATE | DELETE | VIEW
- `userId`: User who performed the action
- `changes`: JSON object with before/after values
- `ipAddress`: Source IP address
- `userAgent`: User agent string
- `timestamp`: When the action occurred

**Compliance Features**:
- Complete audit trail for HIPAA compliance
- Change tracking with before/after values
- User attribution and source tracking
- Immutable audit records

## Relationships

### One-to-Many Relationships
- **User → Appointment**: One provider can have many appointments
- **Patient → LabResult**: One patient can have many lab results
- **Patient → Appointment**: One patient can have many appointments
- **Patient → Notification**: One patient can have many notifications
- **Patient → Assessment**: One patient can have many assessments
- **RecurringAppointment → Appointment**: One series generates many appointments

### Foreign Key Constraints
All foreign key relationships are enforced at the database level to ensure referential integrity.

## Indexes and Performance

### Primary Indexes
- All tables have UUID primary keys with automatic indexing
- Unique constraints on critical fields (username, email, patientId)

### Query Optimization Indexes
- `Patient.patientId`: For quick patient lookup
- `Patient.email`: For patient search
- `LabResult.patientId`: For patient lab history
- `Appointment.patientId`: For patient appointment history
- `Appointment.providerId`: For provider schedules
- `Appointment.date`: For date-based queries
- `AuditLog.entityType, entityId`: For audit trail queries
- `Notification.userId, isRead`: For user notifications

## Data Integrity Features

### Soft Deletes
- All clinical data uses soft deletes (`isDeleted` flag)
- Preserves data integrity and audit trail
- Allows for data recovery if needed

### Audit Trail
- Every data modification is logged in AuditLog
- Includes user attribution, timestamps, and change details
- Immutable audit records for compliance

### Validation
- Database-level constraints for data integrity
- Application-level validation for business rules
- Type safety through Prisma schema

### Backup and Recovery
- Regular automated backups
- Point-in-time recovery capability
- Data encryption at rest and in transit

## HIPAA Compliance Features

### Access Control
- Role-based access control at application level
- User authentication and authorization
- Session management and timeout

### Audit Requirements
- Complete audit trail of all data access and modifications
- User attribution for all actions
- Timestamp tracking for all activities

### Data Protection
- Encryption of sensitive data
- Secure data transmission
- Access logging and monitoring

### Data Retention
- Configurable data retention policies
- Secure data disposal procedures
- Backup and archival management

## Migration Strategy

### Schema Versioning
- Prisma migrations for schema changes
- Version control for all schema modifications
- Rollback capability for failed migrations

### Data Migration
- Safe migration procedures for production data
- Data validation after migrations
- Backup before major schema changes

### Testing
- Migration testing in staging environment
- Data integrity verification
- Performance impact assessment
