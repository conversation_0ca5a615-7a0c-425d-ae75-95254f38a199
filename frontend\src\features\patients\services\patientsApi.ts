import { api, handleApiError } from '../../../lib/api';
import type { Patient, PatientFilters } from '../types';

export const patientsApi = {
  async getAll(filters?: PatientFilters): Promise<Patient[]> {
    try {
      const params = new URLSearchParams();
      if (filters?.search) params.append('search', filters.search);
      if (filters?.gender) params.append('gender', filters.gender);
      if (filters?.isActive !== undefined) params.append('isActive', filters.isActive.toString());

      const response = await api.get(`/api/patients?${params.toString()}`);
      return response.data.data || [];
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async getById(id: string): Promise<Patient> {
    try {
      const response = await api.get(`/api/patients/${id}`);
      return response.data.data.patient;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async create(patientData: Omit<Patient, 'id' | 'createdAt' | 'updatedAt'>): Promise<Patient> {
    try {
      const response = await api.post('/api/patients', patientData);
      return response.data.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async update(id: string, updates: Partial<Patient>): Promise<Patient> {
    try {
      const response = await api.put(`/api/patients/${id}`, updates);
      return response.data.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async delete(id: string): Promise<void> {
    try {
      await api.delete(`/api/patients/${id}`);
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async exportCsv(): Promise<Blob> {
    try {
      const response = await api.get('/api/export/patients', {
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },
};
