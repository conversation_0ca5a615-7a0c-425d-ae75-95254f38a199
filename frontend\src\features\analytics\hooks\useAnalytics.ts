import { useQuery } from '@tanstack/react-query';
import { analyticsApi } from '../services/analyticsApi';
import { queryKeys } from '../../../lib/queryKeys';
import type { DateRange } from '../types';

/**
 * Hook to fetch dashboard analytics.
 */
export const useDashboardAnalytics = () => {
  const { 
    data, 
    isLoading, 
    error 
  } = useQuery({
    queryKey: queryKeys.analytics.dashboard(),
    queryFn: () => analyticsApi.getDashboardAnalytics(),
  });

  return {
    analytics: data,
    loading: isLoading,
    error: error?.message || null,
  };
};

/**
 * Hook to fetch patient analytics for a given date range.
 */
export const usePatientAnalytics = (dateRange: DateRange) => {
  const { 
    data, 
    isLoading, 
    error 
  } = useQuery({
    queryKey: queryKeys.analytics.patientStats(dateRange),
    queryFn: () => analyticsApi.getPatientAnalytics(dateRange),
    enabled: !!dateRange.from && !!dateRange.to, // Only run query when dates are set
  });

  return {
    analytics: data,
    loading: isLoading,
    error: error?.message || null,
  };
};

/**
 * Hook to fetch appointment analytics for a given date range.
 */
export const useAppointmentAnalytics = (dateRange: DateRange) => {
  const { 
    data, 
    isLoading, 
    error 
  } = useQuery({
    queryKey: queryKeys.analytics.appointmentStats(dateRange),
    queryFn: () => analyticsApi.getAppointmentAnalytics(dateRange),
    enabled: !!dateRange.from && !!dateRange.to,
  });

  return {
    analytics: data,
    loading: isLoading,
    error: error?.message || null,
  };
};

/**
 * Hook to fetch lab result analytics for a given date range.
 */
export const useLabResultAnalytics = (dateRange: DateRange) => {
  const { 
    data, 
    isLoading, 
    error 
  } = useQuery({
    queryKey: queryKeys.analytics.labResultStats(dateRange),
    queryFn: () => analyticsApi.getLabResultAnalytics(dateRange),
    enabled: !!dateRange.from && !!dateRange.to,
  });

  return {
    analytics: data,
    loading: isLoading,
    error: error?.message || null,
  };
};

export const useSystemAnalytics = (dateRange: DateRange) => {
  const {
    data: analytics,
    isLoading: loading,
    error,
    refetch: fetchAnalytics,
  } = useQuery({
    queryKey: ['analytics', 'system', dateRange],
    queryFn: () => analyticsApi.getSystemAnalytics(dateRange),
    staleTime: 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: false,
    enabled: !!dateRange.from && !!dateRange.to,
  });

  return {
    analytics,
    loading,
    error: error?.message || null,
    fetchAnalytics,
  };
};
