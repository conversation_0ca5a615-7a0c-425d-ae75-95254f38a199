const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkDatabase() {
  try {
    console.log('🔍 Checking database state...\n');
    
    // Check users
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        role: true,
        firstName: true,
        lastName: true,
        isActive: true
      }
    });
    
    console.log('👥 Users in database:');
    if (users.length === 0) {
      console.log('   No users found');
    } else {
      users.forEach(user => {
        console.log(`   - ${user.username} (${user.role}) - ${user.firstName} ${user.lastName} - Active: ${user.isActive}`);
      });
    }
    
    // Check patients
    const patients = await prisma.patient.findMany({
      select: {
        id: true,
        patientId: true,
        firstName: true,
        lastName: true,
        isActive: true,
        isDeleted: true
      },
      where: {
        isDeleted: false
      }
    });
    
    console.log('\n🏥 Patients in database:');
    if (patients.length === 0) {
      console.log('   No patients found');
    } else {
      patients.forEach(patient => {
        console.log(`   - ${patient.patientId} - ${patient.firstName} ${patient.lastName} - Active: ${patient.isActive}`);
      });
    }
    
    // Check for specific patients the frontend needs
    const frontendPatients = ['P12345', 'P12346', 'P12347', 'P12348', 'P12349'];
    console.log('\n🔍 Checking for frontend-expected patients:');
    
    for (const patientId of frontendPatients) {
      const patient = await prisma.patient.findFirst({
        where: {
          OR: [
            { patientId: patientId },
            { id: patientId }
          ],
          isDeleted: false
        }
      });
      
      if (patient) {
        console.log(`   ✅ ${patientId} - Found (${patient.firstName} ${patient.lastName})`);
      } else {
        console.log(`   ❌ ${patientId} - Missing`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error checking database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabase();
