import { PatientService } from '@/services/patientService';
import { LabResultService } from '@/services/labResultService';
import { AppointmentService } from '@/services/appointmentService';
import { NotificationService } from '@/services/notificationService';
import { createTestUser, createTestPatient, prisma } from '../setup';

describe('Clinical Workflow Integration Tests', () => {
  let testUser: any;
  let testPatient: any;

  beforeEach(async () => {
    testUser = await createTestUser('CLINICIAN');
  });

  describe('Complete Patient Journey', () => {
    test('should handle complete patient workflow: creation → lab results → notifications', async () => {
      // Step 1: Create a new patient
      const patientData = {
        firstName: 'Alice',
        lastName: 'Johnson',
        dateOfBirth: new Date('1985-03-15'),
        gender: 'FEMALE',
        phone: '+**********',
        email: '<EMAIL>',
        address: JSON.stringify({
          street: '456 Health Ave',
          city: 'Medical City',
          state: 'MC',
          zipCode: '54321',
          country: 'USA'
        }),
        occupation: 'Teacher',
        education: 'MASTERS',
        maritalStatus: 'MARRIED',
        emergencyContact: JSON.stringify({
          name: 'Bob Johnson',
          relationship: 'Spouse',
          phone: '+**********'
        }),
        insuranceInfo: JSON.stringify({
          provider: 'Health Plus',
          policyNumber: 'HP789012',
          groupNumber: 'GRP456'
        }),
        medicalHistory: JSON.stringify({
          allergies: ['Shellfish'],
          medications: ['Multivitamin'],
          conditions: ['Hypertension']
        })
      };

      const patientResult = await PatientService.createPatient(
        patientData,
        testUser.id,
        { ipAddress: '127.0.0.1', userAgent: 'test-agent' }
      );

      expect(patientResult.success).toBe(true);
      expect(patientResult.data.patient.firstName).toBe('Alice');
      expect(patientResult.data.patient.lastName).toBe('Johnson');
      
      testPatient = patientResult.data.patient;

      // Step 2: Create lab results for the patient
      const labResultData = {
        patientId: testPatient.id,
        testType: 'LIPID_PANEL',
        testDate: new Date(),
        orderedBy: 'Dr. Smith',
        labName: 'Central Lab',
        results: JSON.stringify({
          'Total Cholesterol': { value: 220, unit: 'mg/dL', normalRange: '<200', isNormal: false },
          'LDL Cholesterol': { value: 140, unit: 'mg/dL', normalRange: '<100', isNormal: false },
          'HDL Cholesterol': { value: 45, unit: 'mg/dL', normalRange: '>40', isNormal: true },
          'Triglycerides': { value: 180, unit: 'mg/dL', normalRange: '<150', isNormal: false }
        }),
        normalRanges: JSON.stringify({
          'Total Cholesterol': '<200 mg/dL',
          'LDL Cholesterol': '<100 mg/dL',
          'HDL Cholesterol': '>40 mg/dL',
          'Triglycerides': '<150 mg/dL'
        }),
        status: 'COMPLETED',
        notes: 'Elevated cholesterol levels - recommend dietary changes and follow-up'
      };

      const labResult = await LabResultService.createLabResult(
        labResultData,
        testUser.id,
        { ipAddress: '127.0.0.1', userAgent: 'test-agent' }
      );

      expect(labResult.success).toBe(true);
      expect(labResult.data.labResult.testType).toBe('LIPID_PANEL');
      expect(labResult.data.labResult.status).toBe('COMPLETED');

      // Step 3: Create appointment for follow-up
      const appointmentData = {
        patientId: testPatient.id,
        providerId: testUser.id,
        date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Next week
        duration: 30,
        type: 'FOLLOW_UP' as const,
        status: 'SCHEDULED' as const,
        notes: 'Follow-up for elevated cholesterol levels'
      };

      const appointment = await AppointmentService.createAppointment(
        appointmentData,
        testUser.id,
        { ipAddress: '127.0.0.1', userAgent: 'test-agent' }
      );

      expect(appointment.success).toBe(true);
      expect(appointment.data.appointment.type).toBe('FOLLOW_UP');

      // Step 4: Send lab result notification
      const notification = await NotificationService.sendLabResultNotification(
        labResult.data.labResult.id,
        testUser.id
      );

      expect(notification.success).toBe(true);
      expect(notification.data.notification.type).toBe('LAB_RESULT');

      // Step 5: Verify all data is properly linked
      const patientWithRelations = await prisma.patient.findUnique({
        where: { id: testPatient.id },
        include: {
          labResults: true,
          appointments: true,
          notifications: true
        }
      });

      expect(patientWithRelations).toBeTruthy();
      expect(patientWithRelations!.labResults).toHaveLength(1);
      expect(patientWithRelations!.appointments).toHaveLength(1);
      expect(patientWithRelations!.notifications).toHaveLength(1);
    });

    test('should handle patient search and retrieval', async () => {
      // Create test patient
      testPatient = await createTestPatient(testUser.id);

      // Test search by name
      const searchResult = await PatientService.searchPatients(
        'John',
        testUser.id,
        testUser.role
      );

      expect(searchResult.success).toBe(true);
      expect(searchResult.data.length).toBeGreaterThan(0);
      expect(searchResult.data[0].firstName).toBe('John');

      // Test search by patient ID
      const searchByIdResult = await PatientService.searchPatients(
        testPatient.patientId,
        testUser.id,
        testUser.role
      );

      expect(searchByIdResult.success).toBe(true);
      expect(searchByIdResult.data.length).toBe(1);
      expect(searchByIdResult.data[0].id).toBe(testPatient.id);
    });
  });

  describe('Lab Result Management', () => {
    beforeEach(async () => {
      testPatient = await createTestPatient(testUser.id);
    });

    test('should create and update lab results', async () => {
      // Create initial lab result
      const labResultData = {
        patientId: testPatient.id,
        testType: 'CBC',
        testDate: new Date(),
        orderedBy: 'Dr. Test',
        labName: 'Test Lab',
        results: JSON.stringify({
          WBC: { value: 7.5, unit: 'K/uL', normalRange: '4.0-11.0', isNormal: true }
        }),
        normalRanges: JSON.stringify({
          WBC: '4.0-11.0 K/uL'
        }),
        status: 'PENDING',
        notes: 'Initial CBC test'
      };

      const createResult = await LabResultService.createLabResult(
        labResultData,
        testUser.id
      );

      expect(createResult.success).toBe(true);
      const labResultId = createResult.data.labResult.id;

      // Update lab result with completed results
      const updateData = {
        status: 'COMPLETED',
        results: JSON.stringify({
          WBC: { value: 7.5, unit: 'K/uL', normalRange: '4.0-11.0', isNormal: true },
          RBC: { value: 4.5, unit: 'M/uL', normalRange: '4.2-5.4', isNormal: true },
          Hemoglobin: { value: 14.2, unit: 'g/dL', normalRange: '12.0-16.0', isNormal: true }
        }),
        notes: 'Complete CBC results - all values normal'
      };

      const updateResult = await LabResultService.updateLabResult(
        labResultId,
        updateData,
        testUser.id
      );

      expect(updateResult.success).toBe(true);
      expect(updateResult.data.labResult.status).toBe('COMPLETED');
    });
  });
});
