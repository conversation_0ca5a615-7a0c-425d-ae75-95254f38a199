import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type { Patient, PatientFilters } from '../types';
import { patientsApi } from '../services/patientsApi';
import { queryKeys, getInvalidationKeys } from '../../../lib/queryKeys';

export const usePatients = (filters?: PatientFilters) => {
  const queryClient = useQueryClient();

  // Query for fetching patients
  const {
    data: patients = [],
    isLoading: loading,
    error,
    refetch: fetchPatients,
  } = useQuery({
    queryKey: queryKeys.patients.list(filters),
    queryFn: () => patientsApi.getAll(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Mutation for creating patients
  const createPatientMutation = useMutation({
    mutationFn: (patientData: Omit<Patient, 'id' | 'createdAt' | 'updatedAt'>) =>
      patientsApi.create(patientData),
    onSuccess: (newPatient) => {
      // Update the cache with the new patient
      queryClient.setQueryData(queryKeys.patients.list(filters), (old: Patient[] = []) => [
        ...old,
        newPatient,
      ]);
      // Invalidate related queries
      getInvalidationKeys.onPatientChange().forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });
    },
  });

  // Mutation for updating patients
  const updatePatientMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<Patient> }) =>
      patientsApi.update(id, updates),
    onSuccess: (updatedPatient) => {
      // Update the cache with the updated patient
      queryClient.setQueryData(queryKeys.patients.list(filters), (old: Patient[] = []) =>
        old.map((p) => (p.id === updatedPatient.id ? updatedPatient : p))
      );
      // Update individual patient cache if it exists
      queryClient.setQueryData(queryKeys.patients.detail(updatedPatient.id), updatedPatient);
      // Invalidate related queries
      getInvalidationKeys.onPatientChange().forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });
    },
  });

  // Mutation for deleting patients
  const deletePatientMutation = useMutation({
    mutationFn: (id: string) => patientsApi.delete(id),
    onSuccess: (_, deletedId) => {
      // Update the cache by removing the deleted patient
      queryClient.setQueryData(queryKeys.patients.list(filters), (old: Patient[] = []) =>
        old.filter((p) => p.id !== deletedId)
      );
      // Remove individual patient cache
      queryClient.removeQueries({ queryKey: queryKeys.patients.detail(deletedId) });
      // Invalidate related queries
      getInvalidationKeys.onPatientChange().forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });
    },
  });

  return {
    patients,
    loading,
    error: error?.message || null,
    fetchPatients,
    createPatient: createPatientMutation.mutateAsync,
    updatePatient: (id: string, updates: Partial<Patient>) =>
      updatePatientMutation.mutateAsync({ id, updates }),
    deletePatient: deletePatientMutation.mutateAsync,
    // Additional mutation states
    isCreating: createPatientMutation.isPending,
    isUpdating: updatePatientMutation.isPending,
    isDeleting: deletePatientMutation.isPending,
  };
};
