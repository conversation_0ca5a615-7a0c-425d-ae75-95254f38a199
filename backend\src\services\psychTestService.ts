import { prisma } from '../utils/database';
import { ApiResponse, PaginatedResponse } from '../types';

export interface CreatePsychTestData {
  patientId: string;
  testName: string;
  testCategory: string;
  version?: string;
  administeredBy: string;
  administeredDate: string;
  completionTime?: number;
  location?: string;
  rawScore?: number;
  totalScore?: number;
  subscaleScores?: string;
  scaledScore?: number;
  percentile?: number;
  tScore?: number;
  zScore?: number;
  severity?: string;
  clinicalRange?: string;
  interpretation?: string;
  recommendations?: string;
  responses: string;
  validity?: string;
  validityIndices?: string;
  notes?: string;
  followUpDate?: string;
  followUpRequired?: boolean;
  batteryId?: string;
  sessionNumber?: number;
  baselineTest?: boolean;
}

export interface UpdatePsychTestData extends Partial<CreatePsychTestData> {}

export interface PsychTestFilters {
  patientId?: string;
  testName?: string;
  testCategory?: string;
}

export class PsychTestService {
  static async createPsychTest(
    data: CreatePsychTestData,
    createdBy: string,
    auditContext: { ipAddress?: string; userAgent?: string }
  ): Promise<ApiResponse<any>> {
    try {
      // Verify patient exists
      const patient = await prisma.patient.findUnique({
        where: { id: data.patientId },
      });

      if (!patient) {
        return {
          success: false,
          error: 'Patient not found',
        };
      }

      // Parse dates
      const administeredDate = new Date(data.administeredDate);
      const followUpDate = data.followUpDate ? new Date(data.followUpDate) : null;

      // Create psychological test
      const psychTest = await prisma.psychTest.create({
        data: {
          patientId: data.patientId,
          testName: data.testName.trim(),
          testCategory: data.testCategory.trim(),
          version: data.version?.trim() || null,
          administeredBy: data.administeredBy.trim(),
          administeredDate,
          completionTime: data.completionTime || null,
          location: data.location?.trim() || null,
          rawScore: data.rawScore || null,
          totalScore: data.totalScore || null,
          subscaleScores: data.subscaleScores || null,
          scaledScore: data.scaledScore || null,
          percentile: data.percentile || null,
          tScore: data.tScore || null,
          zScore: data.zScore || null,
          severity: data.severity?.trim() || null,
          clinicalRange: data.clinicalRange?.trim() || null,
          interpretation: data.interpretation?.trim() || null,
          recommendations: data.recommendations?.trim() || null,
          responses: data.responses,
          validity: data.validity?.trim() || null,
          validityIndices: data.validityIndices || null,
          notes: data.notes?.trim() || null,
          followUpDate,
          followUpRequired: data.followUpRequired || false,
          batteryId: data.batteryId?.trim() || null,
          sessionNumber: data.sessionNumber || null,
          baselineTest: data.baselineTest || false,
        },
        include: {
          patient: {
            select: {
              id: true,
              patientId: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      // TODO: Add audit logging when AuditService is implemented

      return {
        success: true,
        data: psychTest,
        message: 'Psychological test created successfully',
      };
    } catch (error) {
      console.error('Error creating psychological test:', error);
      return {
        success: false,
        error: 'Failed to create psychological test',
      };
    }
  }

  static async getAllPsychTests(
    filters: PsychTestFilters,
    page: number = 1,
    limit: number = 10
  ): Promise<PaginatedResponse<any>> {
    try {
      const skip = (page - 1) * limit;

      const where: any = {};

      if (filters.patientId) {
        where.patientId = filters.patientId;
      }

      if (filters.testName) {
        where.testName = {
          contains: filters.testName,
          mode: 'insensitive',
        };
      }

      if (filters.testCategory) {
        where.testCategory = filters.testCategory;
      }

      const [psychTests, total] = await Promise.all([
        prisma.psychTest.findMany({
          where,
          include: {
            patient: {
              select: {
                id: true,
                patientId: true,
                firstName: true,
                lastName: true,
              },
            },
          },
          orderBy: { administeredDate: 'desc' },
          skip,
          take: limit,
        }),
        prisma.psychTest.count({ where }),
      ]);

      return {
        success: true,
        data: psychTests,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching psychological tests:', error);
      return {
        success: false,
        error: 'Failed to fetch psychological tests',
        data: [],
        pagination: { page, limit, total: 0, pages: 0 },
      };
    }
  }

  static async getPsychTestById(id: string): Promise<ApiResponse<any>> {
    try {
      const psychTest = await prisma.psychTest.findUnique({
        where: { id },
        include: {
          patient: {
            select: {
              id: true,
              patientId: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      if (!psychTest) {
        return {
          success: false,
          error: 'Psychological test not found',
        };
      }

      return {
        success: true,
        data: psychTest,
      };
    } catch (error) {
      console.error('Error fetching psychological test:', error);
      return {
        success: false,
        error: 'Failed to fetch psychological test',
      };
    }
  }

  static async getPsychTestsByPatient(
    patientId: string,
    testCategory?: string,
    limit: number = 10
  ): Promise<ApiResponse<any[]>> {
    try {
      const where: any = { patientId };

      if (testCategory) {
        where.testCategory = testCategory;
      }

      const psychTests = await prisma.psychTest.findMany({
        where,
        orderBy: { administeredDate: 'desc' },
        take: limit,
        include: {
          patient: {
            select: {
              id: true,
              patientId: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      return {
        success: true,
        data: psychTests,
      };
    } catch (error) {
      console.error('Error fetching patient psychological tests:', error);
      return {
        success: false,
        error: 'Failed to fetch patient psychological tests',
        data: [],
      };
    }
  }

  static async updatePsychTest(
    id: string,
    data: UpdatePsychTestData,
    updatedBy: string,
    auditContext: { ipAddress?: string; userAgent?: string }
  ): Promise<ApiResponse<any>> {
    try {
      // Get existing test for audit
      const existingTest = await prisma.psychTest.findUnique({
        where: { id },
      });

      if (!existingTest) {
        return {
          success: false,
          error: 'Psychological test not found',
        };
      }

      // Prepare update data
      const updateData: any = {};

      if (data.testName) updateData.testName = data.testName.trim();
      if (data.testCategory) updateData.testCategory = data.testCategory.trim();
      if (data.version !== undefined) updateData.version = data.version?.trim() || null;
      if (data.administeredBy) updateData.administeredBy = data.administeredBy.trim();
      if (data.administeredDate) updateData.administeredDate = new Date(data.administeredDate);
      if (data.completionTime !== undefined) updateData.completionTime = data.completionTime || null;
      if (data.location !== undefined) updateData.location = data.location?.trim() || null;
      if (data.rawScore !== undefined) updateData.rawScore = data.rawScore || null;
      if (data.totalScore !== undefined) updateData.totalScore = data.totalScore || null;
      if (data.subscaleScores !== undefined) updateData.subscaleScores = data.subscaleScores || null;
      if (data.scaledScore !== undefined) updateData.scaledScore = data.scaledScore || null;
      if (data.percentile !== undefined) updateData.percentile = data.percentile || null;
      if (data.tScore !== undefined) updateData.tScore = data.tScore || null;
      if (data.zScore !== undefined) updateData.zScore = data.zScore || null;
      if (data.severity !== undefined) updateData.severity = data.severity?.trim() || null;
      if (data.clinicalRange !== undefined) updateData.clinicalRange = data.clinicalRange?.trim() || null;
      if (data.interpretation !== undefined) updateData.interpretation = data.interpretation?.trim() || null;
      if (data.recommendations !== undefined) updateData.recommendations = data.recommendations?.trim() || null;
      if (data.responses) updateData.responses = data.responses;
      if (data.validity !== undefined) updateData.validity = data.validity?.trim() || null;
      if (data.validityIndices !== undefined) updateData.validityIndices = data.validityIndices || null;
      if (data.notes !== undefined) updateData.notes = data.notes?.trim() || null;
      if (data.followUpDate !== undefined) updateData.followUpDate = data.followUpDate ? new Date(data.followUpDate) : null;
      if (data.followUpRequired !== undefined) updateData.followUpRequired = data.followUpRequired;
      if (data.batteryId !== undefined) updateData.batteryId = data.batteryId?.trim() || null;
      if (data.sessionNumber !== undefined) updateData.sessionNumber = data.sessionNumber || null;
      if (data.baselineTest !== undefined) updateData.baselineTest = data.baselineTest;

      // Update psychological test
      const updatedTest = await prisma.psychTest.update({
        where: { id },
        data: updateData,
        include: {
          patient: {
            select: {
              id: true,
              patientId: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      // TODO: Add audit logging when AuditService is implemented

      return {
        success: true,
        data: updatedTest,
        message: 'Psychological test updated successfully',
      };
    } catch (error) {
      console.error('Error updating psychological test:', error);
      return {
        success: false,
        error: 'Failed to update psychological test',
      };
    }
  }

  static async deletePsychTest(
    id: string,
    deletedBy: string,
    auditContext: { ipAddress?: string; userAgent?: string }
  ): Promise<ApiResponse<any>> {
    try {
      // Get existing test for audit
      const existingTest = await prisma.psychTest.findUnique({
        where: { id },
      });

      if (!existingTest) {
        return {
          success: false,
          error: 'Psychological test not found',
        };
      }

      // Delete psychological test
      await prisma.psychTest.delete({
        where: { id },
      });

      // TODO: Add audit logging when AuditService is implemented

      return {
        success: true,
        message: 'Psychological test deleted successfully',
      };
    } catch (error) {
      console.error('Error deleting psychological test:', error);
      return {
        success: false,
        error: 'Failed to delete psychological test',
      };
    }
  }
}
