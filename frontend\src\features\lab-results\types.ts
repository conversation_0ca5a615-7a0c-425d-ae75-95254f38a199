export type TestType = 
  | 'CBC' 
  | 'METABOLIC_PANEL' 
  | 'LIPID_PANEL' 
  | 'THYROID' 
  | 'LIVER_FUNCTION'
  | 'KIDNEY_FUNCTION' 
  | 'VITAMIN_LEVELS' 
  | 'DRUG_SCREEN' 
  | 'CARDIAC_MARKERS'
  | 'INFLAMMATORY' 
  | 'COAGULATION' 
  | 'URINALYSIS' 
  | 'HEMOGLOBIN_A1C' 
  | 'OTHER';

export type LabResultStatus = 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'AMENDED';

export type FlagType = 'HIGH' | 'LOW' | 'CRITICAL' | 'ABNORMAL';
export type FlagSeverity = 'MILD' | 'MODERATE' | 'SEVERE';

export interface NormalRange {
  min?: number;
  max?: number;
  unit?: string;
  reference?: string;
}

export interface LabFlag {
  flag: FlagType;
  severity: FlagSeverity;
  note?: string;
}

export interface LabResult {
  id: string;
  patientId: string;
  testType: TestType;
  testDate: string;
  orderedBy: string;
  labName?: string;
  results: Record<string, string | number | boolean>;
  normalRanges?: Record<string, NormalRange>;
  flags?: Record<string, LabFlag>;
  notes?: string;
  status: LabResultStatus;
  isDeleted: boolean;
  deletedAt?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  // Relations
  patient?: {
    id: string;
    patientId: string;
    firstName: string;
    lastName: string;
  };
}

export interface LabResultFormData {
  patientId: string;
  testType: TestType;
  testDate: string;
  orderedBy: string;
  labName?: string;
  results: Record<string, string | number | boolean>;
  normalRanges?: Record<string, NormalRange>;
  flags?: Record<string, LabFlag>;
  notes?: string;
  status?: LabResultStatus;
}

export interface LabResultFilters {
  patientId?: string;
  testType?: TestType;
  status?: LabResultStatus;
  dateFrom?: string;
  dateTo?: string;
  orderedBy?: string;
}

// Lab templates for structured data entry
export interface LabTemplate {
  id: string;
  name: string;
  displayName: string;
  testType: TestType;
  parameters: LabParameter[];
  description?: string;
}

export interface LabParameter {
  id: string;
  name: string;
  displayName: string;
  type: 'number' | 'text' | 'boolean' | 'select';
  unit?: string;
  normalRange?: NormalRange;
  required: boolean;
  options?: string[]; // For select type
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
}

// Common lab value types
export interface CBCResults {
  wbc?: number; // White Blood Cells
  rbc?: number; // Red Blood Cells
  hemoglobin?: number;
  hematocrit?: number;
  platelets?: number;
  neutrophils?: number;
  lymphocytes?: number;
  monocytes?: number;
  eosinophils?: number;
  basophils?: number;
}

export interface MetabolicPanelResults {
  glucose?: number;
  bun?: number; // Blood Urea Nitrogen
  creatinine?: number;
  sodium?: number;
  potassium?: number;
  chloride?: number;
  co2?: number;
  anionGap?: number;
}

export interface LipidPanelResults {
  totalCholesterol?: number;
  ldl?: number; // LDL Cholesterol
  hdl?: number; // HDL Cholesterol
  triglycerides?: number;
  nonHdlCholesterol?: number;
}

export interface ThyroidResults {
  tsh?: number; // Thyroid Stimulating Hormone
  t3?: number;
  t4?: number;
  freeT3?: number;
  freeT4?: number;
}

export interface LiverFunctionResults {
  alt?: number; // Alanine Aminotransferase
  ast?: number; // Aspartate Aminotransferase
  alkalinePhosphatase?: number;
  totalBilirubin?: number;
  directBilirubin?: number;
  albumin?: number;
  totalProtein?: number;
}

// Lab result display helpers
export interface LabResultDisplay {
  id: string;
  patientName: string;
  patientId: string;
  testName: string;
  testDate: string;
  status: LabResultStatus;
  orderedBy: string;
  hasAbnormalValues: boolean;
  criticalValues: string[];
  summary: string;
}

// Trend analysis
export interface LabTrend {
  parameter: string;
  values: Array<{
    date: string;
    value: number;
    isAbnormal: boolean;
  }>;
  trend: 'increasing' | 'decreasing' | 'stable' | 'fluctuating';
  changePercent: number;
}

export interface LabTrendAnalysis {
  patientId: string;
  testType: TestType;
  dateRange: {
    from: string;
    to: string;
  };
  trends: LabTrend[];
  summary: string;
  recommendations: string[];
}
