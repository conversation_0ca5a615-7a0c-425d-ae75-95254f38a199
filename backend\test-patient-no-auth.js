const axios = require('axios');

async function testPatientDetailNoAuth() {
  try {
    const patientId = '285cead6-a4ee-4a25-84f3-5afe1c1d9643';
    
    console.log('Testing patient detail API without auth for ID:', patientId);

    // Test without authentication
    const response = await axios.get(`http://localhost:3002/api/patients/${patientId}`, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 5000
    });

    console.log('Success! Response status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('Error fetching patient detail:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else if (error.code === 'ECONNABORTED') {
      console.error('Request timeout');
    } else {
      console.error('Message:', error.message);
    }
  }
}

testPatientDetailNoAuth();
