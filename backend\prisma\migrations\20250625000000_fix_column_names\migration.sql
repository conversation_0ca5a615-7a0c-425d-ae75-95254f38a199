-- Fix column names to match Prisma schema expectations 
-- Since SQLite doesn't support renaming columns easily, 
-- we'll ensure all tables have the correct column formats by adding any missing columns

-- Check if the is_deleted column exists in patients table
-- If it does not exist, create it
CREATE TABLE IF NOT EXISTS "_temp_check" (
    dummy INTEGER
);
DROP TABLE "_temp_check";

-- Add is_deleted column to appointments if it doesn't exist (should be lowercase in SQLite)
CREATE TABLE IF NOT EXISTS "appointments_new" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "patientId" TEXT NOT NULL,
    "providerId" TEXT,
    "recurringAppointmentId" TEXT,
    "date" DATETIME NOT NULL,
    "endTime" DATETIME,
    "duration" INTEGER NOT NULL,
    "type" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'SCHEDULED',
    "title" TEXT,
    "description" TEXT,
    "location" TEXT,
    "isVirtual" BOOLEAN NOT NULL DEFAULT false,
    "virtualMeetingUrl" TEXT,
    "notes" TEXT,
    "isDeleted" BOOLEAN NOT NULL DEFAULT false,
    "deletedAt" DATETIME,
    "createdBy" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "appointments_patientId_fkey" FOREIGN KEY ("patientId") REFERENCES "patients" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "appointments_providerId_fkey" FOREIGN KEY ("providerId") REFERENCES "users" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT "appointments_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "users" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT "appointments_recurringAppointmentId_fkey" FOREIGN KEY ("recurringAppointmentId") REFERENCES "recurring_appointments" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- Copy data from appointments to new table (SQLite's way of "altering" a table)
INSERT INTO "appointments_new" (
    "id", "patientId", "providerId", "recurringAppointmentId", 
    "date", "endTime", "duration", "type", "status", 
    "title", "description", "location", "isVirtual", "virtualMeetingUrl", 
    "notes", "isDeleted", "deletedAt", "createdBy", "createdAt", "updatedAt"
)
SELECT 
    "id", "patientId", "providerId", "recurringAppointmentId", 
    "date", "endTime", "duration", "type", "status", 
    "title", "description", "location", "isVirtual", "virtualMeetingUrl", 
    "notes", COALESCE("is_deleted", false), "deletedAt", "createdBy", "createdAt", "updatedAt"
FROM "appointments";

-- Drop old table and rename new one
DROP TABLE "appointments";
ALTER TABLE "appointments_new" RENAME TO "appointments";

-- Recreate indexes
CREATE INDEX "appointments_patientId_idx" ON "appointments"("patientId");
CREATE INDEX "appointments_providerId_idx" ON "appointments"("providerId");
CREATE INDEX "appointments_date_idx" ON "appointments"("date");
CREATE INDEX "appointments_status_idx" ON "appointments"("status");
CREATE INDEX "appointments_type_idx" ON "appointments"("type");
CREATE INDEX "appointments_isDeleted_idx" ON "appointments"("isDeleted");
CREATE INDEX "appointments_patientId_date_idx" ON "appointments"("patientId", "date");

-- Add is_deleted column to patients if it doesn't exist
CREATE TABLE IF NOT EXISTS "patients_new" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "patientId" TEXT NOT NULL,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "dateOfBirth" DATETIME NOT NULL,
    "gender" TEXT NOT NULL,
    "phone" TEXT,
    "email" TEXT,
    "address" TEXT,
    "occupation" TEXT,
    "education" TEXT,
    "maritalStatus" TEXT,
    "emergencyContact" TEXT,
    "insuranceInfo" TEXT,
    "medicalHistory" TEXT,
    "allergies" TEXT,
    "currentMeds" TEXT,
    "notes" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isDeleted" BOOLEAN NOT NULL DEFAULT false,
    "deletedAt" DATETIME,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "createdBy" TEXT NOT NULL,
    CONSTRAINT "patients_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "users" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- Copy data from patients to new table
INSERT INTO "patients_new" (
    "id", "patientId", "firstName", "lastName", "dateOfBirth", "gender", 
    "phone", "email", "address", "occupation", "education", "maritalStatus", 
    "emergencyContact", "insuranceInfo", "medicalHistory", "allergies", 
    "currentMeds", "notes", "isActive", "isDeleted", "deletedAt", 
    "createdAt", "updatedAt", "createdBy"
)
SELECT 
    "id", "patientId", "firstName", "lastName", "dateOfBirth", "gender", 
    "phone", "email", "address", "occupation", "education", "maritalStatus", 
    "emergencyContact", "insuranceInfo", "medicalHistory", "allergies", 
    "currentMeds", "notes", "isActive", COALESCE("is_deleted", false), "deletedAt", 
    "createdAt", "updatedAt", "createdBy"
FROM "patients";

-- Drop old table and rename new one
DROP TABLE "patients";
ALTER TABLE "patients_new" RENAME TO "patients";

-- Recreate indexes
CREATE UNIQUE INDEX "patients_patientId_key" ON "patients"("patientId");
CREATE INDEX "patients_lastName_firstName_idx" ON "patients"("lastName", "firstName");
CREATE INDEX "patients_dateOfBirth_idx" ON "patients"("dateOfBirth");
CREATE INDEX "patients_gender_idx" ON "patients"("gender");
CREATE INDEX "patients_isActive_idx" ON "patients"("isActive");
CREATE INDEX "patients_isDeleted_idx" ON "patients"("isDeleted");
CREATE INDEX "patients_createdAt_idx" ON "patients"("createdAt");
CREATE INDEX "patients_createdBy_idx" ON "patients"("createdBy"); 