const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkPatients() {
  try {
    const patients = await prisma.patient.findMany({
      where: { isDeleted: false },
      take: 5
    });
    console.log('Current patients:', patients.length);
    patients.forEach(p => console.log(`- ${p.firstName} ${p.lastName} - ID: ${p.id}`));
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkPatients();
