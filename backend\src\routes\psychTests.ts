import { Router } from 'express';
import { PsychTestController } from '../controllers/psychTestController';
import { authenticate } from '../middleware/auth';
import { authorize } from '../middleware/authorize';
import { asyncHandler } from '../middleware/asyncHandler';

const router = Router();

// Apply authentication to all routes
router.use(authenticate);

/**
 * @route   GET /api/psych-tests
 * @desc    Get all psychological tests with optional filters
 * @access  Private (All authenticated users)
 * @query   patientId, testName, testCategory, page, limit
 */
router.get('/', asyncHandler(PsychTestController.getAllPsychTests));

/**
 * @route   POST /api/psych-tests
 * @desc    Create a new psychological test
 * @access  Private (ADMIN, CLINICIAN)
 * @body    Psychological test data
 */
router.post('/', authorize(['ADMIN', 'CLINICIAN']), asyncHandler(PsychTestController.createPsychTest));

/**
 * @route   GET /api/psych-tests/:id
 * @desc    Get psychological test by ID
 * @access  Private (All authenticated users)
 */
router.get('/:id', asyncHandler(PsychTestController.getPsychTestById));

/**
 * @route   PUT /api/psych-tests/:id
 * @desc    Update psychological test
 * @access  Private (ADMIN, CLINICIAN)
 */
router.put('/:id', authorize(['ADMIN', 'CLINICIAN']), asyncHandler(PsychTestController.updatePsychTest));

/**
 * @route   DELETE /api/psych-tests/:id
 * @desc    Delete psychological test
 * @access  Private (ADMIN, CLINICIAN)
 */
router.delete('/:id', authorize(['ADMIN', 'CLINICIAN']), asyncHandler(PsychTestController.deletePsychTest));

/**
 * @route   GET /api/psych-tests/patient/:patientId
 * @desc    Get psychological tests by patient ID
 * @access  Private (All authenticated users)
 * @query   testCategory, limit
 */
router.get('/patient/:patientId', asyncHandler(PsychTestController.getPsychTestsByPatient));

export default router;
