const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
require('dotenv').config();

const prisma = new PrismaClient();

async function createSampleData() {
  try {
    console.log('🌱 Creating sample data for the Psychiatry App...\n');
    
    // Clean up existing test data
    console.log('🧹 Cleaning up existing data...');
    await prisma.auditLog.deleteMany({});
    await prisma.notification.deleteMany({});
    await prisma.recurringAppointment.deleteMany({});
    await prisma.appointment.deleteMany({});
    await prisma.labResult.deleteMany({});
    await prisma.patient.deleteMany({});
    await prisma.user.deleteMany({});
    console.log('✅ Cleanup completed\n');
    
    // Create sample users
    console.log('👥 Creating sample staff members...');
    const hashedPassword = await bcrypt.hash('password123', 12);
    
    const users = await Promise.all([
      prisma.user.create({
        data: {
          username: 'dr.smith',
          email: '<EMAIL>',
          password: hashedPassword,
          firstName: 'Dr. John',
          lastName: 'Smith',
          role: 'CLINICIAN'
        }
      }),
      prisma.user.create({
        data: {
          username: 'admin',
          email: '<EMAIL>',
          password: hashedPassword,
          firstName: 'Admin',
          lastName: 'User',
          role: 'ADMIN'
        }
      }),
      prisma.user.create({
        data: {
          username: 'nurse.johnson',
          email: '<EMAIL>',
          password: hashedPassword,
          firstName: 'Sarah',
          lastName: 'Johnson',
          role: 'NURSE'
        }
      })
    ]);
    
    console.log(`✅ Created ${users.length} staff members`);
    
    // Create sample patients
    console.log('🏥 Creating sample patients...');
    const patients = await Promise.all([
      prisma.patient.create({
        data: {
          patientId: 'P-2025-001',
          firstName: 'Alice',
          lastName: 'Johnson',
          dateOfBirth: new Date('1985-03-15'),
          gender: 'FEMALE',
          phone: '******-0101',
          email: '<EMAIL>',
          createdBy: users[0].id
        }
      }),
      prisma.patient.create({
        data: {
          patientId: 'P-2025-002',
          firstName: 'Bob',
          lastName: 'Williams',
          dateOfBirth: new Date('1978-07-22'),
          gender: 'MALE',
          phone: '******-0102',
          email: '<EMAIL>',
          createdBy: users[0].id
        }
      }),
      prisma.patient.create({
        data: {
          patientId: 'P-2025-003',
          firstName: 'Carol',
          lastName: 'Davis',
          dateOfBirth: new Date('1992-11-08'),
          gender: 'FEMALE',
          phone: '******-0103',
          email: '<EMAIL>',
          createdBy: users[0].id
        }
      }),
      prisma.patient.create({
        data: {
          patientId: 'P-2025-004',
          firstName: 'David',
          lastName: 'Miller',
          dateOfBirth: new Date('1965-05-30'),
          gender: 'MALE',
          phone: '******-0104',
          createdBy: users[0].id
        }
      }),
      prisma.patient.create({
        data: {
          patientId: 'P-2025-005',
          firstName: 'Emma',
          lastName: 'Wilson',
          dateOfBirth: new Date('1988-09-12'),
          gender: 'FEMALE',
          phone: '******-0105',
          email: '<EMAIL>',
          createdBy: users[0].id
        }
      })
    ]);
    
    console.log(`✅ Created ${patients.length} patients`);
    
    // Create sample appointments
    console.log('📅 Creating sample appointments...');
    const appointments = await Promise.all([
      prisma.appointment.create({
        data: {
          patientId: patients[0].id,
          date: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000), // Tomorrow
          duration: 60,
          type: 'INITIAL_CONSULTATION',
          status: 'SCHEDULED',
          notes: 'Initial psychiatric evaluation for anxiety symptoms'
        }
      }),
      prisma.appointment.create({
        data: {
          patientId: patients[1].id,
          date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
          duration: 45,
          type: 'FOLLOW_UP',
          status: 'CONFIRMED',
          notes: 'Follow-up session for medication adjustment'
        }
      }),
      prisma.appointment.create({
        data: {
          patientId: patients[2].id,
          date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 week from now
          duration: 50,
          type: 'THERAPY_SESSION',
          status: 'SCHEDULED',
          notes: 'Cognitive behavioral therapy session'
        }
      })
    ]);
    
    console.log(`✅ Created ${appointments.length} appointments`);
    
    // Create sample lab results
    console.log('🧪 Creating sample lab results...');
    const labResults = await Promise.all([
      prisma.labResult.create({
        data: {
          patientId: patients[0].id,
          testType: 'CBC',
          testDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
          orderedBy: 'Dr. John Smith',
          labName: 'Central Medical Lab',
          results: JSON.stringify({
            hemoglobin: 13.8,
            hematocrit: 41.2,
            whiteBloodCells: 6800,
            platelets: 285000
          }),
          normalRanges: JSON.stringify({
            hemoglobin: { min: 12.0, max: 16.0, unit: 'g/dL' },
            hematocrit: { min: 36.0, max: 46.0, unit: '%' },
            whiteBloodCells: { min: 4500, max: 11000, unit: '/μL' },
            platelets: { min: 150000, max: 450000, unit: '/μL' }
          }),
          flags: JSON.stringify({}),
          status: 'COMPLETED'
        }
      }),
      prisma.labResult.create({
        data: {
          patientId: patients[1].id,
          testType: 'THYROID',
          testDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
          orderedBy: 'Dr. John Smith',
          labName: 'Central Medical Lab',
          results: JSON.stringify({
            tsh: 2.1,
            t4: 8.2,
            t3: 3.1
          }),
          normalRanges: JSON.stringify({
            tsh: { min: 0.4, max: 4.0, unit: 'mIU/L' },
            t4: { min: 4.5, max: 12.0, unit: 'μg/dL' },
            t3: { min: 2.3, max: 4.2, unit: 'pg/mL' }
          }),
          flags: JSON.stringify({}),
          status: 'COMPLETED'
        }
      })
    ]);
    
    console.log(`✅ Created ${labResults.length} lab results`);
    
    // Create sample notifications
    console.log('🔔 Creating sample notifications...');
    const notifications = await Promise.all([
      prisma.notification.create({
        data: {
          recipientId: users[0].id,
          type: 'APPOINTMENT_REMINDER',
          title: 'Upcoming Appointment',
          message: `Reminder: You have an appointment with ${patients[0].firstName} ${patients[0].lastName} tomorrow at ${appointments[0].date.toLocaleTimeString()}`,
          priority: 'MEDIUM',
          patientId: patients[0].id,
          appointmentId: appointments[0].id
        }
      }),
      prisma.notification.create({
        data: {
          recipientId: users[0].id,
          type: 'LAB_RESULT',
          title: 'Lab Results Available',
          message: `New lab results are available for ${patients[1].firstName} ${patients[1].lastName}`,
          priority: 'LOW',
          patientId: patients[1].id,
          labResultId: labResults[1].id
        }
      })
    ]);
    
    console.log(`✅ Created ${notifications.length} notifications`);
    
    // Final stats
    console.log('\n📊 Sample data creation completed!');
    const finalStats = {
      users: await prisma.user.count(),
      patients: await prisma.patient.count(),
      appointments: await prisma.appointment.count(),
      labResults: await prisma.labResult.count(),
      notifications: await prisma.notification.count()
    };
    
    console.log('Final database stats:');
    Object.entries(finalStats).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`);
    });
    
    console.log('\n🎉 Your Psychiatry App is now ready with sample data!');
    console.log('🌐 Frontend: http://localhost:5174');
    console.log('🔧 Backend API: http://localhost:3001');
    console.log('\n👤 Sample login credentials:');
    console.log('   Username: dr.smith | Password: password123');
    console.log('   Username: admin | Password: password123');
    console.log('   Username: nurse.johnson | Password: password123');
    
  } catch (error) {
    console.error('❌ Error creating sample data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createSampleData();
