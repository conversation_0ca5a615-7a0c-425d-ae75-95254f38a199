import { Router } from 'express';
import { LabResultController } from '@/controllers/labResultController';
import { authenticate, authorize } from '@/middleware/auth';

/**
 * Lab Result routes
 * Base path: /api/lab-results
 */

const router = Router();

// All lab result routes require authentication
router.use(authenticate);

/**
 * @route   GET /api/lab-results
 * @desc    Get lab results with pagination, search, and filtering
 * @access  Private (All authenticated users)
 * @query   page, limit, patientId, testType, status, dateFrom, dateTo, sortBy, sortOrder
 */
router.get('/', LabResultController.getLabResults);

/**
 * @route   POST /api/lab-results
 * @desc    Create a new lab result
 * @access  Private (ADMIN, CLINICIAN)
 * @body    Lab result data (patientId, testType, testDate, results, etc.)
 */
router.post('/', authorize(['ADMIN', 'CLINICIAN']), LabResultController.createLabResult);

/**
 * @route   GET /api/lab-results/:id
 * @desc    Get lab result by ID
 * @access  Private (All authenticated users)
 */
router.get('/:id', LabResultController.getLabResultById);

/**
 * @route   DELETE /api/lab-results/:id
 * @desc    Delete lab result (soft delete)
 * @access  Private (ADMIN, CLINICIAN)
 */
router.delete('/:id', authorize(['ADMIN', 'CLINICIAN']), LabResultController.deleteLabResult);

export default router;
