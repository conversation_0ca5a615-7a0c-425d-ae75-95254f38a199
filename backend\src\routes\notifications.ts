import { Router } from 'express';
import { NotificationController } from '@/controllers/notificationController';
import { authenticate, authorize } from '@/middleware/auth';

/**
 * Notification routes
 * Base path: /api/notifications
 */

const router = Router();

// All notification routes require authentication
router.use(authenticate);

/**
 * @route   GET /api/notifications/stats
 * @desc    Get notification statistics
 * @access  Private (All authenticated users)
 */
router.get('/stats', NotificationController.getNotificationStats);

/**
 * @route   POST /api/notifications/process-scheduled
 * @desc    Process scheduled notifications (for cron jobs)
 * @access  Private (System/Admin only)
 */
router.post('/process-scheduled', authorize(['ADMIN']), NotificationController.processScheduledNotifications);

/**
 * @route   POST /api/notifications/appointment-reminders
 * @desc    Create appointment reminder notifications
 * @access  Private (ADMIN, CLINICIAN, STAFF)
 * @body    { appointmentId: string }
 */
router.post('/appointment-reminders', authorize(['ADMIN', 'CLINICIAN', 'STAFF']), NotificationController.createAppointmentReminders);

/**
 * @route   POST /api/notifications/lab-result
 * @desc    Send lab result notification
 * @access  Private (ADMIN, CLINICIAN)
 * @body    { labResultId: string }
 */
router.post('/lab-result', authorize(['ADMIN', 'CLINICIAN']), NotificationController.sendLabResultNotification);

/**
 * @route   GET /api/notifications
 * @desc    Get notifications with pagination and filtering
 * @access  Private (All authenticated users)
 * @query   page, limit, recipientId, type, status, priority, channel, dateFrom, dateTo
 */
router.get('/', NotificationController.getNotifications);

/**
 * @route   POST /api/notifications
 * @desc    Create a new notification
 * @access  Private (ADMIN, CLINICIAN, STAFF)
 * @body    Notification data (recipientId, type, title, message, etc.)
 */
router.post('/', authorize(['ADMIN', 'CLINICIAN', 'STAFF']), NotificationController.createNotification);

/**
 * @route   PUT /api/notifications/:id/read
 * @desc    Mark notification as read
 * @access  Private (All authenticated users)
 */
router.put('/:id/read', NotificationController.markAsRead);

export default router;
