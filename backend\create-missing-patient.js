const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createMissingPatient() {
  try {
    console.log('🔍 Checking for existing patients...');
    
    // Check if P12345 already exists
    const existingPatient = await prisma.patient.findFirst({
      where: {
        OR: [
          { patientId: 'P12345' },
          { id: 'P12345' }
        ]
      }
    });

    if (existingPatient) {
      console.log('✅ Patient P12345 already exists:', existingPatient.patientId);
      return;
    }

    // Get a user to assign as creator (preferably clinician)
    const clinician = await prisma.user.findFirst({
      where: { role: 'CLINICIAN' }
    });

    if (!clinician) {
      console.error('❌ No clinician found to assign as creator');
      return;
    }

    console.log('👨‍⚕️ Using clinician as creator:', clinician.username);

    // Create the missing patient P12345
    const patient = await prisma.patient.create({
      data: {
        patientId: 'P12345',
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        dateOfBirth: new Date('1981-03-15'),
        gender: 'MALE',
        phone: '555-0123',
        email: '<EMAIL>',
        address: JSON.stringify({
          street: '456 Oak Street',
          city: 'Springfield',
          state: 'IL',
          zipCode: '62705',
          country: 'USA',
        }),
        occupation: 'Software Developer',
        education: 'BACHELORS',
        maritalStatus: 'MARRIED',
        emergencyContact: JSON.stringify({
          name: 'Jane Smith',
          phone: '555-0124',
          relationship: 'Spouse',
          email: '<EMAIL>',
        }),
        insuranceInfo: JSON.stringify({
          provider: 'Blue Cross Blue Shield',
          policyNumber: 'BC987654321',
          groupNumber: 'GRP003',
          subscriberName: 'John Smith',
        }),
        medicalHistory: 'History of depression and anxiety. Currently stable on medication.',
        allergies: 'Penicillin',
        currentMeds: 'Sertraline 50mg daily, Lorazepam 0.5mg as needed',
        notes: 'Patient for frontend testing - Major Depressive Disorder diagnosis',
        createdBy: clinician.id,
      },
    });

    console.log('✅ Successfully created patient P12345:', patient.patientId);
    console.log('📋 Patient details:');
    console.log(`   - ID: ${patient.id}`);
    console.log(`   - Patient ID: ${patient.patientId}`);
    console.log(`   - Name: ${patient.firstName} ${patient.lastName}`);
    console.log(`   - Email: ${patient.email}`);

    // Also create a few more patients that the frontend expects
    const additionalPatients = [
      {
        patientId: 'P12346',
        firstName: 'Emily',
        lastName: 'Davis',
        dateOfBirth: new Date('1994-07-22'),
        gender: 'FEMALE',
        phone: '555-0201',
        email: '<EMAIL>',
        occupation: 'Teacher',
        education: 'MASTERS',
        maritalStatus: 'SINGLE',
        medicalHistory: 'Generalized Anxiety Disorder',
        notes: 'Frontend test patient - GAD diagnosis',
      },
      {
        patientId: 'P12347',
        firstName: 'Robert',
        lastName: 'Wilson',
        dateOfBirth: new Date('1988-11-08'),
        gender: 'MALE',
        phone: '555-0301',
        email: '<EMAIL>',
        occupation: 'Engineer',
        education: 'BACHELORS',
        maritalStatus: 'MARRIED',
        medicalHistory: 'Bipolar Disorder Type I',
        notes: 'Frontend test patient - Bipolar Disorder diagnosis',
      },
      {
        patientId: 'P12348',
        firstName: 'Maria',
        lastName: 'Garcia',
        dateOfBirth: new Date('1992-05-15'),
        gender: 'FEMALE',
        phone: '555-0401',
        email: '<EMAIL>',
        occupation: 'Nurse',
        education: 'BACHELORS',
        maritalStatus: 'SINGLE',
        medicalHistory: 'PTSD following trauma',
        notes: 'Frontend test patient - PTSD diagnosis',
      },
      {
        patientId: 'P12349',
        firstName: 'David',
        lastName: 'Brown',
        dateOfBirth: new Date('1978-09-12'),
        gender: 'MALE',
        phone: '555-0501',
        email: '<EMAIL>',
        occupation: 'Mechanic',
        education: 'HIGH_SCHOOL',
        maritalStatus: 'DIVORCED',
        medicalHistory: 'Schizophrenia, stable on medication',
        notes: 'Frontend test patient - Schizophrenia diagnosis',
      }
    ];

    for (const patientData of additionalPatients) {
      const existing = await prisma.patient.findFirst({
        where: { patientId: patientData.patientId }
      });

      if (!existing) {
        const newPatient = await prisma.patient.create({
          data: {
            ...patientData,
            address: JSON.stringify({
              street: '123 Test St',
              city: 'Springfield',
              state: 'IL',
              zipCode: '62701',
              country: 'USA',
            }),
            emergencyContact: JSON.stringify({
              name: 'Emergency Contact',
              phone: '555-9999',
              relationship: 'Family',
            }),
            insuranceInfo: JSON.stringify({
              provider: 'Test Insurance',
              policyNumber: 'TEST123',
              subscriberName: patientData.firstName + ' ' + patientData.lastName,
            }),
            createdBy: clinician.id,
          },
        });
        console.log(`✅ Created additional patient: ${newPatient.patientId}`);
      } else {
        console.log(`ℹ️  Patient ${patientData.patientId} already exists`);
      }
    }

  } catch (error) {
    console.error('❌ Error creating patient:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createMissingPatient();
