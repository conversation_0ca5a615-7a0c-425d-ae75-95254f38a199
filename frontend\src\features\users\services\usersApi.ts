import { api, handleApiError } from '../../../lib/api';
import type { User, UserFormData, UserFilters } from '../types';

export const usersApi = {
  async getAll(filters?: UserFilters): Promise<User[]> {
    try {
      const params = new URLSearchParams();
      if (filters?.search) params.append('search', filters.search);
      if (filters?.role) params.append('role', filters.role);
      if (filters?.isActive !== undefined) params.append('isActive', filters.isActive.toString());

      const response = await api.get(`/api/users?${params.toString()}`);
      return response.data.data || [];
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async getById(id: string): Promise<User> {
    try {
      const response = await api.get(`/api/users/${id}`);
      return response.data.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async create(userData: UserFormData): Promise<User> {
    try {
      const response = await api.post('/api/users', userData);
      return response.data.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async update(id: string, updates: Partial<UserFormData>): Promise<User> {
    try {
      const response = await api.put(`/api/users/${id}`, updates);
      return response.data.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async delete(id: string): Promise<void> {
    try {
      await api.delete(`/api/users/${id}`);
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },
};
