import { prisma } from '../utils/database';
import { ApiResponse, PaginatedResponse } from '../types';

export interface CreateMentalStatusExamData {
  patientId: string;
  examDate: string;
  examinerId: string;
  appearance_grooming?: string;
  appearance_dress?: string;
  appearance_hygiene?: string;
  behavior_eye_contact?: string;
  behavior_motor?: string;
  behavior_cooperation?: string;
  speech_rate?: string;
  speech_volume?: string;
  speech_tone?: string;
  speech_fluency?: string;
  mood_reported?: string;
  mood_observed?: string;
  affect_type?: string;
  affect_range?: string;
  affect_appropriateness?: string;
  thought_process?: string;
  thought_organization?: string;
  thought_flow?: string;
  thought_content?: string;
  delusions?: boolean;
  delusion_type?: string;
  obsessions?: boolean;
  compulsions?: boolean;
  phobias?: boolean;
  hallucinations?: boolean;
  hallucination_type?: string;
  illusions?: boolean;
  depersonalization?: boolean;
  derealization?: boolean;
  orientation_person?: boolean;
  orientation_place?: boolean;
  orientation_time?: boolean;
  orientation_situation?: boolean;
  attention_span?: string;
  concentration?: string;
  memory_immediate?: string;
  memory_recent?: string;
  memory_remote?: string;
  abstract_thinking?: string;
  insight_level?: string;
  insight_description?: string;
  judgment_level?: string;
  judgment_description?: string;
  suicidal_ideation?: string;
  suicidal_risk?: string;
  homicidal_ideation?: string;
  homicidal_risk?: string;
  clinical_notes?: string;
  recommendations?: string;
  followup_needed?: boolean;
}

export interface UpdateMentalStatusExamData extends Partial<CreateMentalStatusExamData> {}

export interface MentalStatusExamFilters {
  patientId?: string;
  examinerId?: string;
}

export class MentalStatusExamService {
  static async createMentalStatusExam(
    data: CreateMentalStatusExamData,
    createdBy: string,
    auditContext: { ipAddress?: string; userAgent?: string }
  ): Promise<ApiResponse<any>> {
    try {
      // Verify patient exists
      const patient = await prisma.patient.findUnique({
        where: { id: data.patientId },
      });

      if (!patient) {
        return {
          success: false,
          error: 'Patient not found',
        };
      }

      // Verify examiner exists
      const examiner = await prisma.user.findUnique({
        where: { id: data.examinerId },
      });

      if (!examiner) {
        return {
          success: false,
          error: 'Examiner not found',
        };
      }

      // Parse exam date
      const examDate = new Date(data.examDate);

      // Create mental status exam
      const mentalStatusExam = await prisma.mentalStatusExam.create({
        data: {
          patientId: data.patientId,
          examDate,
          examinerId: data.examinerId,
          appearance_grooming: data.appearance_grooming || null,
          appearance_dress: data.appearance_dress || null,
          appearance_hygiene: data.appearance_hygiene || null,
          behavior_eye_contact: data.behavior_eye_contact || null,
          behavior_motor: data.behavior_motor || null,
          behavior_cooperation: data.behavior_cooperation || null,
          speech_rate: data.speech_rate || null,
          speech_volume: data.speech_volume || null,
          speech_tone: data.speech_tone || null,
          speech_fluency: data.speech_fluency || null,
          mood_reported: data.mood_reported || null,
          mood_observed: data.mood_observed || null,
          affect_type: data.affect_type || null,
          affect_range: data.affect_range || null,
          affect_appropriateness: data.affect_appropriateness || null,
          thought_process: data.thought_process || null,
          thought_organization: data.thought_organization || null,
          thought_flow: data.thought_flow || null,
          thought_content: data.thought_content || null,
          delusions: data.delusions || false,
          delusion_type: data.delusion_type || null,
          obsessions: data.obsessions || false,
          compulsions: data.compulsions || false,
          phobias: data.phobias || false,
          hallucinations: data.hallucinations || false,
          hallucination_type: data.hallucination_type || null,
          illusions: data.illusions || false,
          depersonalization: data.depersonalization || false,
          derealization: data.derealization || false,
          orientation_person: data.orientation_person !== false,
          orientation_place: data.orientation_place !== false,
          orientation_time: data.orientation_time !== false,
          orientation_situation: data.orientation_situation !== false,
          attention_span: data.attention_span || null,
          concentration: data.concentration || null,
          memory_immediate: data.memory_immediate || null,
          memory_recent: data.memory_recent || null,
          memory_remote: data.memory_remote || null,
          abstract_thinking: data.abstract_thinking || null,
          insight_level: data.insight_level || null,
          insight_description: data.insight_description || null,
          judgment_level: data.judgment_level || null,
          judgment_description: data.judgment_description || null,
          suicidal_ideation: data.suicidal_ideation || null,
          suicidal_risk: data.suicidal_risk || null,
          homicidal_ideation: data.homicidal_ideation || null,
          homicidal_risk: data.homicidal_risk || null,
          clinical_notes: data.clinical_notes || null,
          recommendations: data.recommendations || null,
          followup_needed: data.followup_needed || false,
        },
        include: {
          patient: {
            select: {
              id: true,
              patientId: true,
              firstName: true,
              lastName: true,
            },
          },
          examiner: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              role: true,
            },
          },
        },
      });

      // Create audit log
      await AuditService.log({
        userId: createdBy,
        action: 'CREATE',
        entityType: 'MentalStatusExam',
        entityId: mentalStatusExam.id,
        newValues: JSON.stringify(mentalStatusExam),
        ipAddress: auditContext.ipAddress,
        userAgent: auditContext.userAgent,
      });

      return {
        success: true,
        data: mentalStatusExam,
        message: 'Mental status exam created successfully',
      };
    } catch (error) {
      console.error('Error creating mental status exam:', error);
      return {
        success: false,
        error: 'Failed to create mental status exam',
      };
    }
  }

  static async getAllMentalStatusExams(
    filters: MentalStatusExamFilters,
    page: number = 1,
    limit: number = 10
  ): Promise<PaginatedResponse<any>> {
    try {
      const skip = (page - 1) * limit;

      const where: any = {};

      if (filters.patientId) {
        where.patientId = filters.patientId;
      }

      if (filters.examinerId) {
        where.examinerId = filters.examinerId;
      }

      const [mentalStatusExams, total] = await Promise.all([
        prisma.mentalStatusExam.findMany({
          where,
          include: {
            patient: {
              select: {
                id: true,
                patientId: true,
                firstName: true,
                lastName: true,
              },
            },
            examiner: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                role: true,
              },
            },
          },
          orderBy: { examDate: 'desc' },
          skip,
          take: limit,
        }),
        prisma.mentalStatusExam.count({ where }),
      ]);

      return {
        success: true,
        data: mentalStatusExams,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching mental status exams:', error);
      return {
        success: false,
        error: 'Failed to fetch mental status exams',
        data: [],
        pagination: { page, limit, total: 0, pages: 0 },
      };
    }
  }

  static async getMentalStatusExamById(id: string): Promise<ApiResponse<any>> {
    try {
      const mentalStatusExam = await prisma.mentalStatusExam.findUnique({
        where: { id },
        include: {
          patient: {
            select: {
              id: true,
              patientId: true,
              firstName: true,
              lastName: true,
            },
          },
          examiner: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              role: true,
            },
          },
        },
      });

      if (!mentalStatusExam) {
        return {
          success: false,
          error: 'Mental status exam not found',
        };
      }

      return {
        success: true,
        data: mentalStatusExam,
      };
    } catch (error) {
      console.error('Error fetching mental status exam:', error);
      return {
        success: false,
        error: 'Failed to fetch mental status exam',
      };
    }
  }

  static async getMentalStatusExamsByPatient(
    patientId: string,
    limit: number = 10
  ): Promise<ApiResponse<any[]>> {
    try {
      const mentalStatusExams = await prisma.mentalStatusExam.findMany({
        where: { patientId },
        orderBy: { examDate: 'desc' },
        take: limit,
        include: {
          patient: {
            select: {
              id: true,
              patientId: true,
              firstName: true,
              lastName: true,
            },
          },
          examiner: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              role: true,
            },
          },
        },
      });

      return {
        success: true,
        data: mentalStatusExams,
      };
    } catch (error) {
      console.error('Error fetching patient mental status exams:', error);
      return {
        success: false,
        error: 'Failed to fetch patient mental status exams',
        data: [],
      };
    }
  }

  static async updateMentalStatusExam(
    id: string,
    data: UpdateMentalStatusExamData,
    updatedBy: string,
    auditContext: { ipAddress?: string; userAgent?: string }
  ): Promise<ApiResponse<any>> {
    try {
      // Get existing exam for audit
      const existingExam = await prisma.mentalStatusExam.findUnique({
        where: { id },
      });

      if (!existingExam) {
        return {
          success: false,
          error: 'Mental status exam not found',
        };
      }

      // Prepare update data
      const updateData: any = {};

      // Only update fields that are provided
      Object.keys(data).forEach(key => {
        if (data[key as keyof UpdateMentalStatusExamData] !== undefined) {
          if (key === 'examDate') {
            updateData[key] = new Date(data[key as keyof UpdateMentalStatusExamData] as string);
          } else {
            updateData[key] = data[key as keyof UpdateMentalStatusExamData];
          }
        }
      });

      // Update mental status exam
      const updatedExam = await prisma.mentalStatusExam.update({
        where: { id },
        data: updateData,
        include: {
          patient: {
            select: {
              id: true,
              patientId: true,
              firstName: true,
              lastName: true,
            },
          },
          examiner: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              role: true,
            },
          },
        },
      });

      // Create audit log
      await AuditService.log({
        userId: updatedBy,
        action: 'UPDATE',
        entityType: 'MentalStatusExam',
        entityId: id,
        oldValues: JSON.stringify(existingExam),
        newValues: JSON.stringify(updatedExam),
        ipAddress: auditContext.ipAddress,
        userAgent: auditContext.userAgent,
      });

      return {
        success: true,
        data: updatedExam,
        message: 'Mental status exam updated successfully',
      };
    } catch (error) {
      console.error('Error updating mental status exam:', error);
      return {
        success: false,
        error: 'Failed to update mental status exam',
      };
    }
  }

  static async deleteMentalStatusExam(
    id: string,
    deletedBy: string,
    auditContext: { ipAddress?: string; userAgent?: string }
  ): Promise<ApiResponse<any>> {
    try {
      // Get existing exam for audit
      const existingExam = await prisma.mentalStatusExam.findUnique({
        where: { id },
      });

      if (!existingExam) {
        return {
          success: false,
          error: 'Mental status exam not found',
        };
      }

      // Delete mental status exam
      await prisma.mentalStatusExam.delete({
        where: { id },
      });

      // Create audit log
      await AuditService.log({
        userId: deletedBy,
        action: 'DELETE',
        entityType: 'MentalStatusExam',
        entityId: id,
        oldValues: JSON.stringify(existingExam),
        ipAddress: auditContext.ipAddress,
        userAgent: auditContext.userAgent,
      });

      return {
        success: true,
        message: 'Mental status exam deleted successfully',
      };
    } catch (error) {
      console.error('Error deleting mental status exam:', error);
      return {
        success: false,
        error: 'Failed to delete mental status exam',
      };
    }
  }
}
