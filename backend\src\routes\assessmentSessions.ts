import { Router } from 'express';
import { AssessmentController } from '@/controllers/assessmentController';
import { authenticate } from '@/middleware/auth';

/**
 * Assessment Session routes
 * Base path: /api/assessment-sessions
 */

const router = Router();

// All routes require authentication
router.use(authenticate);

/**
 * @route   GET /api/assessment-sessions/:id
 * @desc    Get assessment session by ID
 * @access  Private (All authenticated users)
 */
router.get('/:id', AssessmentController.getAssessmentSessionById);

export default router; 