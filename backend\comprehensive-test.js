const axios = require('axios');

const API_BASE_URL = 'http://localhost:3002';

async function runComprehensiveTests() {
  console.log('🧪 Running Comprehensive Tests for Psychiatry App Bug Fixes\n');
  
  let testResults = {
    passed: 0,
    failed: 0,
    tests: []
  };

  // Test 1: Provider Not Found Error (Fixed)
  console.log('1️⃣ Testing Appointment Creation with Provider...');
  try {
    const appointmentData = {
      patientId: '285cead6-a4ee-4a25-84f3-5afe1c1d9643',
      providerId: '99a887f3-1e98-4a6e-978a-1157c95af3f1',
      date: '2025-07-21T14:00:00Z',
      duration: 60,
      type: 'CONSULTATION'
    };

    const response = await axios.post(`${API_BASE_URL}/api/appointments`, appointmentData);
    if (response.status === 201 && response.data.success) {
      console.log('✅ PASS: Appointment creation with provider works');
      testResults.passed++;
      testResults.tests.push({ name: 'Appointment Creation', status: 'PASS' });
    } else {
      throw new Error('Unexpected response format');
    }
  } catch (error) {
    console.log('❌ FAIL: Appointment creation failed:', error.message);
    testResults.failed++;
    testResults.tests.push({ name: 'Appointment Creation', status: 'FAIL', error: error.message });
  }

  // Test 2: Patient Detail Page Data Loading (Fixed)
  console.log('\n2️⃣ Testing Patient Detail API...');
  try {
    const patientId = '285cead6-a4ee-4a25-84f3-5afe1c1d9643';
    const response = await axios.get(`${API_BASE_URL}/api/patients/${patientId}`);
    
    if (response.status === 200 && response.data.success && response.data.data.patient) {
      const patient = response.data.data.patient;
      if (patient.firstName && patient.lastName && patient.id) {
        console.log('✅ PASS: Patient detail API returns complete data');
        testResults.passed++;
        testResults.tests.push({ name: 'Patient Detail API', status: 'PASS' });
      } else {
        throw new Error('Patient data incomplete');
      }
    } else {
      throw new Error('Invalid response structure');
    }
  } catch (error) {
    console.log('❌ FAIL: Patient detail API failed:', error.message);
    testResults.failed++;
    testResults.tests.push({ name: 'Patient Detail API', status: 'FAIL', error: error.message });
  }

  // Test 3: Lab Results Export (Fixed)
  console.log('\n3️⃣ Testing Lab Results Export...');
  try {
    const response = await axios.get(`${API_BASE_URL}/api/export/lab-results`);
    
    if (response.status === 200 && 
        response.headers['content-type'].includes('text/csv') &&
        response.data.includes('Patient ID,Patient Name')) {
      console.log('✅ PASS: Lab results export returns proper CSV format');
      testResults.passed++;
      testResults.tests.push({ name: 'Lab Results Export', status: 'PASS' });
    } else {
      throw new Error('Export not in CSV format or missing headers');
    }
  } catch (error) {
    console.log('❌ FAIL: Lab results export failed:', error.message);
    testResults.failed++;
    testResults.tests.push({ name: 'Lab Results Export', status: 'FAIL', error: error.message });
  }

  // Test 4: Patient Delete Functionality (Fixed)
  console.log('\n4️⃣ Testing Patient Delete Functionality...');
  try {
    // Create a test patient
    const createResponse = await axios.post(`${API_BASE_URL}/api/patients`, {
      firstName: 'Test',
      lastName: 'DeleteTest',
      dateOfBirth: '1990-01-01',
      gender: 'MALE',
      phone: '555-TEST',
      email: '<EMAIL>'
    });

    const testPatientId = createResponse.data.data.id;
    
    // Delete the patient
    const deleteResponse = await axios.delete(`${API_BASE_URL}/api/patients/${testPatientId}`);
    
    if (deleteResponse.status === 200 && deleteResponse.data.success) {
      // Verify patient is no longer accessible
      try {
        await axios.get(`${API_BASE_URL}/api/patients/${testPatientId}`);
        throw new Error('Patient should not be accessible after deletion');
      } catch (getError) {
        if (getError.response && getError.response.status === 404) {
          console.log('✅ PASS: Patient delete functionality works correctly');
          testResults.passed++;
          testResults.tests.push({ name: 'Patient Delete', status: 'PASS' });
        } else {
          throw getError;
        }
      }
    } else {
      throw new Error('Delete operation failed');
    }
  } catch (error) {
    console.log('❌ FAIL: Patient delete failed:', error.message);
    testResults.failed++;
    testResults.tests.push({ name: 'Patient Delete', status: 'FAIL', error: error.message });
  }

  // Test 5: General API Health
  console.log('\n5️⃣ Testing API Health...');
  try {
    const response = await axios.get(`${API_BASE_URL}/health`);
    
    if (response.status === 200 && response.data.success) {
      console.log('✅ PASS: API health check successful');
      testResults.passed++;
      testResults.tests.push({ name: 'API Health', status: 'PASS' });
    } else {
      throw new Error('Health check failed');
    }
  } catch (error) {
    console.log('❌ FAIL: API health check failed:', error.message);
    testResults.failed++;
    testResults.tests.push({ name: 'API Health', status: 'FAIL', error: error.message });
  }

  // Summary
  console.log('\n📊 TEST SUMMARY');
  console.log('================');
  console.log(`Total Tests: ${testResults.passed + testResults.failed}`);
  console.log(`Passed: ${testResults.passed}`);
  console.log(`Failed: ${testResults.failed}`);
  console.log(`Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
  
  console.log('\n📋 DETAILED RESULTS:');
  testResults.tests.forEach((test, index) => {
    const status = test.status === 'PASS' ? '✅' : '❌';
    console.log(`${index + 1}. ${status} ${test.name}`);
    if (test.error) {
      console.log(`   Error: ${test.error}`);
    }
  });

  if (testResults.failed === 0) {
    console.log('\n🎉 ALL TESTS PASSED! The bug fixes are working correctly.');
  } else {
    console.log(`\n⚠️  ${testResults.failed} test(s) failed. Please review the issues above.`);
  }
}

runComprehensiveTests().catch(console.error);
