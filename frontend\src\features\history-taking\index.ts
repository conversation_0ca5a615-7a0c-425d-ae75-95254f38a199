// History Taking feature exports
export { default as HistoryTakingPage } from './components/HistoryTakingPage';
export { default as MoodDisordersAssessment } from './components/MoodDisordersAssessment';
export { default as PsychoticDisordersAssessment } from './components/PsychoticDisordersAssessment';

// Data exports
export { DISORDERS_REGISTRY, getDisorderById, getDisordersByCategory, getAllDisorders } from './data/disorders-registry';
export { MOOD_DISORDERS } from './data/mood-disorders';
export { PSYCHOTIC_DISORDERS } from './data/psychotic-disorders';

// Types exports
export type {
  Disorder,
  DisorderAssessment,
  AssessmentSession,
  Criterion,
  Specifier,
  AssessmentProgress,
  DisorderCategory
} from './types';
