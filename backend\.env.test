# Test Database
TEST_DATABASE_URL="file:./test.db"
DATABASE_URL="file:./test.db"

# JWT Configuration for testing
JWT_SECRET="test-super-secret-jwt-key"
JWT_REFRESH_SECRET="test-super-secret-refresh-key"
JWT_EXPIRES_IN="1h"
JWT_REFRESH_EXPIRES_IN="7d"

# Server Configuration
PORT=3003
NODE_ENV="test"

# CORS Configuration
FRONTEND_URL="http://localhost:5173"

# Rate Limiting (more lenient for tests)
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000

# Security
BCRYPT_ROUNDS=4
MAX_LOGIN_ATTEMPTS=10
LOCKOUT_TIME_MS=60000

# Logging
LOG_LEVEL="error"
