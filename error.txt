in psychiatry assessnt :
:3002/api/patients/assessment-sessions:1 
 Failed to load resource: the server responded with a status of 404 (Not Found)
historyTakingService.ts:15 Error saving assessment session: 
AxiosError
code
: 
"ERR_BAD_REQUEST"
config
: 
{transitional: {…}, adapter: Array(3), transformRequest: Array(1), transformResponse: Array(1), timeout: 10000, …}
message
: 
"Request failed with status code 404"
name
: 
"AxiosError"
request
: 
XMLHttpRequest {onreadystatechange: null, readyState: 4, timeout: 10000, withCredentials: false, upload: XMLHttpRequestUpload, …}
response
: 
{data: {…}, status: 404, statusText: 'Not Found', headers: AxiosHeaders, config: {…}, …}
status
: 
404
stack
: 
"AxiosError: Request failed with status code 404\n    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=0f6856a2:1232:12)\n    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=0f6856a2:1564:7)\n    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=0f6856a2:2122:41)\n    at async Object.saveAssessmentSession (http://localhost:5173/src/features/history-taking/services/historyTakingService.ts:10:24)\n    at async handleSaveSession (http://localhost:5173/src/features/history-taking/components/HistoryTakingPage.tsx:145:7)"
[[Prototype]]
: 
Error
saveAssessmentSession	@	historyTakingService.ts:15
HistoryTakingPage.tsx:141 Error saving assessment: 
AxiosError
code
: 
"ERR_BAD_REQUEST"
config
: 
{transitional: {…}, adapter: Array(3), transformRequest: Array(1), transformResponse: Array(1), timeout: 10000, …}
message
: 
"Request failed with status code 404"
name
: 
"AxiosError"
request
: 
XMLHttpRequest {onreadystatechange: null, readyState: 4, timeout: 10000, withCredentials: false, upload: XMLHttpRequestUpload, …}
response
: 
{data: {…}, status: 404, statusText: 'Not Found', headers: AxiosHeaders, config: {…}, …}
status
: 
404
stack
: 
"AxiosError: Request failed with status code 404\n    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=0f6856a2:1232:12)\n    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=0f6856a2:1564:7)\n    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=0f6856a2:2122:41)\n    at async Object.saveAssessmentSession (http://localhost:5173/src/features/history-taking/services/historyTakingService.ts:10:24)\n    at async handleSaveSession (http://localhost:5173/src/features/history-taking/components/HistoryTakingPage.tsx:145:7)"
[[Prototype]]
: 
Error
handleSaveSession	@	HistoryTakingPage.tsx:141
:3002/api/patients/assessment-sessions:1 
 Failed to load resource: the server responded with a status of 404 (Not Found)
historyTakingService.ts:15 Error saving assessment session: 
AxiosError
code
: 
"ERR_BAD_REQUEST"
config
: 
{transitional: {…}, adapter: Array(3), transformRequest: Array(1), transformResponse: Array(1), timeout: 10000, …}
message
: 
"Request failed with status code 404"
name
: 
"AxiosError"
request
: 
XMLHttpRequest {onreadystatechange: null, readyState: 4, timeout: 10000, withCredentials: false, upload: XMLHttpRequestUpload, …}
response
: 
{data: {…}, status: 404, statusText: 'Not Found', headers: AxiosHeaders, config: {…}, …}
status
: 
404
stack
: 
"AxiosError: Request failed with status code 404\n    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=0f6856a2:1232:12)\n    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=0f6856a2:1564:7)\n    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=0f6856a2:2122:41)\n    at async Object.saveAssessmentSession (http://localhost:5173/src/features/history-taking/services/historyTakingService.ts:10:24)\n    at async handleSaveDraft (http://localhost:5173/src/features/history-taking/components/HistoryTakingPage.tsx:172:7)"
[[Prototype]]
: 
Error
saveAssessmentSession	@	historyTakingService.ts:15
HistoryTakingPage.tsx:176 Error saving draft: 
AxiosError
handleSaveDraft	@	HistoryTakingPage.tsx:176
:3002/api/patients/assessment-sessions:1 
 Failed to load resource: the server responded with a status of 404 (Not Found)
historyTakingService.ts:15 Error saving assessment session: 
AxiosError
saveAssessmentSession	@	historyTakingService.ts:15
HistoryTakingPage.tsx:141 Error saving assessment: 
AxiosError
handleSaveSession	@	HistoryTakingPage.tsx:141
:3002/api/patients/assessment-sessions:1 
 Failed to load resource: the server responded with a status of 404 (Not Found)
historyTakingService.ts:15 Error saving assessment session: 
AxiosError
code
: 
"ERR_BAD_REQUEST"
config
: 
{transitional: {…}, adapter: Array(3), transformRequest: Array(1), transformResponse: Array(1), timeout: 10000, …}
message
: 
"Request failed with status code 404"
name
: 
"AxiosError"
request
: 
XMLHttpRequest {onreadystatechange: null, readyState: 4, timeout: 10000, withCredentials: false, upload: XMLHttpRequestUpload, …}
response
: 
{data: {…}, status: 404, statusText: 'Not Found', headers: AxiosHeaders, config: {…}, …}
status
: 
404
stack
: 
"AxiosError: Request failed with status code 404\n    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=0f6856a2:1232:12)\n    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=0f6856a2:1564:7)\n    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=0f6856a2:2122:41)\n    at async Object.saveAssessmentSession (http://localhost:5173/src/features/history-taking/services/historyTakingService.ts:10:24)\n    at async handleSaveSession (http://localhost:5173/src/features/history-taking/components/HistoryTakingPage.tsx:145:7)"
[[Prototype]]
: 
Error
saveAssessmentSession	@	historyTakingService.ts:15
HistoryTakingPage.tsx:141 Error saving assessment: 
AxiosError
code
: 
"ERR_BAD_REQUEST"
config
: 
{transitional: {…}, adapter: Array(3), transformRequest: Array(1), transformResponse: Array(1), timeout: 10000, …}
message
: 
"Request failed with status code 404"
name
: 
"AxiosError"
request
: 
XMLHttpRequest {onreadystatechange: null, readyState: 4, timeout: 10000, withCredentials: false, upload: XMLHttpRequestUpload, …}
response
: 
{data: {…}, status: 404, statusText: 'Not Found', headers: AxiosHeaders, config: {…}, …}
status
: 
404
stack
: 
"AxiosError: Request failed with status code 404\n    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=0f6856a2:1232:12)\n    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=0f6856a2:1564:7)\n    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=0f6856a2:2122:41)\n    at async Object.saveAssessmentSession (http://localhost:5173/src/features/history-taking/services/historyTakingService.ts:10:24)\n    at async handleSaveSession (http://localhost:5173/src/features/history-taking/components/HistoryTakingPage.tsx:145:7)"
[[Prototype]]
: 
Error

and in the dashboard and analysitcs cant see a thing:
App.tsx:46 Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ["analytics","dashboard"]
<...>		
App	@	App.tsx:46
<App>		
(anonymous)	@	main.tsx:9

analytics:
analyticsApi.ts:28 
 GET http://localhost:3002/api/analytics/patients?from=2025-06-05&to=2025-07-05 404 (Not Found)
Promise.then		
getPatientAnalytics	@	analyticsApi.ts:28
queryFn	@	useAnalytics.ts:41
<...>		
App	@	App.tsx:109
<App>		
(anonymous)	@	main.tsx:9
analyticsApi.ts:42 
 GET http://localhost:3002/api/analytics/appointments?from=2025-06-05&to=2025-07-05 404 (Not Found)
Promise.then		
getAppointmentAnalytics	@	analyticsApi.ts:42
queryFn	@	useAnalytics.ts:63
<...>		
App	@	App.tsx:109
<App>		
(anonymous)	@	main.tsx:9
analyticsApi.ts:56 
 GET http://localhost:3002/api/analytics/lab-results?from=2025-06-05&to=2025-07-05 404 (Not Found)
Promise.then		
getLabResultAnalytics	@	analyticsApi.ts:56
queryFn	@	useAnalytics.ts:85
<...>		
App	@	App.tsx:109
<App>		
(anonymous)	@	main.tsx:9
App.tsx:109 Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ["analytics","dashboard"]
<...>		
App	@	App.tsx:109
<App>		
(anonymous)	@	main.tsx:9
analyticsApi.ts:28 
 GET http://localhost:3002/api/analytics/patients?from=2025-06-05&to=2025-07-05 404 (Not Found)
Promise.then		
getPatientAnalytics	@	analyticsApi.ts:28
queryFn	@	useAnalytics.ts:41
<...>		
App	@	App.tsx:109
<App>		
(anonymous)	@	main.tsx:9
analyticsApi.ts:42 
 GET http://localhost:3002/api/analytics/appointments?from=2025-06-05&to=2025-07-05 404 (Not Found)
Promise.then		
getAppointmentAnalytics	@	analyticsApi.ts:42
queryFn	@	useAnalytics.ts:63
<...>		
App	@	App.tsx:109
<App>		
(anonymous)	@	main.tsx:9
analyticsApi.ts:56 
 GET http://localhost:3002/api/analytics/lab-results?from=2025-06-05&to=2025-07-05 404 (Not Found)
Promise.then		
getLabResultAnalytics	@	analyticsApi.ts:56
queryFn	@	useAnalytics.ts:85
<...>		
App	@	App.tsx:109
<App>		
(anonymous)	@	main.tsx:9
analyticsApi.ts:28 
 GET http://localhost:3002/api/analytics/patients?from=2025-06-05&to=2025-07-05 404 (Not Found)
Promise.then		
getPatientAnalytics	@	analyticsApi.ts:28
queryFn	@	useAnalytics.ts:41
<...>		
App	@	App.tsx:109
<App>		
(anonymous)	@	main.tsx:9
analyticsApi.ts:42 
 GET http://localhost:3002/api/analytics/appointments?from=2025-06-05&to=2025-07-05 404 (Not Found)
analyticsApi.ts:56 
 GET http://localhost:3002/api/analytics/lab-results?from=2025-06-05&to=2025-07-05 404 (Not Found)
Promise.then		
getLabResultAnalytics	@	analyticsApi.ts:56
queryFn	@	useAnalytics.ts:85
<...>		
App	@	App.tsx:109
<App>		
(anonymous)	@	main.tsx:9
analyticsApi.ts:28 
 GET http://localhost:3002/api/analytics/patients?from=2025-06-05&to=2025-07-05 404 (Not Found)
Promise.then		
getPatientAnalytics	@	analyticsApi.ts:28
queryFn	@	useAnalytics.ts:41
<...>		
App	@	App.tsx:109
<App>		
(anonymous)	@	main.tsx:9
analyticsApi.ts:42 
 GET http://localhost:3002/api/analytics/appointments?from=2025-06-05&to=2025-07-05 404 (Not Found)
Promise.then		
getAppointmentAnalytics	@	analyticsApi.ts:42
queryFn	@	useAnalytics.ts:63
<...>		
App	@	App.tsx:109
<App>		
(anonymous)	@	main.tsx:9
analyticsApi.ts:56 
 GET http://localhost:3002/api/analytics/lab-results?from=2025-06-05&to=2025-07-05 404 (Not Found)
Promise.then		
getLabResultAnalytics	@	analyticsApi.ts:56
queryFn	@	useAnalytics.ts:85
<...>		
App	@	App.tsx:109
<App>		
(anonymous)	@	main.tsx:9