const axios = require('axios');

async function testLabResultsExport() {
  try {
    console.log('Testing lab results export API...');

    const response = await axios.get('http://localhost:3002/api/export/lab-results', {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    console.log('Success! Response status:', response.status);
    console.log('Response headers:');
    console.log('- Content-Type:', response.headers['content-type']);
    console.log('- Content-Disposition:', response.headers['content-disposition']);
    console.log('Response data (first 500 chars):');
    console.log(response.data.substring(0, 500));
    console.log('...');
    console.log('Response data type:', typeof response.data);
    console.log('Is CSV format:', response.data.includes('Patient ID,Patient Name'));
  } catch (error) {
    console.error('Error testing lab results export:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Headers:', error.response.headers);
      console.error('Data (first 500 chars):', error.response.data?.substring(0, 500));
    } else {
      console.error('Message:', error.message);
    }
  }
}

testLabResultsExport();
