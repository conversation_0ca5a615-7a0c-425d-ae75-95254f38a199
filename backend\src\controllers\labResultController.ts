import { Response, NextFunction } from 'express';
import { z } from 'zod';
import { LabResultService } from '@/services/labResultService';
import { AuthRequest, CreateLabResultData } from '@/types';
import { createLabResultSchema, updateLabResultSchema } from '../utils/validation';

/**
 * Lab Result controller handling HTTP requests for lab result operations
 */

// Validation schemas
const labResultQuerySchema = z.object({
  page: z.string().optional(),
  limit: z.string().optional(),
  patientId: z.string().uuid().optional(),
  testType: z.string().optional(),
  status: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

const patientLabResultsQuerySchema = z.object({
  testType: z.string().optional(),
  limit: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
});

const trendsQuerySchema = z.object({
  testType: z.string().min(1, 'Test type is required'),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
});

export class LabResultController {
  /**
   * Create a new lab result
   * POST /api/lab-results
   */
  static async createLabResult(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const validatedData = createLabResultSchema.parse(req.body) as CreateLabResultData;

      const result = await LabResultService.createLabResult(
        validatedData,
        req.user!.id,
        { ipAddress: req.ip, userAgent: req.get('User-Agent') }
      );

      res.status(201).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get lab results with pagination and filtering
   * GET /api/lab-results
   */
  static async getLabResults(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const query = labResultQuerySchema.parse(req.query);
      
      const result = await LabResultService.getLabResults(
        query,
        req.user!.id,
        req.user!.role
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get lab result by ID
   * GET /api/lab-results/:id
   */
  static async getLabResultById(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const { id } = req.params;
      
      const result = await LabResultService.getLabResultById(
        id,
        req.user!.id,
        req.user!.role
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get lab results for a specific patient
   * GET /api/patients/:patientId/lab-results
   */
  static async getPatientLabResults(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const { patientId } = req.params;
      const validatedQuery = patientLabResultsQuerySchema.parse(req.query);
      
      const queryOptions = {
        testType: validatedQuery.testType,
        limit: validatedQuery.limit ? parseInt(validatedQuery.limit, 10) : undefined,
        dateFrom: validatedQuery.dateFrom,
        dateTo: validatedQuery.dateTo,
      };

      const result = await LabResultService.getPatientLabResults(
        patientId,
        req.user!.id,
        req.user!.role,
        queryOptions
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get lab result trends for a patient
   * GET /api/patients/:patientId/lab-results/trends
   */
  static async getLabResultTrends(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const { patientId } = req.params;
      const validatedQuery = trendsQuerySchema.parse(req.query);
      
      const result = await LabResultService.getLabResultTrends(
        patientId,
        validatedQuery.testType,
        req.user!.id,
        req.user!.role,
        validatedQuery.dateFrom,
        validatedQuery.dateTo
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update a lab result
   * PUT /api/lab-results/:id
   */
  static async updateLabResult(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }
      
      const validatedData = updateLabResultSchema.parse(req.body);

      const result = await LabResultService.updateLabResult(
        req.params.id,
        validatedData,
        req.user!.id,
        { ipAddress: req.ip, userAgent: req.get('User-Agent') }
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete lab result (soft delete)
   * DELETE /api/lab-results/:id
   */
  static async deleteLabResult(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const { id } = req.params;
      const auditData = {
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
      };

      const result = await LabResultService.deleteLabResult(
        id,
        req.user!.id,
        req.user!.role,
        auditData
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }
}
