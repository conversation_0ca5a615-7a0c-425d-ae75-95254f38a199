import swaggerJSDoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { Express } from 'express';

const options: swaggerJSDoc.Options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Psychiatry Patient Management API',
      version: '1.0.0',
      description: `
        A comprehensive API for managing psychiatric patient care, including patient records, 
        lab results, appointments, and clinical workflows. This API is designed with HIPAA 
        compliance and clinical safety as top priorities.
        
        ## Features
        - **Patient Management**: Complete patient lifecycle management with secure data handling
        - **Lab Results**: Structured lab data with normal ranges and abnormal value flagging
        - **Appointment Scheduling**: Including recurring appointments with complex scheduling patterns
        - **Notifications**: Clinical alerts and reminders for patient care
        - **Analytics**: Clinical insights and reporting capabilities
        - **Audit Logging**: Complete audit trail for compliance and security
        
        ## Security
        - JWT-based authentication with refresh tokens
        - Role-based access control (ADMIN, CLINICIAN, STAFF)
        - Rate limiting and input sanitization
        - HIPAA-compliant data handling
        
        ## Clinical Safety
        - Data validation at all levels
        - Audit trails for all patient data changes
        - Soft deletes to preserve data integrity
        - Automated abnormal value detection in lab results
      `,
      contact: {
        name: 'API Support',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: 'http://localhost:3002',
        description: 'Development server'
      },
      {
        url: 'https://api.psychiatryapp.com',
        description: 'Production server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT token obtained from /api/auth/login'
        }
      },
      schemas: {
        Patient: {
          type: 'object',
          required: ['firstName', 'lastName', 'dateOfBirth', 'gender'],
          properties: {
            id: {
              type: 'string',
              format: 'uuid',
              description: 'Unique patient identifier'
            },
            patientId: {
              type: 'string',
              description: 'Human-readable patient ID (e.g., P-2024-001)',
              example: 'P-2024-001'
            },
            firstName: {
              type: 'string',
              description: 'Patient first name',
              example: 'John'
            },
            lastName: {
              type: 'string',
              description: 'Patient last name',
              example: 'Doe'
            },
            dateOfBirth: {
              type: 'string',
              format: 'date',
              description: 'Patient date of birth',
              example: '1990-01-15'
            },
            gender: {
              type: 'string',
              enum: ['MALE', 'FEMALE', 'NON_BINARY', 'PREFER_NOT_TO_SAY', 'OTHER'],
              description: 'Patient gender'
            },
            phone: {
              type: 'string',
              description: 'Patient phone number',
              example: '+**********'
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'Patient email address',
              example: '<EMAIL>'
            },
            address: {
              type: 'object',
              description: 'Patient address information'
            },
            occupation: {
              type: 'string',
              description: 'Patient occupation',
              example: 'Software Engineer'
            },
            education: {
              type: 'string',
              enum: ['ELEMENTARY', 'HIGH_SCHOOL', 'SOME_COLLEGE', 'BACHELORS', 'MASTERS', 'DOCTORATE', 'PROFESSIONAL', 'OTHER'],
              description: 'Patient education level'
            },
            maritalStatus: {
              type: 'string',
              enum: ['SINGLE', 'MARRIED', 'DIVORCED', 'WIDOWED', 'SEPARATED', 'DOMESTIC_PARTNERSHIP', 'OTHER'],
              description: 'Patient marital status'
            },
            emergencyContact: {
              type: 'object',
              description: 'Emergency contact information'
            },
            insuranceInfo: {
              type: 'object',
              description: 'Insurance information'
            },
            medicalHistory: {
              type: 'string',
              description: 'Medical history notes'
            },
            isActive: {
              type: 'boolean',
              description: 'Whether the patient record is active'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Record creation timestamp'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: 'Record last update timestamp'
            }
          }
        },
        LabResult: {
          type: 'object',
          required: ['patientId', 'testType', 'testDate', 'orderedBy', 'results'],
          properties: {
            id: {
              type: 'string',
              format: 'uuid',
              description: 'Unique lab result identifier'
            },
            patientId: {
              type: 'string',
              format: 'uuid',
              description: 'Associated patient ID'
            },
            testType: {
              type: 'string',
              enum: ['CBC', 'METABOLIC_PANEL', 'LIPID_PANEL', 'THYROID', 'LIVER_FUNCTION', 'KIDNEY_FUNCTION', 'VITAMIN_LEVELS', 'DRUG_SCREEN', 'CARDIAC_MARKERS', 'INFLAMMATORY', 'COAGULATION', 'URINALYSIS', 'HEMOGLOBIN_A1C', 'OTHER'],
              description: 'Type of lab test performed'
            },
            testDate: {
              type: 'string',
              format: 'date-time',
              description: 'Date when the test was performed'
            },
            orderedBy: {
              type: 'string',
              description: 'Name of the ordering physician',
              example: 'Dr. Smith'
            },
            labName: {
              type: 'string',
              description: 'Name of the laboratory facility',
              example: 'Central Lab'
            },
            results: {
              type: 'object',
              description: 'Structured lab result values'
            },
            normalRanges: {
              type: 'object',
              description: 'Reference ranges for the test values'
            },
            flags: {
              type: 'object',
              description: 'Abnormal value flags and alerts'
            },
            status: {
              type: 'string',
              enum: ['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'AMENDED'],
              description: 'Status of the lab result'
            },
            notes: {
              type: 'string',
              description: 'Additional notes about the lab result'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Record creation timestamp'
            }
          }
        },
        Appointment: {
          type: 'object',
          required: ['patientId', 'providerId', 'date', 'duration', 'type'],
          properties: {
            id: {
              type: 'string',
              format: 'uuid',
              description: 'Unique appointment identifier'
            },
            patientId: {
              type: 'string',
              format: 'uuid',
              description: 'Associated patient ID'
            },
            providerId: {
              type: 'string',
              format: 'uuid',
              description: 'Healthcare provider ID'
            },
            date: {
              type: 'string',
              format: 'date-time',
              description: 'Appointment date and time'
            },
            duration: {
              type: 'integer',
              description: 'Appointment duration in minutes',
              example: 60
            },
            type: {
              type: 'string',
              enum: ['INITIAL_CONSULTATION', 'FOLLOW_UP', 'THERAPY_SESSION', 'MEDICATION_REVIEW', 'CRISIS_INTERVENTION', 'GROUP_THERAPY', 'FAMILY_THERAPY', 'PSYCHOLOGICAL_TESTING', 'OTHER'],
              description: 'Type of appointment'
            },
            status: {
              type: 'string',
              enum: ['SCHEDULED', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW'],
              description: 'Appointment status'
            },
            notes: {
              type: 'string',
              description: 'Appointment notes'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Record creation timestamp'
            }
          }
        },
        ApiResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              description: 'Whether the request was successful'
            },
            data: {
              type: 'object',
              description: 'Response data'
            },
            message: {
              type: 'string',
              description: 'Response message'
            },
            error: {
              type: 'string',
              description: 'Error message if request failed'
            }
          }
        },
        PaginatedResponse: {
          allOf: [
            { $ref: '#/components/schemas/ApiResponse' },
            {
              type: 'object',
              properties: {
                pagination: {
                  type: 'object',
                  properties: {
                    page: { type: 'integer', description: 'Current page number' },
                    limit: { type: 'integer', description: 'Items per page' },
                    total: { type: 'integer', description: 'Total number of items' },
                    totalPages: { type: 'integer', description: 'Total number of pages' },
                    hasNext: { type: 'boolean', description: 'Whether there is a next page' },
                    hasPrev: { type: 'boolean', description: 'Whether there is a previous page' }
                  }
                }
              }
            }
          ]
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  apis: [
    './src/routes/*.ts',
    './src/controllers/*.ts'
  ]
};

const specs = swaggerJSDoc(options);

export const setupSwagger = (app: Express): void => {
  app.use('/api/docs', swaggerUi.serve, swaggerUi.setup(specs, {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'Psychiatry API Documentation'
  }));
  
  // Serve the raw OpenAPI spec
  app.get('/api/docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(specs);
  });
};

export default specs;
