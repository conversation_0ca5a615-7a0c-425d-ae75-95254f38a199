import { Request, Response, NextFunction } from 'express';
import { PsychTestService, CreatePsychTestData } from '../services/psychTestService';
import { AuthRequest } from '../types';
import { createPsychTestSchema, updatePsychTestSchema } from '../utils/validation';

export class PsychTestController {
  /**
   * Create a new psychological test
   * POST /api/psych-tests
   */
  static async createPsychTest(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const validatedData = createPsychTestSchema.parse(req.body) as CreatePsychTestData;

      const result = await PsychTestService.createPsychTest(
        validatedData,
        req.user!.id,
        { ipAddress: req.ip, userAgent: req.get('User-Agent') }
      );

      res.status(201).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all psychological tests
   * GET /api/psych-tests
   */
  static async getAllPsychTests(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { patientId, testName, testCategory, page = 1, limit = 10 } = req.query;

      const filters = {
        patientId: patientId as string,
        testName: testName as string,
        testCategory: testCategory as string,
      };

      const result = await PsychTestService.getAllPsychTests(
        filters,
        parseInt(page as string),
        parseInt(limit as string)
      );

      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get psychological test by ID
   * GET /api/psych-tests/:id
   */
  static async getPsychTestById(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      const result = await PsychTestService.getPsychTestById(id);

      if (!result.success) {
        res.status(404).json(result);
        return;
      }

      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get psychological tests by patient ID
   * GET /api/psych-tests/patient/:patientId
   */
  static async getPsychTestsByPatient(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { patientId } = req.params;
      const { testCategory, limit = 10 } = req.query;

      const result = await PsychTestService.getPsychTestsByPatient(
        patientId,
        testCategory as string,
        parseInt(limit as string)
      );

      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update psychological test
   * PUT /api/psych-tests/:id
   */
  static async updatePsychTest(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const { id } = req.params;
      const validatedData = updatePsychTestSchema.parse(req.body);

      const result = await PsychTestService.updatePsychTest(
        id,
        validatedData,
        req.user!.id,
        { ipAddress: req.ip, userAgent: req.get('User-Agent') }
      );

      if (!result.success) {
        res.status(404).json(result);
        return;
      }

      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete psychological test
   * DELETE /api/psych-tests/:id
   */
  static async deletePsychTest(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const { id } = req.params;

      const result = await PsychTestService.deletePsychTest(
        id,
        req.user!.id,
        { ipAddress: req.ip, userAgent: req.get('User-Agent') }
      );

      if (!result.success) {
        res.status(404).json(result);
        return;
      }

      res.json(result);
    } catch (error) {
      next(error);
    }
  }
}
