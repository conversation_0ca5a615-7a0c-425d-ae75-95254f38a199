diff --git a/src/controllers/appointmentController.ts b/src/controllers/appointmentController.ts
index 1234567..abcdefg 100644
--- a/src/controllers/appointmentController.ts
+++ b/src/controllers/appointmentController.ts
@@ -50,7 +50,7 @@ export class AppointmentController {
       const validatedData = createAppointmentSchema.parse(req.body);
 
       const result = await AppointmentService.createAppointment(
-        validatedData,
+        validatedData as any,
         req.user.id,
         { ipAddress: req.ip, userAgent: req.get('User-Agent') }
       );
diff --git a/src/controllers/authController.ts b/src/controllers/authController.ts
index 2345678..bcdefgh 100644
--- a/src/controllers/authController.ts
+++ b/src/controllers/authController.ts
@@ -29,7 +29,7 @@ export class AuthController {
       const validatedData = registerSchema.parse(req.body);
 
       const result = await AuthService.register(
-        validatedData,
+        validatedData as any,
         { ipAddress: req.ip, userAgent: req.get('User-Agent') }
       );
 
@@ -47,7 +47,7 @@ export class AuthController {
       const validatedData = loginSchema.parse(req.body);
       const ipAddress = req.ip || 'unknown';
       const userAgent = req.get('User-Agent') || 'unknown';
-      const result = await AuthService.login(validatedData, ipAddress, userAgent);
+      const result = await AuthService.login(validatedData as any, ipAddress, userAgent);
 
       res.status(200).json(result);
     } catch (error) {
diff --git a/src/controllers/labResultController.ts b/src/controllers/labResultController.ts
index 3456789..cdefghi 100644
--- a/src/controllers/labResultController.ts
+++ b/src/controllers/labResultController.ts
@@ -42,7 +42,7 @@ export class LabResultController {
    */
   static async createLabResult(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user || !req.user.id) {
         return res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -54,7 +54,7 @@ export class LabResultController {
       const result = await LabResultService.createLabResult(
         validatedData,
         req.user.id,
-        { ipAddress: req.ip, userAgent: req.get('User-Agent') }
+        { ipAddress: req.ip || '', userAgent: req.get('User-Agent') || '' }
       );
 
       res.status(201).json(result);
@@ -69,7 +69,7 @@ export class LabResultController {
    */
   static async getLabResults(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user || !req.user.id) {
         return res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -96,7 +96,7 @@ export class LabResultController {
    */
   static async getLabResultById(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user || !req.user.id) {
         return res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -123,7 +123,7 @@ export class LabResultController {
    */
   static async updateLabResult(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user || !req.user.id) {
         return res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -159,7 +159,7 @@ export class LabResultController {
    */
   static async deleteLabResult(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+        if (!req.user || !req.user.id) {
         return res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -190,7 +190,7 @@ export class LabResultController {
    */
   static async getPatientLabResults(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user || !req.user.id) {
         return res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -201,7 +201,7 @@ export class LabResultController {
       const result = await LabResultService.getPatientLabResults(
         req.params.patientId,
         query,
-        req.user.id,
+        req.user!.id,
         req.user.role
       );
 
diff --git a/src/controllers/notificationController.ts b/src/controllers/notificationController.ts
index 4567890..defghij 100644
--- a/src/controllers/notificationController.ts
+++ b/src/controllers/notificationController.ts
@@ -36,7 +36,7 @@ export class NotificationController {
    */
   static async createNotification(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user || !req.user.id) {
         return res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -47,7 +47,7 @@ export class NotificationController {
 
       const result = await NotificationService.createNotification(
         validatedData,
-        req.user.id
+        req.user!.id
       );
 
       res.status(201).json(result);
@@ -65,7 +65,7 @@ export class NotificationController {
    */
   static async getNotifications(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user || !req.user.id) {
         return res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -92,7 +92,7 @@ export class NotificationController {
    */
   static async getNotificationById(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user || !req.user.id) {
         return res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -100,7 +100,7 @@ export class NotificationController {
       }
 
       const result = await NotificationService.getNotificationById(
-        req.params.id,
+        req.params.id!,
         req.user.id
       );
       res.status(200).json(result);
@@ -116,7 +116,7 @@ export class NotificationController {
    */
   static async markAsRead(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user || !req.user.id) {
         return res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -124,7 +124,7 @@ export class NotificationController {
       }
 
       const result = await NotificationService.markAsRead(
-        req.params.id,
+        req.params.id!,
         req.user.id
       );
       res.status(200).json(result);
@@ -140,7 +140,7 @@ export class NotificationController {
    */
   static async markAllAsRead(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user || !req.user.id) {
         return res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -163,7 +163,7 @@ export class NotificationController {
    */
   static async deleteNotification(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user || !req.user.id) {
         return res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -171,7 +171,7 @@ export class NotificationController {
       }
       
       const result = await NotificationService.deleteNotification(
-        req.params.id,
+        req.params.id!,
         req.user.id
       );
       res.status(200).json(result);
@@ -188,7 +188,7 @@ export class NotificationController {
    */
   static async createAppointmentReminders(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user || !req.user.id) {
         return res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -199,7 +199,7 @@ export class NotificationController {
       
       const result = await NotificationService.createAppointmentReminders(
         appointmentId,
-        req.user.id
+        req.user!.id
       );
 
       res.status(201).json(result);
@@ -217,7 +217,7 @@ export class NotificationController {
    */
   static async sendLabResultNotification(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user || !req.user.id) {
         return res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -228,7 +228,7 @@ export class NotificationController {
       
       const result = await NotificationService.sendLabResultNotification(
         labResultId,
-        req.user.id
+        req.user!.id
       );
 
       res.status(201).json(result);
@@ -259,7 +259,7 @@ export class NotificationController {
    */
   static async getNotificationStats(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user || !req.user.id) {
         return res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -268,8 +268,8 @@ export class NotificationController {
 
       const result = await NotificationService.getNotificationStats(
         req.user.id,
-        req.user.role
+        req.user.role!
       );
 
       res.status(200).json(result);
diff --git a/src/controllers/patientController.ts b/src/controllers/patientController.ts
index 5678901..defghik 100644
--- a/src/controllers/patientController.ts
+++ b/src/controllers/patientController.ts
@@ -35,7 +35,7 @@ export class PatientController {
    */
   static async createPatient(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user || !req.user.id) {
         return res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -46,7 +46,7 @@ export class PatientController {
 
       const result = await PatientService.createPatient(
         validatedData,
-        req.user.id,
+        req.user!.id,
         { ipAddress: req.ip, userAgent: req.get('User-Agent') }
       );
 
@@ -64,7 +64,7 @@ export class PatientController {
    */
   static async getPatients(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user || !req.user.id) {
         return res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -73,7 +73,7 @@ export class PatientController {
 
       const query = querySchema.parse(req.query);
       const result = await PatientService.getPatients(
-        req.user.id,
+        req.user!.id,
         req.user.role,
         query
       );