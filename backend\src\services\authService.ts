import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import { hashPassword, comparePassword, validatePasswordStrength } from '@/utils/password';
import { generateAccessToken, generateRefreshToken, verifyRefreshToken } from '@/utils/jwt';
import {
  AuthenticationError,
  ValidationError,
  ConflictError,
  NotFoundError,
} from '@/utils/errors';
import {
  LoginCredentials,
  RegisterData,
  AuthenticatedUser,
  ApiResponse,
  AuditLogData,
} from '@/types';

const prisma = new PrismaClient();

// Configuration constants
const MAX_LOGIN_ATTEMPTS = parseInt(process.env.MAX_LOGIN_ATTEMPTS || '5', 10);
const LOCKOUT_TIME_MS = parseInt(process.env.LOCKOUT_TIME_MS || '900000', 10); // 15 minutes

/**
 * Authentication service handling user login, registration, and token management
 */
export class AuthService {
  /**
   * Register a new user
   */
  static async register(data: RegisterData): Promise<ApiResponse<{ user: AuthenticatedUser }>> {
    // Validate password strength
    const passwordValidation = validatePasswordStrength(data.password);
    if (!passwordValidation.isValid) {
      throw new ValidationError(
        `Password validation failed: ${passwordValidation.errors.join(', ')}`
      );
    }

    // Check if username or email already exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username: data.username },
          { email: data.email },
        ],
      },
    });

    if (existingUser) {
      if (existingUser.username === data.username) {
        throw new ConflictError('Username already exists');
      }
      if (existingUser.email === data.email) {
        throw new ConflictError('Email already exists');
      }
    }

    // Hash password
    const hashedPassword = await hashPassword(data.password);

    // Create user
    const user = await prisma.user.create({
      data: {
        username: data.username,
        email: data.email,
        password: hashedPassword,
        firstName: data.firstName,
        lastName: data.lastName,
        role: data.role || 'CLINICIAN',
      },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        firstName: true,
        lastName: true,
        createdAt: true,
      },
    });

    // Log registration
    await this.createAuditLog({
      userId: user.id,
      action: 'CREATE',
      entityType: 'USER',
      entityId: user.id,
      newValues: {
        username: user.username,
        email: user.email,
        role: user.role,
      },
    });

    return {
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role as 'ADMIN' | 'CLINICIAN' | 'STAFF',
          firstName: user.firstName,
          lastName: user.lastName,
        },
      },
      message: 'User registered successfully',
    };
  }

  /**
   * Login user with credentials
   */
  static async login(
    credentials: LoginCredentials,
    ipAddress?: string,
    userAgent?: string
  ): Promise<ApiResponse<{ user: AuthenticatedUser; accessToken: string; refreshToken: string }>> {
    const { username, password } = credentials;

    // Find user by username or email
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { username },
          { email: username },
        ],
      },
    });

    if (!user) {
      throw new AuthenticationError('Invalid credentials');
    }

    // Check if account is locked
    if (user.lockedUntil && user.lockedUntil > new Date()) {
      const lockoutMinutes = Math.ceil((user.lockedUntil.getTime() - Date.now()) / 60000);
      throw new AuthenticationError(
        `Account is locked. Try again in ${lockoutMinutes} minutes.`
      );
    }

    // Check if account is active
    if (!user.isActive) {
      throw new AuthenticationError('Account is deactivated');
    }

    // Verify password
    const isPasswordValid = await comparePassword(password, user.password);

    if (!isPasswordValid) {
      // Increment login attempts
      const newAttempts = user.loginAttempts + 1;
      const shouldLock = newAttempts >= MAX_LOGIN_ATTEMPTS;

      await prisma.user.update({
        where: { id: user.id },
        data: {
          loginAttempts: newAttempts,
          lockedUntil: shouldLock ? new Date(Date.now() + LOCKOUT_TIME_MS) : null,
        },
      });

      if (shouldLock) {
        throw new AuthenticationError(
          `Account locked due to too many failed login attempts. Try again in ${Math.ceil(LOCKOUT_TIME_MS / 60000)} minutes.`
        );
      }

      throw new AuthenticationError('Invalid credentials');
    }

    // Reset login attempts on successful login
    await prisma.user.update({
      where: { id: user.id },
      data: {
        loginAttempts: 0,
        lockedUntil: null,
        lastLogin: new Date(),
      },
    });

    // Generate tokens
    const tokenId = uuidv4();
    const accessToken = generateAccessToken({
      userId: user.id,
      username: user.username,
      role: user.role,
    });
    const refreshToken = generateRefreshToken({
      userId: user.id,
      tokenId,
    });

    // Store refresh token
    await prisma.refreshToken.create({
      data: {
        id: tokenId,
        token: refreshToken,
        userId: user.id,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      },
    });

    // Log successful login
    await this.createAuditLog({
      userId: user.id,
      action: 'VIEW',
      entityType: 'USER',
      entityId: user.id,
      ipAddress,
      userAgent,
    });

    return {
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role as 'ADMIN' | 'CLINICIAN' | 'STAFF',
          firstName: user.firstName,
          lastName: user.lastName,
        },
        accessToken,
        refreshToken,
      },
      message: 'Login successful',
    };
  }

  /**
   * Refresh access token using refresh token
   */
  static async refreshToken(refreshToken: string): Promise<ApiResponse<{ accessToken: string; refreshToken: string }>> {
    try {
      // Verify refresh token
      const payload = verifyRefreshToken(refreshToken);

      // Find stored refresh token
      const storedToken = await prisma.refreshToken.findUnique({
        where: { id: payload.tokenId },
        include: { user: true },
      });

      if (!storedToken || storedToken.isRevoked || storedToken.expiresAt < new Date()) {
        throw new AuthenticationError('Invalid or expired refresh token');
      }

      if (!storedToken.user.isActive) {
        throw new AuthenticationError('Account is deactivated');
      }

      // Generate new tokens
      const newTokenId = uuidv4();
      const newAccessToken = generateAccessToken({
        userId: storedToken.user.id,
        username: storedToken.user.username,
        role: storedToken.user.role,
      });
      const newRefreshToken = generateRefreshToken({
        userId: storedToken.user.id,
        tokenId: newTokenId,
      });

      // Revoke old refresh token and create new one
      await prisma.$transaction([
        prisma.refreshToken.update({
          where: { id: payload.tokenId },
          data: { isRevoked: true },
        }),
        prisma.refreshToken.create({
          data: {
            id: newTokenId,
            token: newRefreshToken,
            userId: storedToken.user.id,
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
          },
        }),
      ]);

      return {
        success: true,
        data: {
          accessToken: newAccessToken,
          refreshToken: newRefreshToken,
        },
        message: 'Token refreshed successfully',
      };
    } catch (error) {
      throw new AuthenticationError('Invalid refresh token');
    }
  }

  /**
   * Logout user by revoking refresh token
   */
  static async logout(refreshToken: string): Promise<ApiResponse<null>> {
    try {
      const payload = verifyRefreshToken(refreshToken);
      
      await prisma.refreshToken.update({
        where: { id: payload.tokenId },
        data: { isRevoked: true },
      });

      return {
        success: true,
        data: null,
        message: 'Logout successful',
      };
    } catch {
      // Even if token is invalid, consider logout successful
      return {
        success: true,
        data: null,
        message: 'Logout successful',
      };
    }
  }

  /**
   * Get current user information
   */
  static async getCurrentUser(userId: string): Promise<ApiResponse<{ user: AuthenticatedUser }>> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        firstName: true,
        lastName: true,
        lastLogin: true,
        createdAt: true,
      },
    });

    if (!user) {
      throw new NotFoundError('User not found');
    }

    return {
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role as 'ADMIN' | 'CLINICIAN' | 'STAFF',
          firstName: user.firstName,
          lastName: user.lastName,
        },
      },
    };
  }

  /**
   * Change user password
   */
  static async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string
  ): Promise<ApiResponse<null>> {
    // Get user
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Verify current password
    const isCurrentPasswordValid = await comparePassword(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      throw new AuthenticationError('Current password is incorrect');
    }

    // Validate new password strength
    const passwordValidation = validatePasswordStrength(newPassword);
    if (!passwordValidation.isValid) {
      throw new ValidationError(
        `Password validation failed: ${passwordValidation.errors.join(', ')}`
      );
    }

    // Hash new password
    const hashedNewPassword = await hashPassword(newPassword);

    // Update password
    await prisma.user.update({
      where: { id: userId },
      data: { password: hashedNewPassword },
    });

    // Revoke all refresh tokens to force re-login
    await prisma.refreshToken.updateMany({
      where: { userId },
      data: { isRevoked: true },
    });

    // Log password change
    await this.createAuditLog({
      userId,
      action: 'UPDATE',
      entityType: 'USER',
      entityId: userId,
      newValues: { passwordChanged: true },
    });

    return {
      success: true,
      data: null,
      message: 'Password changed successfully',
    };
  }

  /**
   * Create audit log entry
   */
  private static async createAuditLog(data: AuditLogData): Promise<void> {
    try {
      await prisma.auditLog.create({
        data: {
          userId: data.userId,
          action: data.action,
          entityType: data.entityType,
          entityId: data.entityId,
          oldValues: data.oldValues ? JSON.stringify(data.oldValues) : null,
          newValues: data.newValues ? JSON.stringify(data.newValues) : null,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          patientId: data.patientId,
          labResultId: data.labResultId,
          appointmentId: data.appointmentId,
        },
      });
    } catch (error) {
      // Log audit creation failure but don't throw
      console.error('Failed to create audit log:', error);
    }
  }
}
