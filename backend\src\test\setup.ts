import { PrismaClient } from '@prisma/client'
import dotenv from 'dotenv'
import { beforeAll, afterAll, beforeEach } from 'vitest'

// Load test environment variables
dotenv.config({ path: '.env.test' })

// Global test setup
const prisma = new PrismaClient()

beforeAll(async () => {
  // Ensure test database is clean
  await cleanDatabase()
})

afterAll(async () => {
  // Clean up after all tests
  await cleanDatabase()
  await prisma.$disconnect()
})

beforeEach(async () => {
  // Clean database before each test to ensure isolation
  await cleanDatabase()
})

/**
 * Clean all data from test database
 */
async function cleanDatabase() {
  const tablenames = await prisma.$queryRaw<Array<{ name: string }>>`
    SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name NOT LIKE '_prisma_%';
  `

  for (const { name } of tablenames) {
    try {
      await prisma.$executeRawUnsafe(`DELETE FROM "${name}";`)
    } catch (error) {
      console.warn(`Failed to clean table ${name}:`, error)
    }
  }
}

/**
 * Create test user for authentication
 */
export async function createTestUser(role: 'ADMIN' | 'CLINICIAN' | 'STAFF' = 'CLINICIAN') {
  const bcrypt = require('bcrypt')
  const hashedPassword = await bcrypt.hash('testpassword123', 4)

  return await prisma.user.create({
    data: {
      username: `testuser_${Date.now()}`,
      email: `test_${Date.now()}@example.com`,
      password: hashedPassword,
      firstName: 'Test',
      lastName: 'User',
      role,
      isActive: true,
    },
  })
}

/**
 * Create test patient
 */
export async function createTestPatient(createdBy: string) {
  return await prisma.patient.create({
    data: {
      patientId: `P-TEST-${Date.now()}`,
      firstName: 'John',
      lastName: 'Doe',
      dateOfBirth: new Date('1990-01-01'),
      gender: 'MALE',
      phone: '+**********',
      email: '<EMAIL>',
      address: JSON.stringify({
        street: '123 Test St',
        city: 'Test City',
        state: 'TS',
        zipCode: '12345',
        country: 'USA'
      }),
      occupation: 'Software Engineer',
      education: 'BACHELORS',
      maritalStatus: 'SINGLE',
      emergencyContact: JSON.stringify({
        name: 'Jane Doe',
        relationship: 'Sister',
        phone: '+**********'
      }),
      insuranceInfo: JSON.stringify({
        provider: 'Test Insurance',
        policyNumber: 'TEST123456',
        groupNumber: 'GRP789'
      }),
      medicalHistory: JSON.stringify({
        allergies: ['Penicillin'],
        medications: ['Vitamin D'],
        conditions: []
      }),
      createdBy,
      isActive: true,
      isDeleted: false,
    },
  })
}

// Export prisma instance for tests
export { prisma }
