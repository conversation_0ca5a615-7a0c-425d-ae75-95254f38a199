diff --git a/src/controllers/appointmentController.ts b/src/controllers/appointmentController.ts
index 1234567..abcdefg 100644
--- a/src/controllers/appointmentController.ts
+++ b/src/controllers/appointmentController.ts
@@ -50,7 +50,7 @@ export class AppointmentController {
       const validatedData = createAppointmentSchema.parse(req.body);
 
       const result = await AppointmentService.createAppointment(
-        validatedData,
+        validatedData as CreateAppointmentData,
         req.user.id,
         { ipAddress: req.ip, userAgent: req.get('User-Agent') }
       );
diff --git a/src/controllers/authController.ts b/src/controllers/authController.ts
index 2345678..bcdefgh 100644
--- a/src/controllers/authController.ts
+++ b/src/controllers/authController.ts
@@ -29,7 +29,7 @@ export class AuthController {
       const validatedData = registerSchema.parse(req.body);
 
       const result = await AuthService.register(
-        validatedData,
+        validatedData as RegisterData,
         { ipAddress: req.ip, userAgent: req.get('User-Agent') }
       );
 
@@ -47,7 +47,7 @@ export class AuthController {
       const validatedData = loginSchema.parse(req.body);
       const ipAddress = req.ip || 'unknown';
       const userAgent = req.get('User-Agent') || 'unknown';
-      const result = await AuthService.login(validatedData, ipAddress, userAgent);
+      const result = await AuthService.login(validatedData as LoginCredentials, ipAddress, userAgent);
 
       res.status(200).json(result);
     } catch (error) {
diff --git a/src/controllers/labResultController.ts b/src/controllers/labResultController.ts
index 3456789..cdefghi 100644
--- a/src/controllers/labResultController.ts
+++ b/src/controllers/labResultController.ts
@@ -42,7 +42,7 @@ export class LabResultController {
    */
   static async createLabResult(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user?.id) {
         res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -51,7 +51,7 @@ export class LabResultController {
       }
 
       const validatedData = createLabResultSchema.parse(req.body);
-      const result = await LabResultService.createLabResult(validatedData, req.user.id);
+      const result = await LabResultService.createLabResult(validatedData as CreateLabResultData, req.user.id);
       res.status(201).json(result);
     } catch (error) {
       next(error);
@@ -69,7 +69,7 @@ export class LabResultController {
    */
   static async getLabResults(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user?.id) {
         res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -96,7 +96,7 @@ export class LabResultController {
    */
   static async getLabResultById(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user?.id) {
         res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -123,7 +123,7 @@ export class LabResultController {
    */
   static async updateLabResult(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user?.id) {
         res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -159,7 +159,7 @@ export class LabResultController {
    */
   static async deleteLabResult(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+        if (!req.user?.id) {
         res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -190,7 +190,7 @@ export class LabResultController {
    */
   static async getLabResultsByPatient(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user?.id) {
         res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -198,7 +198,7 @@ export class LabResultController {
         return;
       }
 
-      const result = await LabResultService.getLabResultsByPatient(req.params.patientId);
+      const result = await LabResultService.getLabResultsByPatient(req.params.patientId!);
       res.status(200).json(result);
     } catch (error) {
       next(error);
diff --git a/src/controllers/notificationController.ts b/src/controllers/notificationController.ts
index 4567890..defghij 100644
--- a/src/controllers/notificationController.ts
+++ b/src/controllers/notificationController.ts
@@ -48,7 +48,7 @@ export class NotificationController {
    */
   static async createNotification(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user?.id) {
         res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -57,7 +57,7 @@ export class NotificationController {
       }
 
       const validatedData = createNotificationSchema.parse(req.body);
-      const result = await NotificationService.createNotification(validatedData, req.user.id);
+      const result = await NotificationService.createNotification(validatedData as CreateNotificationData, req.user.id);
       res.status(201).json(result);
     } catch (error) {
       next(error);
@@ -75,7 +75,7 @@ export class NotificationController {
    */
   static async getNotifications(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user?.id) {
         res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -84,7 +84,7 @@ export class NotificationController {
       }
 
       const query = querySchema.parse(req.query);
-      const result = await NotificationService.getNotifications(req.user.id, req.user.role, query);
+      const result = await NotificationService.getNotifications(req.user.id, req.user.role!, query);
       res.status(200).json(result);
     } catch (error) {
       next(error);
@@ -102,7 +102,7 @@ export class NotificationController {
    */
   static async getNotificationById(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user?.id) {
         res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -111,7 +111,7 @@ export class NotificationController {
       }
 
       const { id } = req.params;
-      const result = await NotificationService.getNotificationById(id, req.user.id, req.user.role);
+      const result = await NotificationService.getNotificationById(id, req.user.id, req.user.role!);
       res.status(200).json(result);
     } catch (error) {
       next(error);
@@ -129,7 +129,7 @@ export class NotificationController {
    */
   static async markAsRead(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user?.id) {
         res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -138,7 +138,7 @@ export class NotificationController {
       }
 
       const { id } = req.params;
-      const result = await NotificationService.markAsRead(id, req.user.id);
+      const result = await NotificationService.markAsRead(id, req.user.id);
       res.status(200).json(result);
     } catch (error) {
       next(error);
@@ -156,7 +156,7 @@ export class NotificationController {
    */
   static async markAllAsRead(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user?.id) {
         res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -164,7 +164,7 @@ export class NotificationController {
         return;
       }
 
-      const result = await NotificationService.markAllAsRead(req.user.id);
+      const result = await NotificationService.markAllAsRead(req.user.id);
       res.status(200).json(result);
     } catch (error) {
       next(error);
@@ -182,7 +182,7 @@ export class NotificationController {
    */
   static async deleteNotification(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
     try {
-      if (!req.user) {
+      if (!req.user?.id) {
         res.status(401).json({
           success: false,
           error: 'Authentication required',
@@ -191,7 +191,7 @@ export class NotificationController {
       }
 
       const { id } = req.params;
-     