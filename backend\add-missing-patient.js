const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  // First, get the clinician user
  const clinician = await prisma.user.findFirst({
    where: { role: 'CLINICIAN' }
  });

  if (!clinician) {
    console.error('No clinician found. Please run the seed script first.');
    return;
  }

  // Create the missing patient P12346
  const patient = await prisma.patient.create({
    data: {
      patientId: 'P12346',
      firstName: 'Alex',
      lastName: '<PERSON>',
      dateOfBirth: new Date('1990-05-12'),
      gender: 'MALE',
      phone: '555-1234',
      email: '<EMAIL>',
      address: JSON.stringify({
        street: '123 Pine St',
        city: 'Springfield',
        state: 'IL',
        zipCode: '62704',
        country: 'USA',
      }),
      occupation: 'Engineer',
      education: 'MASTERS',
      maritalStatus: 'SINGLE',
      emergencyContact: JSON.stringify({
        name: '<PERSON>',
        phone: '555-5678',
        relationship: 'Father',
      }),
      insuranceInfo: JSON.stringify({
        provider: 'United Healthcare',
        policyNumber: 'UH123456789',
        groupNumber: 'GRP002',
        subscriberName: '<PERSON> <PERSON>',
      }),
      medicalHistory: 'History of generalized anxiety disorder.',
      allergies: 'None',
      currentMeds: 'Escitalopram 10mg daily',
      notes: 'Patient has been showing improvement with current treatment plan.',
      createdBy: clinician.id,
    },
  });

  console.log(`✅ Created missing patient: ${patient.patientId}`);

  // Also create another patient to ensure we have sufficient data
  const additionalPatient = await prisma.patient.create({
    data: {
      patientId: 'P12347',
      firstName: 'Emma',
      lastName: 'Davis',
      dateOfBirth: new Date('1988-09-23'),
      gender: 'FEMALE',
      phone: '555-9876',
      email: '<EMAIL>',
      address: JSON.stringify({
        street: '456 Maple Ave',
        city: 'Springfield',
        state: 'IL',
        zipCode: '62705',
        country: 'USA',
      }),
      occupation: 'Graphic Designer',
      education: 'BACHELORS',
      maritalStatus: 'MARRIED',
      emergencyContact: JSON.stringify({
        name: 'William Davis',
        phone: '555-5432',
        relationship: 'Husband',
      }),
      insuranceInfo: JSON.stringify({
        provider: 'Cigna',
        policyNumber: 'CG987654321',
        groupNumber: 'GRP003',
        subscriberName: 'Emma Davis',
      }),
      medicalHistory: 'History of insomnia and mild depression.',
      allergies: 'Sulfa drugs',
      currentMeds: 'Trazodone 50mg at bedtime, Bupropion 150mg daily',
      notes: 'Patient reports improved sleep with current medication.',
      createdBy: clinician.id,
    },
  });

  console.log(`✅ Created additional patient: ${additionalPatient.patientId}`);
}

main()
  .catch((e) => {
    console.error('❌ Error adding missing patient:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 