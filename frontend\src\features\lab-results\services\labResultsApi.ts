import { api, handleApiError } from '../../../lib/api';
import type { LabResult, LabResultFormData, LabResultFilters } from '../types';

export const labResultsApi = {
  async getAll(filters?: LabResultFilters): Promise<LabResult[]> {
    try {
      const params = new URLSearchParams();
      if (filters?.patientId) params.append('patientId', filters.patientId);
      if (filters?.testType) params.append('testType', filters.testType);
      if (filters?.status) params.append('status', filters.status);
      if (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);
      if (filters?.dateTo) params.append('dateTo', filters.dateTo);

      const response = await api.get(`/api/lab-results?${params.toString()}`);
      return response.data.data || [];
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async getById(id: string): Promise<LabResult> {
    try {
      const response = await api.get(`/api/lab-results/${id}`);
      return response.data.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async create(labResultData: LabResultFormData): Promise<LabResult> {
    try {
      // Transform form data to API format
      const apiData = {
        patientId: labResultData.patientId,
        testType: labResultData.testType,
        testDate: labResultData.testDate,
        orderedBy: labResultData.orderedBy,
        labName: labResultData.labName || undefined,
        results: labResultData.results,
        normalRanges: labResultData.normalRanges || undefined,
        flags: labResultData.flags || undefined,
        notes: labResultData.notes || undefined,
        status: labResultData.status || 'COMPLETED',
      };

      const response = await api.post('/api/lab-results', apiData);
      return response.data.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async update(id: string, updates: Partial<LabResultFormData>): Promise<LabResult> {
    try {
      // Transform form data to API format
      const apiData: any = {};
      if (updates.patientId) apiData.patientId = updates.patientId;
      if (updates.testType) apiData.testType = updates.testType;
      if (updates.testDate) apiData.testDate = updates.testDate;
      if (updates.orderedBy) apiData.orderedBy = updates.orderedBy;
      if (updates.labName !== undefined) apiData.labName = updates.labName || null;
      if (updates.results) apiData.results = updates.results;
      if (updates.normalRanges !== undefined) apiData.normalRanges = updates.normalRanges;
      if (updates.flags !== undefined) apiData.flags = updates.flags;
      if (updates.notes !== undefined) apiData.notes = updates.notes || null;
      if (updates.status) apiData.status = updates.status;

      const response = await api.put(`/api/lab-results/${id}`, apiData);
      return response.data.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async delete(id: string): Promise<void> {
    try {
      await api.delete(`/api/lab-results/${id}`);
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async getPatientLabResults(patientId: string, filters?: { testType?: string; limit?: number; dateFrom?: string; dateTo?: string }): Promise<LabResult[]> {
    try {
      const params = new URLSearchParams();
      if (filters?.testType) params.append('testType', filters.testType);
      if (filters?.limit) params.append('limit', filters.limit.toString());
      if (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);
      if (filters?.dateTo) params.append('dateTo', filters.dateTo);

      const response = await api.get(`/api/patients/${patientId}/lab-results?${params.toString()}`);
      return response.data.data || [];
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async getLabResultTrends(patientId: string, filters?: { testType?: string; dateFrom?: string; dateTo?: string }): Promise<any> {
    try {
      const params = new URLSearchParams();
      if (filters?.testType) params.append('testType', filters.testType);
      if (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);
      if (filters?.dateTo) params.append('dateTo', filters.dateTo);

      const response = await api.get(`/api/patients/${patientId}/lab-results/trends?${params.toString()}`);
      return response.data.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async exportCsv(): Promise<Blob> {
    try {
      const response = await api.get('/api/export/lab-results', {
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },
};
