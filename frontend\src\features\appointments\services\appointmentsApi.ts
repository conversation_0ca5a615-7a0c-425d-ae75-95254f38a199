import { api, handleApiError } from '../../../lib/api';
import type { Appointment, AppointmentFormData, AppointmentFilters } from '../types';

export const appointmentsApi = {
  async getAll(filters?: AppointmentFilters): Promise<Appointment[]> {
    try {
      const params = new URLSearchParams();
      if (filters?.patientId) params.append('patientId', filters.patientId);
      if (filters?.status) params.append('status', filters.status);
      if (filters?.type) params.append('type', filters.type);
      if (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);
      if (filters?.dateTo) params.append('dateTo', filters.dateTo);

      const response = await api.get(`/api/appointments?${params.toString()}`);
      return response.data.data || [];
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async getById(id: string): Promise<Appointment> {
    try {
      const response = await api.get(`/api/appointments/${id}`);
      return response.data.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async create(appointmentData: AppointmentFormData): Promise<Appointment> {
    try {
      // Convert 12-hour time format to 24-hour format
      const convertTo24Hour = (time12h: string): string => {
        const [time, modifier] = time12h.split(' ');
        let [hours, minutes] = time.split(':');
        if (hours === '12') {
          hours = '00';
        }
        if (modifier === 'PM') {
          hours = (parseInt(hours, 10) + 12).toString();
        }
        return `${hours.padStart(2, '0')}:${minutes}`;
      };

      // Get current user ID as provider (temporary solution)
      const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
      const providerId = currentUser.id; // Always use authenticated user ID

      const time24h = convertTo24Hour(appointmentData.time);
      
      // Transform form data to API format
      const apiData = {
        patientId: appointmentData.patientId,
        providerId: providerId,
        date: `${appointmentData.date}T${time24h}:00.000Z`,
        duration: parseInt(appointmentData.duration),
        type: appointmentData.type,
        status: appointmentData.status,
        notes: appointmentData.notes?.trim() || null,
      };

      const response = await api.post('/api/appointments', apiData);
      return response.data.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async update(id: string, updates: Partial<AppointmentFormData>): Promise<Appointment> {
    try {
      // Transform form data to API format
      const apiData: any = {};
      if (updates.patientId) apiData.patientId = updates.patientId;
      if (updates.date && updates.time) {
        apiData.date = `${updates.date}T${updates.time}:00.000Z`;
      }
      if (updates.duration) apiData.duration = parseInt(updates.duration);
      if (updates.type) apiData.type = updates.type;
      if (updates.status) apiData.status = updates.status;
      if (updates.notes !== undefined) apiData.notes = updates.notes?.trim() || null;

      const response = await api.put(`/api/appointments/${id}`, apiData);
      return response.data.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async delete(id: string): Promise<void> {
    try {
      await api.delete(`/api/appointments/${id}`);
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async cancel(id: string, reason?: string): Promise<Appointment> {
    try {
      const response = await api.put(`/api/appointments/${id}`, {
        status: 'CANCELLED',
        notes: reason ? `Cancelled: ${reason}` : 'Cancelled',
      });
      return response.data.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async getPatientAppointments(patientId: string): Promise<Appointment[]> {
    try {
      const response = await api.get(`/api/appointments?patientId=${patientId}`);
      return response.data.data || [];
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async exportCsv(): Promise<Blob> {
    try {
      const response = await api.get('/api/export/appointments', {
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },
};
