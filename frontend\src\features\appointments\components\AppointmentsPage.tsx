import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Plus, Calendar, Search, Filter, ChevronLeft, ChevronRight, Clock, Edit, CheckCircle, XCircle } from 'lucide-react';
import { useAppointments } from '../hooks/useAppointments';
import type { Appointment } from '../types';

// Group appointments by date
const groupAppointmentsByDate = (appointments: Appointment[]) => {
  const grouped: Record<string, Appointment[]> = {};

  appointments.forEach(appointment => {
    const dateKey = appointment.date.split('T')[0]; // Extract date part from ISO string
    if (!grouped[dateKey]) {
      grouped[dateKey] = [];
    }
    grouped[dateKey].push(appointment);
  });

  // Sort appointments by date within each date
  Object.keys(grouped).forEach(date => {
    grouped[date].sort((a: Appointment, b: Appointment) => {
      return new Date(a.date).getTime() - new Date(b.date).getTime();
    });
  });

  return grouped;
};

const AppointmentsPage: React.FC = () => {
  const [view, setView] = useState('calendar'); // 'calendar' or 'list'
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [filteredAppointments, setFilteredAppointments] = useState<Appointment[]>([]);
  const [groupedAppointments, setGroupedAppointments] = useState<Record<string, Appointment[]>>({});

  // Fetch appointments using the hook
  const { appointments, loading, error } = useAppointments();

  // Filter appointments
  useEffect(() => {
    if (!appointments) return;

    let filtered = [...appointments];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(appointment => {
        const patientName = appointment.patient ?
          `${appointment.patient.firstName} ${appointment.patient.lastName}` : '';
        const patientId = appointment.patient?.patientId || '';

        return patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
               patientId.toLowerCase().includes(searchTerm.toLowerCase()) ||
               appointment.type.toLowerCase().includes(searchTerm.toLowerCase());
      });
    }

    // Apply status filter
    if (statusFilter !== 'All') {
      filtered = filtered.filter(appointment => appointment.status === statusFilter);
    }

    setFilteredAppointments(filtered);
    setGroupedAppointments(groupAppointmentsByDate(filtered));
  }, [appointments, searchTerm, statusFilter]);

  // Get unique statuses for filter
  const statuses = ['All', ...new Set((appointments || []).map(a => a.status).filter(Boolean))];

  // Format date for display with error handling
  const formatDate = (dateString: string) => {
    try {
      if (!dateString) return 'Invalid date';

      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return 'Invalid date';
      }

      const options: Intl.DateTimeFormatOptions = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      };
      return date.toLocaleDateString(undefined, options);
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid date';
    }
  };

  // Navigate between dates
  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setDate(newDate.getDate() - 1);
    } else {
      newDate.setDate(newDate.getDate() + 1);
    }
    setCurrentDate(newDate);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Appointments</h1>
        <div className="flex space-x-2">
          <div className="flex rounded-md shadow-sm">
            <button
              type="button"
              className={`px-4 py-2 text-sm font-medium rounded-l-md ${
                view === 'calendar' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
              }`}
              onClick={() => setView('calendar')}
            >
              Calendar View
            </button>
            <button
              type="button"
              className={`px-4 py-2 text-sm font-medium rounded-r-md ${
                view === 'list' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
              }`}
              onClick={() => setView('list')}
            >
              List View
            </button>
          </div>
          <Link
            to="/appointments/new"
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
          >
            <Plus className="h-4 w-4 mr-2" />
            Schedule
          </Link>
        </div>
      </div>

      {/* Filters and search */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div className="flex flex-wrap gap-4">
          <div className="flex-1 min-w-[240px]">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search patient, ID or type..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Filter className="text-gray-400 h-4 w-4" />
            <select
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              {statuses.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Loading state */}
      {loading && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Loading appointments...</span>
          </div>
        </div>
      )}

      {/* Error state */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <XCircle className="h-5 w-5 text-red-400 mr-2" />
            <span className="text-red-800">Error loading appointments: {typeof error === 'string' ? error : 'Unknown error'}</span>
          </div>
        </div>
      )}

      {/* No appointments state */}
      {!loading && !error && appointments.length === 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="text-center">
            <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No appointments scheduled</h3>
            <p className="text-gray-600 mb-4">Get started by scheduling your first appointment.</p>
            <Link
              to="/appointments/new"
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors inline-flex items-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              Schedule Appointment
            </Link>
          </div>
        </div>
      )}

      {!loading && !error && appointments.length > 0 && view === 'calendar' ? (
        // Calendar view
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {/* Date navigation */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <button 
              onClick={() => navigateDate('prev')}
              className="p-2 rounded-full hover:bg-gray-100"
            >
              <ChevronLeft className="h-5 w-5 text-gray-500" />
            </button>
            <h2 className="text-lg font-semibold text-gray-900">
              {currentDate.toLocaleDateString(undefined, { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
            </h2>
            <button 
              onClick={() => navigateDate('next')}
              className="p-2 rounded-full hover:bg-gray-100"
            >
              <ChevronRight className="h-5 w-5 text-gray-500" />
            </button>
          </div>
          
          {/* Appointments for the day */}
          <div className="divide-y divide-gray-200">
            {Object.keys(groupedAppointments || {}).map(date => (
              <div key={date} className="p-4">
                <h3 className="text-md font-medium text-gray-900 mb-4">{formatDate(date)}</h3>
                <div className="space-y-3">
                  {(groupedAppointments[date] || []).map(appointment => (
                    <div 
                      key={appointment.id} 
                      className={`p-3 rounded-lg border ${
                        appointment.status === 'CONFIRMED'
                          ? 'border-green-200 bg-green-50'
                          : appointment.status === 'CANCELLED'
                            ? 'border-red-200 bg-red-50'
                            : 'border-yellow-200 bg-yellow-50'
                      }`}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 text-gray-500 mr-1" />
                            <span className="text-sm font-medium text-gray-900">
                              {new Date(appointment.date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} ({appointment.duration} min)
                            </span>
                          </div>
                          <h4 className="text-md font-semibold mt-1">
                            {appointment.patient ? `${appointment.patient.firstName} ${appointment.patient.lastName}` : 'Unknown Patient'}
                          </h4>
                          <p className="text-sm text-gray-500">{appointment.type}</p>
                          {appointment.notes && (
                            <p className="text-sm text-gray-600 mt-1 italic">"{appointment.notes}"</p>
                          )}
                        </div>
                        <div className="flex space-x-2">
                          <button className="p-1 text-blue-600 hover:text-blue-800 rounded-full hover:bg-blue-100">
                            <Edit className="h-4 w-4" />
                          </button>
                          {appointment.status !== 'CANCELLED' && (
                            <button className="p-1 text-red-600 hover:text-red-800 rounded-full hover:bg-red-100">
                              <XCircle className="h-4 w-4" />
                            </button>
                          )}
                          {appointment.status !== 'CONFIRMED' && (
                            <button className="p-1 text-green-600 hover:text-green-800 rounded-full hover:bg-green-100">
                              <CheckCircle className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      </div>
                      <div className="mt-2 flex justify-between items-center">
                        <span className="text-xs text-gray-500">
                          ID: {appointment.patient?.patientId || 'N/A'}
                        </span>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          appointment.status === 'CONFIRMED'
                            ? 'bg-green-100 text-green-800'
                            : appointment.status === 'CANCELLED'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {appointment.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
            
            {Object.keys(groupedAppointments).length === 0 && (
              <div className="p-8 text-center">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No appointments found</h3>
                <p className="text-gray-600">
                  There are no appointments matching your filters.
                </p>
              </div>
            )}
          </div>
        </div>
      ) : (
        // List view
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Patient
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date & Time
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Duration
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {(filteredAppointments || []).length > 0 ? (
                  (filteredAppointments || []).map((appointment) => (
                    <tr key={appointment.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {appointment.patient ? `${appointment.patient.firstName} ${appointment.patient.lastName}` : 'Unknown Patient'}
                            </div>
                            <div className="text-sm text-gray-500">{appointment.patient?.patientId || 'N/A'}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{new Date(appointment.date).toLocaleDateString()}</div>
                        <div className="text-sm text-gray-500">
                          {new Date(appointment.date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {appointment.type}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {appointment.duration} min
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          appointment.status === 'CONFIRMED'
                            ? 'bg-green-100 text-green-800'
                            : appointment.status === 'CANCELLED'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {appointment.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <button className="text-indigo-600 hover:text-indigo-900">
                            <Edit className="h-4 w-4" />
                          </button>
                          {appointment.status !== 'CANCELLED' && (
                            <button className="text-red-600 hover:text-red-900">
                              <XCircle className="h-4 w-4" />
                            </button>
                          )}
                          {appointment.status !== 'CONFIRMED' && (
                            <button className="text-green-600 hover:text-green-900">
                              <CheckCircle className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                      No appointments found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default AppointmentsPage;
