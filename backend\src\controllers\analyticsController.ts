import { Response, NextFunction } from 'express';
import { z } from 'zod';
import { AnalyticsService } from '@/services/analyticsService';
import { AuthRequest } from '@/types';

/**
 * Analytics controller handling HTTP requests for analytics and reporting
 */

// Validation schemas
const dateRangeSchema = z.object({
  from: z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid from date'),
  to: z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid to date'),
});

export class AnalyticsController {
  /**
   * Get dashboard analytics overview
   * GET /api/analytics/dashboard
   */
  static async getDashboardAnalytics(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
      }

      const result = await AnalyticsService.getDashboardAnalytics(
        req.user.id,
        req.user.role
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get patient analytics
   * GET /api/analytics/patients
   */
  static async getPatientAnalytics(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
      }

      const { from, to } = dateRangeSchema.parse(req.query);
      
      const result = await AnalyticsService.getPatientAnalytics(
        { from, to },
        req.user.id,
        req.user.role
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get appointment analytics
   * GET /api/analytics/appointments
   */
  static async getAppointmentAnalytics(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
      }

      const { from, to } = dateRangeSchema.parse(req.query);
      
      const result = await AnalyticsService.getAppointmentAnalytics(
        { from, to },
        req.user.id,
        req.user.role
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get lab result analytics
   * GET /api/analytics/lab-results
   */
  static async getLabResultAnalytics(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
      }

      const { from, to } = dateRangeSchema.parse(req.query);
      
      const result = await AnalyticsService.getLabResultAnalytics(
        { from, to },
        req.user.id,
        req.user.role
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get system analytics (admin only)
   * GET /api/analytics/system
   */
  static async getSystemAnalytics(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
      }

      const { from, to } = dateRangeSchema.parse(req.query);
      
      const result = await AnalyticsService.getSystemAnalytics(
        { from, to },
        req.user.id,
        req.user.role
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }
}
