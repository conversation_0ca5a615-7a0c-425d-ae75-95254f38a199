@echo off
echo ========================================
echo   Psychiatry App - Full Stack Startup
echo ========================================
echo.

echo Starting Backend Server...
cd backend
start "Backend Server" cmd /k "npm start"
echo Backend server starting on http://localhost:3002
echo.

echo Waiting 5 seconds for backend to initialize...
timeout /t 5 /nobreak > nul

echo Starting Frontend Development Server...
cd ..\frontend
start "Frontend Server" cmd /k "npm run dev"
echo Frontend server starting on http://localhost:5173
echo.

echo ========================================
echo   Application URLs:
echo ========================================
echo Frontend: http://localhost:5173
echo Backend API: http://localhost:3002/api
echo Backend Health: http://localhost:3002/health
echo Backend Stats: http://localhost:3002/api/stats
echo ========================================
echo.
echo Both servers are starting in separate windows.
echo Close this window when done.
echo.

timeout /t 3 /nobreak > nul
start http://localhost:5173

pause
