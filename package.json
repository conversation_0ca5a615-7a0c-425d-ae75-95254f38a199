{"name": "psychiatry-patient-management", "version": "1.0.0", "description": "HIPAA-compliant psychiatry patient management system", "private": true, "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "cd backend && npm start", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "db:generate": "cd backend && npx prisma generate", "db:push": "cd backend && npx prisma db push", "db:migrate": "cd backend && npx prisma migrate dev", "db:seed": "cd backend && npx prisma db seed", "db:studio": "cd backend && npx prisma studio"}, "devDependencies": {"concurrently": "^8.2.2", "prettier": "^3.1.1", "typescript": "^5.8.3"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}