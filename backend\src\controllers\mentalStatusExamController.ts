import { Request, Response, NextFunction } from 'express';
import { MentalStatusExamService } from '../services/mentalStatusExamService';
import { AuthRequest } from '../types';
import { createMentalStatusExamSchema, updateMentalStatusExamSchema } from '../utils/validation';

export class MentalStatusExamController {
  /**
   * Create a new mental status exam
   * POST /api/mental-status-exams
   */
  static async createMentalStatusExam(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const validatedData = createMentalStatusExamSchema.parse(req.body);

      const result = await MentalStatusExamService.createMentalStatusExam(
        validatedData,
        req.user!.id,
        { ipAddress: req.ip, userAgent: req.get('User-Agent') }
      );

      res.status(201).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all mental status exams
   * GET /api/mental-status-exams
   */
  static async getAllMentalStatusExams(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { patientId, examinerId, page = 1, limit = 10 } = req.query;

      const filters = {
        patientId: patientId as string,
        examinerId: examinerId as string,
      };

      const result = await MentalStatusExamService.getAllMentalStatusExams(
        filters,
        parseInt(page as string),
        parseInt(limit as string)
      );

      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get mental status exam by ID
   * GET /api/mental-status-exams/:id
   */
  static async getMentalStatusExamById(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      const result = await MentalStatusExamService.getMentalStatusExamById(id);

      if (!result.success) {
        res.status(404).json(result);
        return;
      }

      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get mental status exams by patient ID
   * GET /api/mental-status-exams/patient/:patientId
   */
  static async getMentalStatusExamsByPatient(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { patientId } = req.params;
      const { limit = 10 } = req.query;

      const result = await MentalStatusExamService.getMentalStatusExamsByPatient(
        patientId,
        parseInt(limit as string)
      );

      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update mental status exam
   * PUT /api/mental-status-exams/:id
   */
  static async updateMentalStatusExam(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const { id } = req.params;
      const validatedData = updateMentalStatusExamSchema.parse(req.body);

      const result = await MentalStatusExamService.updateMentalStatusExam(
        id,
        validatedData,
        req.user!.id,
        { ipAddress: req.ip, userAgent: req.get('User-Agent') }
      );

      if (!result.success) {
        res.status(404).json(result);
        return;
      }

      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete mental status exam
   * DELETE /api/mental-status-exams/:id
   */
  static async deleteMentalStatusExam(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const { id } = req.params;

      const result = await MentalStatusExamService.deleteMentalStatusExam(
        id,
        req.user!.id,
        { ipAddress: req.ip, userAgent: req.get('User-Agent') }
      );

      if (!result.success) {
        res.status(404).json(result);
        return;
      }

      res.json(result);
    } catch (error) {
      next(error);
    }
  }
}
