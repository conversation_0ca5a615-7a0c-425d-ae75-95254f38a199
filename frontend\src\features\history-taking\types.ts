// DSM-5-TR History Taking Types

export interface Criterion {
  id: string;
  code: string; // e.g., "A1", "B2", etc.
  description: string;
  present: boolean;
  severity?: number; // 1-4 scale (1=mild, 2=moderate, 3=severe, 4=extreme)
  duration?: string;
  onset?: string;
  comments?: string;
  subCriteria?: Criterion[];
}

export interface Specifier {
  id: string;
  name: string;
  description: string;
  selected: boolean;
  mutuallyExclusive?: string[]; // IDs of other specifiers that can't be selected together
}

export interface DisorderAssessment {
  id: string;
  disorderId: string;
  patientId: string;
  assessmentDate: string;
  criteria: Criterion[];
  specifiers: Specifier[];
  severity: 'mild' | 'moderate' | 'severe' | 'unspecified';
  courseSpecifiers: string[];
  diagnosticConfidence: 'provisional' | 'principal' | 'rule-out' | 'confirmed';
  functionalImpairment: {
    social: number; // 1-5 scale
    occupational: number;
    academic: number;
    overall: number;
  };
  notes: string;
  assessorId: string;
  status: 'draft' | 'completed' | 'reviewed';
  createdAt: string;
  updatedAt: string;
}

export interface Disorder {
  id: string;
  code: string; // DSM-5-TR code (e.g., "296.23")
  name: string;
  category: DisorderCategory;
  subcategory?: string;
  description: string;
  criteria: CriterionTemplate[];
  specifiers: SpecifierTemplate[];
  diagnosticFeatures: string[];
  prevalence?: string;
  developmentAndCourse?: string;
  riskAndPrognosticFactors?: string[];
  differentialDiagnosis?: string[];
  comorbidity?: string[];
  minimumCriteria?: number; // Minimum number of criteria that must be met
  durationRequirement?: string;
  exclusionCriteria?: string[];
}

export interface CriterionTemplate {
  id: string;
  code: string;
  description: string;
  required: boolean;
  type: 'symptom' | 'duration' | 'impairment' | 'exclusion';
  options?: string[]; // For multiple choice criteria
  subCriteria?: CriterionTemplate[];
  validationRules?: ValidationRule[];
}

export interface SpecifierTemplate {
  id: string;
  name: string;
  description: string;
  type: 'severity' | 'course' | 'features' | 'context';
  options: string[];
  required: boolean;
  mutuallyExclusive?: string[];
}

export interface ValidationRule {
  type: 'required' | 'minimum' | 'maximum' | 'dependency';
  value?: number;
  dependsOn?: string[]; // Criterion IDs this rule depends on
  message: string;
}

export type DisorderCategory = 
  | 'neurodevelopmental'
  | 'schizophrenia-spectrum'
  | 'bipolar-related'
  | 'depressive'
  | 'anxiety'
  | 'obsessive-compulsive'
  | 'trauma-stressor'
  | 'dissociative'
  | 'somatic-symptom'
  | 'feeding-eating'
  | 'elimination'
  | 'sleep-wake'
  | 'sexual-dysfunctions'
  | 'gender-dysphoria'
  | 'disruptive-impulse'
  | 'substance-related'
  | 'neurocognitive'
  | 'personality'
  | 'paraphilic'
  | 'other-mental'
  | 'medication-induced'
  | 'other-conditions';

export interface AssessmentSession {
  id?: string;
  patientId: string;
  sessionDate: string;
  assessments: DisorderAssessment[];
  clinicalImpression?: string;
  treatmentRecommendations?: string[];
  followUpPlan?: string;
  riskAssessment: {
    suicidalIdeation: boolean;
    homicidalIdeation: boolean;
    selfHarm: boolean;
    substanceUse: boolean;
    level: 'low' | 'moderate' | 'high' | 'imminent';
    interventions?: string[];
  };
  status: 'in-progress' | 'completed' | 'reviewed' | 'signed';
  duration: number; // in minutes
  assessorId?: string;
  supervisorId?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface AssessmentProgress {
  totalCriteria: number;
  completedCriteria: number;
  requiredCriteria: number;
  metCriteria: number;
  percentComplete: number;
  isValid: boolean;
  canDiagnose: boolean;
  missingRequired: string[];
}

export interface DiagnosticSuggestion {
  disorderId: string;
  confidence: number; // 0-1
  metCriteria: string[];
  missingCriteria: string[];
  reasoning: string;
  alternativeDiagnoses: string[];
}

// Form state for assessment
export interface AssessmentFormState {
  currentStep: number;
  selectedDisorders: string[];
  assessments: Record<string, Partial<DisorderAssessment>>;
  globalNotes: string;
  riskFactors: string[];
  isValid: boolean;
  errors: Record<string, string>;
}

// Assessment workflow steps
export interface AssessmentStep {
  id: string;
  title: string;
  description: string;
  component: string;
  required: boolean;
  completed: boolean;
  data?: any;
}

export interface AssessmentWorkflow {
  id: string;
  name: string;
  description: string;
  steps: AssessmentStep[];
  currentStepIndex: number;
  canProceed: boolean;
  canGoBack: boolean;
}

// Search and filtering
export interface DisorderFilter {
  category?: DisorderCategory;
  search?: string;
  hasSymptoms?: string[];
  excludeSymptoms?: string[];
}

export interface AssessmentFilter {
  patientId?: string;
  assessorId?: string;
  dateFrom?: string;
  dateTo?: string;
  status?: string;
  disorders?: string[];
}
