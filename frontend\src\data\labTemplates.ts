export interface LabParameter {
  name: string;
  key: string;
  unit: string;
  normalRange: {
    min?: number;
    max?: number;
    reference: string;
  };
  type: 'number' | 'text' | 'select';
  options?: string[];
  required: boolean;
}

export interface LabTemplate {
  testType: string;
  displayName: string;
  category: string;
  description: string;
  parameters: LabParameter[];
}

export const LAB_TEMPLATES: LabTemplate[] = [
  {
    testType: 'CBC',
    displayName: 'Complete Blood Count (CBC)',
    category: 'Hematology',
    description: 'Comprehensive blood cell analysis including white blood cells, red blood cells, and platelets',
    parameters: [
      {
        name: 'White Blood Cells (WBC)',
        key: 'wbc',
        unit: 'K/uL',
        normalRange: { min: 4.0, max: 11.0, reference: '4.0-11.0 K/uL' },
        type: 'number',
        required: true
      },
      {
        name: 'Red Blood Cells (RBC)',
        key: 'rbc',
        unit: 'M/uL',
        normalRange: { min: 4.2, max: 5.4, reference: '4.2-5.4 M/uL' },
        type: 'number',
        required: true
      },
      {
        name: 'Hemoglobin (Hb)',
        key: 'hemoglobin',
        unit: 'g/dL',
        normalRange: { min: 12.0, max: 16.0, reference: '12.0-16.0 g/dL' },
        type: 'number',
        required: true
      },
      {
        name: 'Hematocrit (Hct)',
        key: 'hematocrit',
        unit: '%',
        normalRange: { min: 36.0, max: 46.0, reference: '36.0-46.0%' },
        type: 'number',
        required: true
      },
      {
        name: 'Platelets (PLT)',
        key: 'platelets',
        unit: 'K/uL',
        normalRange: { min: 150, max: 450, reference: '150-450 K/uL' },
        type: 'number',
        required: true
      },
      {
        name: 'Neutrophils',
        key: 'neutrophils',
        unit: '%',
        normalRange: { min: 50, max: 70, reference: '50-70%' },
        type: 'number',
        required: false
      },
      {
        name: 'Lymphocytes',
        key: 'lymphocytes',
        unit: '%',
        normalRange: { min: 20, max: 40, reference: '20-40%' },
        type: 'number',
        required: false
      },
      {
        name: 'Monocytes',
        key: 'monocytes',
        unit: '%',
        normalRange: { min: 2, max: 8, reference: '2-8%' },
        type: 'number',
        required: false
      },
      {
        name: 'Eosinophils',
        key: 'eosinophils',
        unit: '%',
        normalRange: { min: 1, max: 4, reference: '1-4%' },
        type: 'number',
        required: false
      },
      {
        name: 'Basophils',
        key: 'basophils',
        unit: '%',
        normalRange: { min: 0, max: 2, reference: '0-2%' },
        type: 'number',
        required: false
      }
    ]
  },
  {
    testType: 'METABOLIC_PANEL',
    displayName: 'Basic Metabolic Panel (BMP)',
    category: 'Chemistry',
    description: 'Basic metabolic panel including glucose, electrolytes, and kidney function markers',
    parameters: [
      {
        name: 'Glucose',
        key: 'glucose',
        unit: 'mg/dL',
        normalRange: { min: 70, max: 100, reference: '70-100 mg/dL (fasting)' },
        type: 'number',
        required: true
      },
      {
        name: 'Sodium (Na)',
        key: 'sodium',
        unit: 'mEq/L',
        normalRange: { min: 136, max: 145, reference: '136-145 mEq/L' },
        type: 'number',
        required: true
      },
      {
        name: 'Potassium (K)',
        key: 'potassium',
        unit: 'mEq/L',
        normalRange: { min: 3.5, max: 5.1, reference: '3.5-5.1 mEq/L' },
        type: 'number',
        required: true
      },
      {
        name: 'Chloride (Cl)',
        key: 'chloride',
        unit: 'mEq/L',
        normalRange: { min: 98, max: 107, reference: '98-107 mEq/L' },
        type: 'number',
        required: true
      },
      {
        name: 'BUN (Blood Urea Nitrogen)',
        key: 'bun',
        unit: 'mg/dL',
        normalRange: { min: 7, max: 20, reference: '7-20 mg/dL' },
        type: 'number',
        required: true
      },
      {
        name: 'Creatinine',
        key: 'creatinine',
        unit: 'mg/dL',
        normalRange: { min: 0.6, max: 1.2, reference: '0.6-1.2 mg/dL' },
        type: 'number',
        required: true
      },
      {
        name: 'CO2 (Carbon Dioxide)',
        key: 'co2',
        unit: 'mEq/L',
        normalRange: { min: 22, max: 28, reference: '22-28 mEq/L' },
        type: 'number',
        required: false
      }
    ]
  },
  {
    testType: 'LIPID_PANEL',
    displayName: 'Lipid Panel',
    category: 'Chemistry',
    description: 'Cholesterol and lipid analysis for cardiovascular risk assessment',
    parameters: [
      {
        name: 'Total Cholesterol',
        key: 'totalCholesterol',
        unit: 'mg/dL',
        normalRange: { max: 200, reference: '<200 mg/dL' },
        type: 'number',
        required: true
      },
      {
        name: 'LDL Cholesterol',
        key: 'ldlCholesterol',
        unit: 'mg/dL',
        normalRange: { max: 100, reference: '<100 mg/dL' },
        type: 'number',
        required: true
      },
      {
        name: 'HDL Cholesterol',
        key: 'hdlCholesterol',
        unit: 'mg/dL',
        normalRange: { min: 40, reference: '>40 mg/dL (men), >50 mg/dL (women)' },
        type: 'number',
        required: true
      },
      {
        name: 'Triglycerides',
        key: 'triglycerides',
        unit: 'mg/dL',
        normalRange: { max: 150, reference: '<150 mg/dL' },
        type: 'number',
        required: true
      }
    ]
  },
  {
    testType: 'THYROID',
    displayName: 'Thyroid Function Panel',
    category: 'Endocrinology',
    description: 'Thyroid hormone levels and function assessment',
    parameters: [
      {
        name: 'TSH (Thyroid Stimulating Hormone)',
        key: 'tsh',
        unit: 'mIU/L',
        normalRange: { min: 0.4, max: 4.0, reference: '0.4-4.0 mIU/L' },
        type: 'number',
        required: true
      },
      {
        name: 'Free T4 (Thyroxine)',
        key: 'freeT4',
        unit: 'ng/dL',
        normalRange: { min: 0.8, max: 1.8, reference: '0.8-1.8 ng/dL' },
        type: 'number',
        required: false
      },
      {
        name: 'Free T3 (Triiodothyronine)',
        key: 'freeT3',
        unit: 'pg/mL',
        normalRange: { min: 2.3, max: 4.2, reference: '2.3-4.2 pg/mL' },
        type: 'number',
        required: false
      }
    ]
  },
  {
    testType: 'LIVER_FUNCTION',
    displayName: 'Liver Function Tests (LFTs)',
    category: 'Chemistry',
    description: 'Liver enzyme and function assessment',
    parameters: [
      {
        name: 'ALT (Alanine Aminotransferase)',
        key: 'alt',
        unit: 'U/L',
        normalRange: { min: 7, max: 56, reference: '7-56 U/L' },
        type: 'number',
        required: true
      },
      {
        name: 'AST (Aspartate Aminotransferase)',
        key: 'ast',
        unit: 'U/L',
        normalRange: { min: 10, max: 40, reference: '10-40 U/L' },
        type: 'number',
        required: true
      },
      {
        name: 'Total Bilirubin',
        key: 'totalBilirubin',
        unit: 'mg/dL',
        normalRange: { min: 0.2, max: 1.2, reference: '0.2-1.2 mg/dL' },
        type: 'number',
        required: true
      },
      {
        name: 'Alkaline Phosphatase',
        key: 'alkalinePhosphatase',
        unit: 'U/L',
        normalRange: { min: 44, max: 147, reference: '44-147 U/L' },
        type: 'number',
        required: false
      },
      {
        name: 'Albumin',
        key: 'albumin',
        unit: 'g/dL',
        normalRange: { min: 3.5, max: 5.0, reference: '3.5-5.0 g/dL' },
        type: 'number',
        required: false
      }
    ]
  }
];

export const getLabTemplate = (testType: string): LabTemplate | undefined => {
  return LAB_TEMPLATES.find(template => template.testType === testType);
};

export const getLabCategories = (): string[] => {
  return [...new Set(LAB_TEMPLATES.map(template => template.category))];
};

export const getTestTypesByCategory = (category: string): LabTemplate[] => {
  return LAB_TEMPLATES.filter(template => template.category === category);
};
