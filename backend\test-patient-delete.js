const axios = require('axios');

async function testPatientDelete() {
  try {
    // First, let's create a test patient to delete
    console.log('Creating a test patient...');
    const createResponse = await axios.post('http://localhost:3002/api/patients', {
      firstName: 'Test',
      lastName: 'DeleteMe',
      dateOfBirth: '1990-01-01',
      gender: 'MALE',
      phone: '555-0000',
      email: '<EMAIL>'
    });

    const testPatientId = createResponse.data.data.id;
    console.log('Created test patient with ID:', testPatientId);

    // Now test the delete functionality
    console.log('Testing patient delete API...');
    const deleteResponse = await axios.delete(`http://localhost:3002/api/patients/${testPatientId}`);

    console.log('Delete Success! Response status:', deleteResponse.status);
    console.log('Response:', deleteResponse.data);

    // Verify the patient is soft deleted by trying to fetch it
    console.log('Verifying patient is deleted...');
    try {
      await axios.get(`http://localhost:3002/api/patients/${testPatientId}`);
      console.log('ERROR: Patient should not be accessible after deletion!');
    } catch (error) {
      if (error.response && error.response.status === 404) {
        console.log('SUCCESS: Patient correctly returns 404 after deletion');
      } else {
        console.log('Unexpected error:', error.message);
      }
    }

  } catch (error) {
    console.error('Error testing patient delete:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Message:', error.message);
    }
  }
}

testPatientDelete();
