@echo off
echo ========================================
echo   Psychiatry App Backend Server
echo ========================================
echo.

cd backend

echo Checking database...
npx prisma db push --accept-data-loss
if errorlevel 1 (
    echo ERROR: Database setup failed
    pause
    exit /b 1
)

echo.
echo Starting backend server...
echo Server will be available at: http://localhost:3002
echo Health check: http://localhost:3002/health
echo API documentation: http://localhost:3002/api
echo Database stats: http://localhost:3002/api/stats
echo.
echo Press Ctrl+C to stop the server
echo.

npm start
