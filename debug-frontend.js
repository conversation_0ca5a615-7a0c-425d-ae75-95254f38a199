const { chromium } = require('playwright');

(async () => {
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  // Listen for console messages
  page.on('console', msg => {
    console.log(`CONSOLE ${msg.type()}: ${msg.text()}`);
  });
  
  // Listen for page errors
  page.on('pageerror', error => {
    console.log(`PAGE ERROR: ${error.message}`);
  });
  
  // Listen for network responses
  page.on('response', response => {
    if (response.status() >= 400) {
      console.log(`NETWORK ERROR: ${response.status()} - ${response.url()}`);
    }
  });
  
  try {
    console.log('Navigating to http://localhost:5173...');
    await page.goto('http://localhost:5173');
    
    console.log('Waiting for load state...');
    await page.waitForLoadState('networkidle', { timeout: 10000 });
    
    console.log('Checking page content...');
    const title = await page.title();
    console.log(`Page title: ${title}`);
    
    const bodyVisible = await page.locator('body').isVisible();
    console.log(`Body visible: ${bodyVisible}`);
    
    const bodyContent = await page.locator('body').innerHTML();
    console.log(`Body content length: ${bodyContent.length}`);
    console.log(`Body content preview: ${bodyContent.substring(0, 500)}...`);
    
    // Check for React root
    const reactRoot = await page.locator('#root').count();
    console.log(`React root elements: ${reactRoot}`);
    
    if (reactRoot > 0) {
      const rootContent = await page.locator('#root').innerHTML();
      console.log(`Root content length: ${rootContent.length}`);
      console.log(`Root content preview: ${rootContent.substring(0, 500)}...`);
    }
    
  } catch (error) {
    console.error('Error during debugging:', error);
  }
  
  console.log('Keeping browser open for manual inspection...');
  // Keep browser open for manual inspection
  // await browser.close();
})();
