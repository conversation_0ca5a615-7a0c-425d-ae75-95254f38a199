import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import { verifyAccessToken, extractTokenFromHeader } from '@/utils/jwt';
import { AuthenticationError, AuthorizationError } from '@/utils/errors';
import { AuthRequest, AuthenticatedUser } from '@/types';
import { logger } from '@/utils/logger';

const prisma = new PrismaClient();

/**
 * Authentication middleware to verify JWT tokens
 * Adds user information to request object if token is valid
 */
export const authenticate = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    logger.info('Authentication attempt', {
      method: req.method,
      url: req.url,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    const token = extractTokenFromHeader(req.headers.authorization);

    if (!token) {
      logger.warn('Authentication failed: No token provided', {
        method: req.method,
        url: req.url,
        ip: req.ip,
      });
      throw new AuthenticationError('Access token required');
    }

    // Verify the token
    const payload = verifyAccessToken(token);
    logger.info('Token verified successfully', { userId: payload.userId });

    // Get user from database to ensure they still exist and are active
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        firstName: true,
        lastName: true,
        isActive: true,
        lockedUntil: true,
      },
    });

    if (!user) {
      logger.warn('Authentication failed: User not found', { userId: payload.userId });
      throw new AuthenticationError('User not found');
    }

    if (!user.isActive) {
      logger.warn('Authentication failed: Account deactivated', {
        userId: user.id,
        username: user.username
      });
      throw new AuthenticationError('Account is deactivated');
    }

    if (user.lockedUntil && user.lockedUntil > new Date()) {
      logger.warn('Authentication failed: Account locked', {
        userId: user.id,
        username: user.username,
        lockedUntil: user.lockedUntil
      });
      throw new AuthenticationError('Account is temporarily locked');
    }

    // Add user to request object
    req.user = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role as 'ADMIN' | 'CLINICIAN' | 'STAFF',
      firstName: user.firstName,
      lastName: user.lastName,
    };

    logger.info('Authentication successful', {
      userId: user.id,
      username: user.username,
      role: user.role,
    });

    next();
  } catch (error) {
    logger.error('Authentication error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      method: req.method,
      url: req.url,
      ip: req.ip,
    });
    next(error);
  }
};

/**
 * Authorization middleware to check user roles
 * Must be used after authenticate middleware
 */
export const authorize = (allowedRoles: ('ADMIN' | 'CLINICIAN' | 'STAFF')[]) => {
  return (req: AuthRequest, res: Response, next: NextFunction): void => {
    try {
      logger.info('Authorization check', {
        method: req.method,
        url: req.url,
        requiredRoles: allowedRoles,
        userRole: req.user?.role,
        userId: req.user?.id,
      });

      if (!req.user) {
        logger.warn('Authorization failed: No authenticated user', {
          method: req.method,
          url: req.url,
          requiredRoles: allowedRoles,
        });
        throw new AuthenticationError('Authentication required');
      }

      if (!allowedRoles.includes(req.user.role)) {
        logger.warn('Authorization failed: Insufficient permissions', {
          method: req.method,
          url: req.url,
          requiredRoles: allowedRoles,
          userRole: req.user.role,
          userId: req.user.id,
          username: req.user.username,
        });
        throw new AuthorizationError(
          `Access denied. Required roles: ${allowedRoles.join(', ')}`
        );
      }

      logger.info('Authorization successful', {
        method: req.method,
        url: req.url,
        userRole: req.user.role,
        userId: req.user.id,
        username: req.user.username,
      });

      next();
    } catch (error) {
      logger.error('Authorization error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        method: req.method,
        url: req.url,
        requiredRoles: allowedRoles,
        userRole: req.user?.role,
        userId: req.user?.id,
      });
      next(error);
    }
  };
};

/**
 * Optional authentication middleware
 * Adds user information if token is present and valid, but doesn't require it
 */
export const optionalAuth = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = extractTokenFromHeader(req.headers.authorization);
    
    if (token) {
      try {
        const payload = verifyAccessToken(token);
        
        const user = await prisma.user.findUnique({
          where: { id: payload.userId },
          select: {
            id: true,
            username: true,
            email: true,
            role: true,
            firstName: true,
            lastName: true,
            isActive: true,
            lockedUntil: true,
          },
        });

        if (user && user.isActive && (!user.lockedUntil || user.lockedUntil <= new Date())) {
          req.user = {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role as 'ADMIN' | 'CLINICIAN' | 'STAFF',
            firstName: user.firstName,
            lastName: user.lastName,
          };
        }
      } catch {
        // Ignore token errors for optional auth
      }
    }

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Middleware to check if user can access specific patient data
 * Clinicians can only access their own patients, admins can access all
 */
export const authorizePatientAccess = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user) {
      throw new AuthenticationError('Authentication required');
    }

    // Admins can access all patients
    if (req.user!.role === 'ADMIN') {
      return next();
    }

    const patientId = req.params.id || req.params.patientId || req.body.patientId;
    
    if (!patientId) {
      throw new AuthorizationError('Patient ID required');
    }

    // Check if the patient was created by this user or if user is admin
    const patient = await prisma.patient.findUnique({
      where: { id: patientId },
      select: { createdBy: true },
    });

    if (!patient) {
      throw new AuthorizationError('Patient not found');
    }

    if (patient.createdBy !== req.user!.id && !['ADMIN'].includes(req.user!.role)) {
      throw new AuthorizationError('Access denied to this patient record');
    }

    next();
  } catch (error) {
    next(error);
  }
};
