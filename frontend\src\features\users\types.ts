export interface User {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'ADMIN' | 'CLINICIAN' | 'STAFF';
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
}

export interface UserFormData {
  username: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role: 'ADMIN' | 'CLINICIAN' | 'STAFF';
}

export interface UserFilters {
  search?: string;
  role?: 'ADMIN' | 'CLINICIAN' | 'STAFF';
  isActive?: boolean;
}
