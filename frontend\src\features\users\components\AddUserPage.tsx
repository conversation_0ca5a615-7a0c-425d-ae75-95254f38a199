import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import type { UserFormData } from '../types';
import { useUsers } from '../hooks/useUsers';
import UserForm from './UserForm';
import { useToast } from '../../../components/ui/Toast';

const AddUserPage: React.FC = () => {
  const navigate = useNavigate();
  const { createUser, isCreating } = useUsers();
  const { success, error: showError } = useToast();
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (formData: UserFormData) => {
    setError(null);

    try {
      const newUser = await createUser(formData);
      success('Staff Member Created', `${newUser.firstName} ${newUser.lastName} has been successfully added.`);
      navigate('/users');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create staff member';
      setError(errorMessage);
      showError('Failed to Create Staff Member', errorMessage);
    }
  };

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            to="/users"
            className="text-gray-600 hover:text-gray-800 flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Staff
          </Link>
        </div>
      </div>

      <div>
        <h1 className="text-3xl font-bold text-gray-900">Add Staff Member</h1>
        <p className="text-gray-600">Create a new staff member account</p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <UserForm
          onSubmit={handleSubmit}
          loading={isCreating}
          submitButtonText={isCreating ? 'Creating...' : 'Create Staff Member'}
        />
      </div>
    </div>
  );
};

export default AddUserPage;
