import React, { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Search, Brain, ArrowRight, User } from 'lucide-react';

// Mock data for patients
const mockPatients = [
  { 
    id: 'P12345', 
    name: '<PERSON>', 
    age: 42, 
    gender: 'Male',
    diagnosis: 'Major Depressive Disorder',
    lastAssessment: '2023-05-10'
  },
  { 
    id: 'P12346', 
    name: '<PERSON>', 
    age: 29, 
    gender: 'Female',
    diagnosis: 'Generalized Anxiety Disorder',
    lastAssessment: '2023-05-15'
  },
  { 
    id: 'P12347', 
    name: '<PERSON>', 
    age: 35, 
    gender: 'Male',
    diagnosis: 'Bipolar Disorder',
    lastAssessment: '2023-04-22'
  },
  { 
    id: 'P12348', 
    name: '<PERSON>', 
    age: 31, 
    gender: 'Female',
    diagnosis: 'PTSD',
    lastAssessment: '2023-05-05'
  },
  { 
    id: 'P12349', 
    name: '<PERSON>', 
    age: 45, 
    gender: 'Male',
    diagnosis: 'Schizophrenia',
    lastAssessment: '2023-05-18'
  }
];

const PatientSelectionPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [patients, setPatients] = useState(mockPatients);

  // Filter patients based on search term
  useEffect(() => {
    if (!searchTerm) {
      setPatients(mockPatients);
      return;
    }

    const filtered = mockPatients.filter(patient => 
      patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.diagnosis.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    setPatients(filtered);
  }, [searchTerm]);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Psychiatric Assessment</h1>
          <p className="text-gray-600">Select a patient to begin assessment</p>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Search patients by name, ID or diagnosis..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* Patient List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <h2 className="font-semibold text-gray-800 flex items-center">
            <Brain className="h-5 w-5 mr-2 text-purple-600" />
            Patients for Psychiatric Assessment
          </h2>
        </div>
        
        <div className="divide-y divide-gray-200">
          {patients.length > 0 ? (
            patients.map(patient => (
              <div key={patient.id} className="p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-start space-x-4">
                    <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <User className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">{patient.name}</h3>
                      <div className="text-sm text-gray-500 space-x-2">
                        <span>{patient.id}</span>
                        <span>•</span>
                        <span>{patient.age} years</span>
                        <span>•</span>
                        <span>{patient.gender}</span>
                      </div>
                      <div className="mt-1">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                          {patient.diagnosis}
                        </span>
                      </div>
                      {patient.lastAssessment && (
                        <div className="text-xs text-gray-500 mt-1">
                          Last assessment: {new Date(patient.lastAssessment).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                  </div>
                  <Link
                    to={`/history-taking/${patient.id}`}
                    className="flex items-center text-blue-600 hover:text-blue-800"
                  >
                    Begin Assessment
                    <ArrowRight className="h-4 w-4 ml-1" />
                  </Link>
                </div>
              </div>
            ))
          ) : (
            <div className="p-8 text-center text-gray-500">
              No patients found matching your search criteria.
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PatientSelectionPage;