{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@tanstack/react-query": "^5.81.2", "@tanstack/react-query-devtools": "^5.81.2", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^2.30.0", "lucide-react": "^0.515.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.0", "react-router-dom": "^7.6.2", "recharts": "^2.15.3", "zod": "^3.25.64", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.0.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.19", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jsdom": "^26.1.0", "postcss": "^8.4.38", "rollup": "^4.43.0", "tailwindcss": "^3.4.4", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vitest": "^3.2.4"}}