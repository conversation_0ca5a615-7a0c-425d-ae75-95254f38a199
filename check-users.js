const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkUsers() {
  try {
    console.log('Checking users in database...');
    
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        firstName: true,
        lastName: true,
        isActive: true,
        createdAt: true
      }
    });
    
    console.log('Found users:', JSON.stringify(users, null, 2));
    
    // Check patients
    const patients = await prisma.patient.findMany({
      select: {
        id: true,
        patientId: true,
        firstName: true,
        lastName: true,
        isDeleted: true,
        createdAt: true
      }
    });
    
    console.log('Found patients:', JSON.stringify(patients, null, 2));
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUsers();