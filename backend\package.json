{"name": "psychiatry-backend", "version": "1.0.0", "description": "Backend for Psychiatry Patient Management System", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node working-server.js", "dev": "node working-server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testPathPattern=integration", "test:unit": "jest --testPathPattern=unit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "keywords": ["psychiatry", "patient-management", "healthcare"], "author": "Your Name", "license": "MIT", "dependencies": {"@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.9.0", "@types/uuid": "^10.0.0", "axios": "^1.10.0", "bcrypt": "^6.0.0", "cors": "^2.8.5", "date-fns": "^2.30.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.515.0", "morgan": "^1.10.0", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "prisma": "^6.9.0", "react-hook-form": "^7.58.0", "react-router-dom": "^7.6.2", "recharts": "^2.15.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^11.1.0", "winston": "^3.17.0", "zod": "^3.25.64"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^24.0.1", "@types/supertest": "^6.0.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "autoprefixer": "^10.4.21", "get-tsconfig": "^4.10.1", "jest": "^29.7.0", "nodemon": "^3.1.10", "postcss": "^8.5.5", "supertest": "^7.1.3", "tailwindcss": "^4.1.10", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.8.3", "vitest": "^3.2.4"}}