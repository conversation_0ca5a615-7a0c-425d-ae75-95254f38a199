/**
 * Custom error classes for better error handling and API responses
 */

export class AppError extends Error {
  public readonly statusCode: number;
  public readonly isOperational: boolean;

  constructor(message: string, statusCode: number, isOperational: boolean = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    // Maintains proper stack trace for where our error was thrown
    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  public readonly field?: string;
  public readonly value?: unknown;

  constructor(message: string, field?: string, value?: unknown) {
    super(message, 400);
    this.field = field;
    this.value = value;
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication failed') {
    super(message, 401);
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Access denied') {
    super(message, 403);
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 404);
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Resource conflict') {
    super(message, 409);
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Too many requests') {
    super(message, 429);
  }
}

export class ServerError extends AppError {
  constructor(message: string = 'Internal server error') {
    super(message, 500, false);
  }
}

/**
 * Error response formatter for consistent API responses
 */
export const formatErrorResponse = (error: Error) => {
  if (error instanceof AppError) {
    return {
      success: false,
      error: error.message,
      statusCode: error.statusCode,
      ...(error instanceof ValidationError && {
        field: error.field,
        value: error.value,
      }),
    };
  }

  // Handle Prisma errors
  if (error.name === 'PrismaClientKnownRequestError') {
    const prismaError = error as any;
    
    switch (prismaError.code) {
      case 'P2002':
        return {
          success: false,
          error: 'A record with this information already exists',
          statusCode: 409,
        };
      case 'P2025':
        return {
          success: false,
          error: 'Record not found',
          statusCode: 404,
        };
      default:
        return {
          success: false,
          error: 'Database operation failed',
          statusCode: 500,
        };
    }
  }

  // Handle validation errors from Zod or Joi
  if (error.name === 'ZodError' || error.name === 'ValidationError') {
    return {
      success: false,
      error: 'Validation failed',
      statusCode: 400,
      details: error.message,
    };
  }

  // Default error response
  return {
    success: false,
    error: process.env.NODE_ENV === 'production' 
      ? 'Internal server error' 
      : error.message,
    statusCode: 500,
  };
};
