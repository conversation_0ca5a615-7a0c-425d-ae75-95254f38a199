import { Request, Response, NextFunction } from 'express';
import { MedicationHistoryService } from '../services/medicationHistoryService';
import { AuthRequest } from '../types';
import { createMedicationHistorySchema, updateMedicationHistorySchema } from '../utils/validation';

export class MedicationHistoryController {
  /**
   * Create a new medication history entry
   * POST /api/medication-history
   */
  static async createMedicationHistory(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const validatedData = createMedicationHistorySchema.parse(req.body);

      const result = await MedicationHistoryService.createMedicationHistory(
        validatedData,
        req.user!.id,
        { ipAddress: req.ip, userAgent: req.get('User-Agent') }
      );

      res.status(201).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all medication history entries
   * GET /api/medication-history
   */
  static async getAllMedicationHistory(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { patientId, medicationName, isActive, page = 1, limit = 10 } = req.query;

      const filters = {
        patientId: patientId as string,
        medicationName: medicationName as string,
        isActive: isActive === 'true' ? true : isActive === 'false' ? false : undefined,
      };

      const result = await MedicationHistoryService.getAllMedicationHistory(
        filters,
        parseInt(page as string),
        parseInt(limit as string)
      );

      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get medication history by ID
   * GET /api/medication-history/:id
   */
  static async getMedicationHistoryById(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      const result = await MedicationHistoryService.getMedicationHistoryById(id);

      if (!result.success) {
        res.status(404).json(result);
        return;
      }

      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get medication history by patient ID
   * GET /api/medication-history/patient/:patientId
   */
  static async getMedicationHistoryByPatient(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { patientId } = req.params;
      const { isActive, limit = 20 } = req.query;

      const result = await MedicationHistoryService.getMedicationHistoryByPatient(
        patientId,
        isActive === 'true' ? true : isActive === 'false' ? false : undefined,
        parseInt(limit as string)
      );

      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update medication history
   * PUT /api/medication-history/:id
   */
  static async updateMedicationHistory(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const { id } = req.params;
      const validatedData = updateMedicationHistorySchema.parse(req.body);

      const result = await MedicationHistoryService.updateMedicationHistory(
        id,
        validatedData,
        req.user!.id,
        { ipAddress: req.ip, userAgent: req.get('User-Agent') }
      );

      if (!result.success) {
        res.status(404).json(result);
        return;
      }

      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete medication history
   * DELETE /api/medication-history/:id
   */
  static async deleteMedicationHistory(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const { id } = req.params;

      const result = await MedicationHistoryService.deleteMedicationHistory(
        id,
        req.user!.id,
        { ipAddress: req.ip, userAgent: req.get('User-Agent') }
      );

      if (!result.success) {
        res.status(404).json(result);
        return;
      }

      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get active medications for patient
   * GET /api/medication-history/patient/:patientId/active
   */
  static async getActiveMedications(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { patientId } = req.params;

      const result = await MedicationHistoryService.getMedicationHistoryByPatient(
        patientId,
        true,
        50
      );

      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Discontinue medication
   * PUT /api/medication-history/:id/discontinue
   */
  static async discontinueMedication(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const { id } = req.params;
      const { discontinuedReason, endDate } = req.body;

      const result = await MedicationHistoryService.updateMedicationHistory(
        id,
        {
          isActive: false,
          endDate: endDate || new Date().toISOString(),
          discontinuedReason: discontinuedReason || 'Discontinued by clinician',
        },
        req.user!.id,
        { ipAddress: req.ip, userAgent: req.get('User-Agent') }
      );

      if (!result.success) {
        res.status(404).json(result);
        return;
      }

      res.json(result);
    } catch (error) {
      next(error);
    }
  }
}
