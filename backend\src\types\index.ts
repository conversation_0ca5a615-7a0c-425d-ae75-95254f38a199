import { Request } from 'express';
import { JwtPayload } from 'jsonwebtoken';

// User-related types
export interface AuthenticatedUser {
  id: string;
  username: string;
  email: string;
  role: 'ADMIN' | 'CLINICIAN' | 'STAFF';
  firstName: string;
  lastName: string;
}

export interface AuthRequest extends Request {
  user?: AuthenticatedUser;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role?: 'ADMIN' | 'CLINICIAN' | 'STAFF';
}

export interface TokenPayload extends JwtPayload {
  userId: string;
  username: string;
  role: string;
}

export interface RefreshTokenPayload extends JwtPayload {
  userId: string;
  tokenId: string;
}

// Patient-related types
export interface PatientAddress {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country?: string;
}

export interface EmergencyContact {
  name: string;
  phone: string;
  relationship: string;
  email?: string;
}

export interface InsuranceInfo {
  provider: string;
  policyNumber: string;
  groupNumber?: string;
  subscriberName?: string;
  effectiveDate?: string;
  expirationDate?: string;
}

export interface CreatePatientData {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: 'MALE' | 'FEMALE' | 'NON_BINARY' | 'PREFER_NOT_TO_SAY' | 'OTHER';
  phone?: string;
  email?: string;
  address?: PatientAddress;
  occupation?: string;
  education?: 'ELEMENTARY' | 'HIGH_SCHOOL' | 'SOME_COLLEGE' | 'BACHELORS' | 'MASTERS' | 'DOCTORATE' | 'PROFESSIONAL' | 'OTHER';
  maritalStatus?: 'SINGLE' | 'MARRIED' | 'DIVORCED' | 'WIDOWED' | 'SEPARATED' | 'DOMESTIC_PARTNERSHIP' | 'OTHER';
  emergencyContact?: EmergencyContact;
  insuranceInfo?: InsuranceInfo;
  medicalHistory?: string;
  allergies?: string;
  currentMeds?: string;
  notes?: string;
}

export interface UpdatePatientData extends Partial<CreatePatientData> {
  isActive?: boolean;
}

// Lab Result types
export interface LabValues {
  [key: string]: number | string | boolean;
}

export interface NormalRanges {
  [key: string]: {
    min?: number;
    max?: number;
    unit?: string;
    reference?: string;
  };
}

export interface LabFlags {
  [key: string]: {
    flag: 'HIGH' | 'LOW' | 'CRITICAL' | 'ABNORMAL';
    severity: 'MILD' | 'MODERATE' | 'SEVERE';
    note?: string;
  };
}

export interface CreateLabResultData {
  patientId: string;
  testName: string;
  testCategory: 'chemistry' | 'hematology' | 'endocrine' | 'cardiac' | 'toxicology';
  testType: 'routine' | 'stat' | 'fasting' | 'random';
  value: string;
  numericValue?: number;
  unit?: string;
  referenceRange: string;
  referenceMin?: number;
  referenceMax?: number;
  status: 'normal' | 'abnormal' | 'critical' | 'pending';
  flagged?: boolean;
  testDate: string;
  resultDate?: string;
  orderedBy: string;
  reviewedBy?: string;
  laboratoryId?: string;
  specimenType?: 'serum' | 'plasma' | 'whole blood' | 'urine';
  notes?: string;
  criticalValue?: boolean;
  deltaCheck?: boolean;

  // Legacy fields for backward compatibility
  results?: LabValues;
  normalRanges?: NormalRanges;
  flags?: LabFlags;
}

// Appointment types
export interface CreateAppointmentData {
  patientId: string;
  providerId: string;
  date: string;
  duration: number;
  type: 'INITIAL_CONSULTATION' | 'FOLLOW_UP' | 'THERAPY_SESSION' | 'MEDICATION_REVIEW' | 'CRISIS_INTERVENTION' | 'GROUP_THERAPY' | 'FAMILY_THERAPY' | 'PSYCHOLOGICAL_TESTING' | 'OTHER';
  title?: string;
  description?: string;
  location?: string;
  isVirtual?: boolean;
  virtualMeetingUrl?: string;
  notes?: string;
  status?: 'SCHEDULED' | 'CONFIRMED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'NO_SHOW' | 'RESCHEDULED';
  recurringAppointmentId?: string;
}

export interface UpdateAppointmentData extends Partial<CreateAppointmentData> {
  status?: 'SCHEDULED' | 'CONFIRMED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'NO_SHOW' | 'RESCHEDULED';
}

// Notification types
export interface CreateNotificationData {
  recipientId: string;
  type: 'APPOINTMENT_REMINDER' | 'LAB_RESULT_AVAILABLE' | 'APPOINTMENT_CANCELLED' | 'APPOINTMENT_RESCHEDULED' | 'LAB_RESULT_CRITICAL' | 'SYSTEM_NOTIFICATION' | 'WELCOME' | 'PASSWORD_RESET' | 'ACCOUNT_LOCKED' | 'OTHER';
  title: string;
  message: string;
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT' | 'CRITICAL';
  scheduledFor?: string;
  patientId?: string;
  appointmentId?: string;
  labResultId?: string;
  metadata?: Record<string, any>;
  channel?: 'IN_APP' | 'EMAIL' | 'SMS';
}

// Recurring Appointment types
export interface CreateRecurringAppointmentData {
  patientId: string;
  providerId: string;
  startDate: string;
  endDate?: string;
  duration: number;
  type: 'INITIAL_CONSULTATION' | 'FOLLOW_UP' | 'THERAPY_SESSION' | 'MEDICATION_REVIEW' | 'CRISIS_INTERVENTION' | 'GROUP_THERAPY' | 'FAMILY_THERAPY' | 'PSYCHOLOGICAL_TESTING' | 'OTHER';
  frequency: 'DAILY' | 'WEEKLY' | 'BIWEEKLY' | 'MONTHLY' | 'QUARTERLY';
  recurrencePattern: 'DAILY' | 'WEEKLY' | 'BIWEEKLY' | 'MONTHLY' | 'YEARLY';
  interval?: number;
  dayOfWeek?: number; // 0-6, Sunday = 0
  dayOfMonth?: number; // 1-31
  timeSlot: string; // HH:MM format
  title?: string;
  description?: string;
  location?: string;
  isVirtual?: boolean;
  virtualMeetingUrl?: string;
  notes?: string;
  occurrences?: number;
  maxOccurrences?: number;
  isActive?: boolean;
}

// API Response types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// Query parameters
export interface PaginationQuery {
  page?: string;
  limit?: string;
}

export interface PatientQuery extends PaginationQuery {
  search?: string;
  gender?: string;
  isActive?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Error types
export interface ValidationError {
  field: string;
  message: string;
  value?: unknown;
}

export interface AppError extends Error {
  statusCode: number;
  isOperational: boolean;
}

// Audit log types
export interface AuditLogData {
  userId: string;
  action: 'CREATE' | 'UPDATE' | 'DELETE' | 'VIEW' | 'EXPORT' | 'CANCEL';
  entityType: 'USER' | 'PATIENT' | 'LAB_RESULT' | 'APPOINTMENT' | 'NOTIFICATION' | 'RECURRING_APPOINTMENT' | 'PsychTest' | 'MentalStatusExam' | 'MedicationHistory';
  entityId: string;
  oldValues?: Record<string, unknown>;
  newValues?: Record<string, unknown>;
  ipAddress?: string;
  userAgent?: string;
  patientId?: string;
  labResultId?: string;
  appointmentId?: string;
}
