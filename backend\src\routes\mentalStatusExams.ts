import { Router } from 'express';
import { MentalStatusExamController } from '../controllers/mentalStatusExamController';
import { authenticate } from '../middleware/auth';
import { authorize } from '../middleware/auth';
import { asyncHandler } from '../utils/asyncHandler';

const router = Router();

// Apply authentication to all routes
router.use(authenticate);

/**
 * @route   GET /api/mental-status-exams
 * @desc    Get all mental status exams with optional filters
 * @access  Private (All authenticated users)
 * @query   patientId, examinerId, page, limit
 */
router.get('/', asyncHandler(MentalStatusExamController.getAllMentalStatusExams));

/**
 * @route   POST /api/mental-status-exams
 * @desc    Create a new mental status exam
 * @access  Private (ADMIN, CLINICIAN)
 * @body    Mental status exam data
 */
router.post('/', authorize(['ADMIN', 'CLINICIAN']), asyncHandler(MentalStatusExamController.createMentalStatusExam));

/**
 * @route   GET /api/mental-status-exams/:id
 * @desc    Get mental status exam by ID
 * @access  Private (All authenticated users)
 */
router.get('/:id', asyncHandler(MentalStatusExamController.getMentalStatusExamById));

/**
 * @route   PUT /api/mental-status-exams/:id
 * @desc    Update mental status exam
 * @access  Private (ADMIN, CLINICIAN)
 */
router.put('/:id', authorize(['ADMIN', 'CLINICIAN']), asyncHandler(MentalStatusExamController.updateMentalStatusExam));

/**
 * @route   DELETE /api/mental-status-exams/:id
 * @desc    Delete mental status exam
 * @access  Private (ADMIN, CLINICIAN)
 */
router.delete('/:id', authorize(['ADMIN', 'CLINICIAN']), asyncHandler(MentalStatusExamController.deleteMentalStatusExam));

/**
 * @route   GET /api/mental-status-exams/patient/:patientId
 * @desc    Get mental status exams by patient ID
 * @access  Private (All authenticated users)
 * @query   limit
 */
router.get('/patient/:patientId', asyncHandler(MentalStatusExamController.getMentalStatusExamsByPatient));

export default router;
