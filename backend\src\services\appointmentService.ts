import { PrismaClient } from '@prisma/client';
import { 
  CreateAppointmentData, 
  UpdateAppointmentData,
  ApiResponse, 
  PaginatedResponse,
  AuditLogData 
} from '@/types';
import { NotFoundError, ValidationError, ConflictError } from '@/utils/errors';
import { addMinutes, isAfter, isBefore, startOfDay, endOfDay, parseISO } from 'date-fns';

const prisma = new PrismaClient();

/**
 * Appointment service handling all appointment operations
 * Includes scheduling, availability checking, and calendar management
 */
export class AppointmentService {
  /**
   * Check if a time slot is available
   */
  private static async isTimeSlotAvailable(
    providerId: string,
    startTime: Date,
    endTime: Date,
    excludeAppointmentId?: string
  ): Promise<boolean> {
    const conflictingAppointments = await prisma.appointment.findMany({
      where: {
        providerId,
        isDeleted: false,
        status: {
          in: ['SCHEDULED', 'CONFIRMED', 'IN_PROGRESS'],
        },
        AND: [
          {
            OR: [
              // New appointment starts during existing appointment
              {
                AND: [
                  { date: { lte: startTime } },
                  { endTime: { gt: startTime } },
                ],
              },
              // New appointment ends during existing appointment
              {
                AND: [
                  { date: { lt: endTime } },
                  { endTime: { gte: endTime } },
                ],
              },
              // New appointment encompasses existing appointment
              {
                AND: [
                  { date: { gte: startTime } },
                  { endTime: { lte: endTime } },
                ],
              },
            ],
          },
        ],
        ...(excludeAppointmentId && { id: { not: excludeAppointmentId } }),
      },
    });

    return conflictingAppointments.length === 0;
  }

  /**
   * Create a new appointment
   */
  static async createAppointment(
    data: CreateAppointmentData,
    createdBy: string,
    auditData?: { ipAddress?: string; userAgent?: string }
  ): Promise<ApiResponse<{ appointment: any }>> {
    // Validate required fields
    if (!data.patientId || !data.providerId || !data.date || !data.duration) {
      throw new ValidationError('Patient ID, provider ID, date, and duration are required');
    }

    // Validate appointment date
    const appointmentDate = new Date(data.date);
    if (isNaN(appointmentDate.getTime()) || isBefore(appointmentDate, new Date())) {
      throw new ValidationError('Invalid appointment date or date is in the past');
    }

    // Calculate end time
    const endTime = addMinutes(appointmentDate, data.duration);

    // Check if patient exists
    const patient = await prisma.patient.findFirst({
      where: {
        id: data.patientId,
        isDeleted: false,
      },
    });

    if (!patient) {
      throw new NotFoundError(`Patient not found with ID: ${data.patientId}`);
    }

    // Check if provider exists and is active with proper role
    const provider = await prisma.user.findUnique({
      where: {
        id: data.providerId,
      },
    });

    if (!provider) {
      throw new NotFoundError(`Provider not found with ID: ${data.providerId}`);
    }

    if (!provider.isActive) {
      throw new ValidationError(`Provider with ID: ${data.providerId} is not active`);
    }

    if (!['ADMIN', 'CLINICIAN'].includes(provider.role)) {
      throw new ValidationError(`User with ID: ${data.providerId} has role ${provider.role} which cannot provide appointments`);
    }

    // Check time slot availability
    const isAvailable = await this.isTimeSlotAvailable(
      data.providerId,
      appointmentDate,
      endTime
    );

    if (!isAvailable) {
      throw new ConflictError('Time slot is not available');
    }

    try {
      // Create appointment
      const appointment = await prisma.appointment.create({
        data: {
          patientId: data.patientId,
          providerId: data.providerId,
          date: appointmentDate,
          endTime,
          duration: data.duration,
          type: data.type || 'CONSULTATION',
          status: data.status || 'SCHEDULED',
          title: data.title?.trim() || null,
          description: data.description?.trim() || null,
          location: data.location?.trim() || null,
          isVirtual: data.isVirtual || false,
          virtualMeetingUrl: data.virtualMeetingUrl?.trim() || null,
          notes: data.notes?.trim() || null,
          createdBy,
        },
        include: {
          patient: {
            select: {
              id: true,
              patientId: true,
              firstName: true,
              lastName: true,
              phone: true,
              email: true,
            },
          },
          provider: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              username: true,
            },
          },
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              username: true,
            },
          },
        },
      });

      // Create audit log
      await this.createAuditLog({
        userId: createdBy,
        action: 'CREATE',
        entityType: 'APPOINTMENT',
        entityId: appointment.id,
        patientId: data.patientId,
        appointmentId: appointment.id,
        newValues: {
          date: appointment.date,
          duration: appointment.duration,
          type: appointment.type,
          status: appointment.status,
          provider: `${provider.firstName} ${provider.lastName}`,
          patient: `${patient.firstName} ${patient.lastName}`,
        },
        ipAddress: auditData?.ipAddress,
        userAgent: auditData?.userAgent,
      });

      return {
        success: true,
        data: { appointment },
        message: 'Appointment created successfully',
      };
    } catch (error) {
      // Better error handling for foreign key violations
      if (error.message && error.message.includes('foreign key constraint')) {
        if (error.message.includes('providerId')) {
          throw new ValidationError(`Provider with ID ${data.providerId} does not exist or is not active`);
        } else if (error.message.includes('patientId')) {
          throw new ValidationError(`Patient with ID ${data.patientId} does not exist or is deleted`);
        }
      }
      throw error;
    }
  }

  /**
   * Get appointments with pagination and filtering
   */
  static async getAppointments(
    query: {
      page?: string;
      limit?: string;
      patientId?: string;
      providerId?: string;
      status?: string;
      type?: string;
      dateFrom?: string;
      dateTo?: string;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    },
    userId: string,
    userRole: string
  ): Promise<PaginatedResponse<any>> {
    const page = parseInt(query.page || '1', 10);
    const limit = Math.min(parseInt(query.limit || '10', 10), 100);
    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      isDeleted: false,
    };

    // Role-based filtering
    if (userRole === 'CLINICIAN') {
      where.providerId = userId;
    } else if (userRole === 'STAFF') {
      // Staff can see all appointments but cannot modify
    }
    // ADMIN can see all appointments

    // Patient filter
    if (query.patientId) {
      where.patientId = query.patientId;
    }

    // Provider filter
    if (query.providerId) {
      where.providerId = query.providerId;
    }

    // Status filter
    if (query.status) {
      where.status = query.status;
    }

    // Type filter
    if (query.type) {
      where.type = query.type;
    }

    // Date range filter
    if (query.dateFrom || query.dateTo) {
      where.date = {};
      if (query.dateFrom) {
        where.date.gte = startOfDay(new Date(query.dateFrom));
      }
      if (query.dateTo) {
        where.date.lte = endOfDay(new Date(query.dateTo));
      }
    }

    // Sorting
    const orderBy: any = {};
    if (query.sortBy) {
      const direction = query.sortOrder === 'desc' ? 'desc' : 'asc';
      orderBy[query.sortBy] = direction;
    } else {
      orderBy.date = 'asc'; // Default sort by date
    }

    // Execute queries
    const [appointments, total] = await Promise.all([
      prisma.appointment.findMany({
        where,
        skip,
        take: limit,
        orderBy,
        include: {
          patient: {
            select: {
              id: true,
              patientId: true,
              firstName: true,
              lastName: true,
              phone: true,
              email: true,
            },
          },
          provider: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              username: true,
            },
          },
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              username: true,
            },
          },
        },
      }),
      prisma.appointment.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      success: true,
      data: appointments,
      pagination: {
        page,
        limit,
        total,
        pages: totalPages,

      },
    };
  }

  /**
   * Get appointment by ID
   */
  static async getAppointmentById(
    id: string,
    userId: string,
    userRole: string
  ): Promise<ApiResponse<{ appointment: any }>> {
    const where: any = {
      id,
      isDeleted: false,
    };

    // Role-based access control
    if (userRole === 'CLINICIAN') {
      where.providerId = userId;
    }

    const appointment = await prisma.appointment.findFirst({
      where,
      include: {
        patient: {
          select: {
            id: true,
            patientId: true,
            firstName: true,
            lastName: true,
            phone: true,
            email: true,
            dateOfBirth: true,
            gender: true,
          },
        },
        provider: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            username: true,
            email: true,
          },
        },
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            username: true,
          },
        },
      },
    });

    if (!appointment) {
      throw new NotFoundError('Appointment not found');
    }

    // Create audit log for viewing
    await this.createAuditLog({
      userId,
      action: 'VIEW',
      entityType: 'APPOINTMENT',
      entityId: appointment.id,
      patientId: appointment.patientId,
      appointmentId: appointment.id,
    });

    return {
      success: true,
      data: { appointment },
    };
  }

  /**
   * Update appointment
   */
  static async updateAppointment(
    id: string,
    data: UpdateAppointmentData,
    userId: string,
    userRole: string,
    auditData?: { ipAddress?: string; userAgent?: string }
  ): Promise<ApiResponse<{ appointment: any }>> {
    // Check if appointment exists and user has access
    const existingAppointment = await prisma.appointment.findFirst({
      where: {
        id,
        isDeleted: false,
        ...(userRole === 'CLINICIAN' && { providerId: userId }),
      },
    });

    if (!existingAppointment) {
      throw new NotFoundError('Appointment not found');
    }

    // Prepare update data
    const updateData: any = {};
    let newEndTime = existingAppointment.endTime;

    // Validate and update date/time if provided
    if (data.date || data.duration) {
      const newDate = data.date ? new Date(data.date) : existingAppointment.date;
      const newDuration = data.duration || existingAppointment.duration;

      if (data.date && (isNaN(newDate.getTime()) || isBefore(newDate, new Date()))) {
        throw new ValidationError('Invalid appointment date or date is in the past');
      }

      newEndTime = addMinutes(newDate, newDuration);

      // Check availability if date or duration changed
      if (data.date || data.duration) {
        const isAvailable = await this.isTimeSlotAvailable(
          data.providerId || existingAppointment.providerId,
          newDate,
          newEndTime,
          id
        );

        if (!isAvailable) {
          throw new ConflictError('Time slot is not available');
        }
      }

      updateData.date = newDate;
      updateData.duration = newDuration;
      updateData.endTime = newEndTime;
    }

    // Update other fields
    if (data.providerId !== undefined) updateData.providerId = data.providerId;
    if (data.type !== undefined) updateData.type = data.type;
    if (data.status !== undefined) updateData.status = data.status;
    if (data.title !== undefined) updateData.title = data.title?.trim() || null;
    if (data.description !== undefined) updateData.description = data.description?.trim() || null;
    if (data.location !== undefined) updateData.location = data.location?.trim() || null;
    if (data.isVirtual !== undefined) updateData.isVirtual = data.isVirtual;
    if (data.virtualMeetingUrl !== undefined) updateData.virtualMeetingUrl = data.virtualMeetingUrl?.trim() || null;
    if (data.notes !== undefined) updateData.notes = data.notes?.trim() || null;

    // Update appointment
    const appointment = await prisma.appointment.update({
      where: { id },
      data: updateData,
      include: {
        patient: {
          select: {
            id: true,
            patientId: true,
            firstName: true,
            lastName: true,
            phone: true,
            email: true,
          },
        },
        provider: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            username: true,
          },
        },
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            username: true,
          },
        },
      },
    });

    // Create audit log
    const oldValues = {
      date: existingAppointment.date,
      duration: existingAppointment.duration,
      status: existingAppointment.status,
      type: existingAppointment.type,
      notes: existingAppointment.notes,
    };

    await this.createAuditLog({
      userId,
      action: 'UPDATE',
      entityType: 'APPOINTMENT',
      entityId: appointment.id,
      patientId: appointment.patientId,
      appointmentId: appointment.id,
      oldValues: oldValues,
      newValues: {
        date: appointment.date,
        duration: appointment.duration,
        type: appointment.type,
        status: appointment.status,
        provider: `${appointment.provider.firstName} ${appointment.provider.lastName}`,
        patient: `${appointment.patient.firstName} ${appointment.patient.lastName}`,
      },
      ipAddress: auditData?.ipAddress,
      userAgent: auditData?.userAgent,
    });

    return {
      success: true,
      data: { appointment },
      message: 'Appointment updated successfully',
    };
  }

  /**
   * Cancel appointment (soft delete)
   */
  static async cancelAppointment(
    id: string,
    reason: string,
    userId: string,
    userRole: string,
    auditData?: { ipAddress?: string; userAgent?: string }
  ): Promise<ApiResponse<null>> {
    // Check if appointment exists and user has access
    const appointment = await prisma.appointment.findFirst({
      where: {
        id,
        isDeleted: false,
        ...(userRole === 'CLINICIAN' && { providerId: userId }),
      },
      include: {
        patient: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    if (!appointment) {
      throw new NotFoundError('Appointment not found');
    }

    // Update appointment status to cancelled
    await prisma.appointment.update({
      where: { id },
      data: {
        status: 'CANCELLED',
        notes: appointment.notes 
          ? `${appointment.notes}\n\nCancelled: ${reason}`
          : `Cancelled: ${reason}`,
      },
    });

    // Create audit log
    await this.createAuditLog({
      userId,
      action: 'UPDATE',
      entityType: 'APPOINTMENT',
      entityId: id,
      patientId: appointment.patientId,
      appointmentId: id,
      newValues: {
        status: 'CANCELLED',
        reason,
      },
      oldValues: { status: appointment.status },
      ipAddress: auditData?.ipAddress,
      userAgent: auditData?.userAgent,
    });

    return {
      success: true,
      data: null,
      message: 'Appointment cancelled successfully',
    };
  }

  /**
   * Get provider availability for a specific date
   */
  static async getProviderAvailability(
    providerId: string,
    date: string,
    userId: string,
    userRole: string
  ): Promise<ApiResponse<{ availability: any[] }>> {
    const targetDate = parseISO(date);
    const dayStart = startOfDay(targetDate);
    const dayEnd = endOfDay(targetDate);

    // Get existing appointments for the day
    const existingAppointments = await prisma.appointment.findMany({
      where: {
        providerId,
        isDeleted: false,
        status: {
          in: ['SCHEDULED', 'CONFIRMED', 'IN_PROGRESS'],
        },
        date: {
          gte: dayStart,
          lte: dayEnd,
        },
      },
      orderBy: { date: 'asc' },
    });

    // Generate time slots (assuming 9 AM to 5 PM, 30-minute slots)
    const workDayStart = new Date(targetDate);
    workDayStart.setHours(9, 0, 0, 0);
    
    const workDayEnd = new Date(targetDate);
    workDayEnd.setHours(17, 0, 0, 0);

    const timeSlots = [];
    let currentTime = new Date(workDayStart);

    while (isBefore(currentTime, workDayEnd)) {
      const slotEnd = addMinutes(currentTime, 30);
      
      // Check if this slot conflicts with any existing appointment
      const isAvailable = !existingAppointments.some(apt => 
        (currentTime >= apt.date && currentTime < apt.endTime) ||
        (slotEnd > apt.date && slotEnd <= apt.endTime) ||
        (currentTime <= apt.date && slotEnd >= apt.endTime)
      );

      timeSlots.push({
        startTime: new Date(currentTime),
        endTime: new Date(slotEnd),
        isAvailable,
        duration: 30,
      });

      currentTime = addMinutes(currentTime, 30);
    }

    return {
      success: true,
      data: { availability: timeSlots },
    };
  }

  /**
   * Create audit log entry
   */
  private static async createAuditLog(data: AuditLogData): Promise<void> {
    const { oldValues, newValues, ...rest } = data;

    await prisma.auditLog.create({
      data: {
        ...rest,
        oldValues: oldValues ? JSON.stringify(oldValues) : null,
        newValues: newValues ? JSON.stringify(newValues) : null,
      },
    });
  }
}
