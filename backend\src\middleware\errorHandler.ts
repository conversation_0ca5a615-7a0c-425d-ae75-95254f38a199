import { Request, Response, NextFunction } from 'express';
import { formatErrorResponse, AppError } from '@/utils/errors';
import { logger } from '@/utils/logger';

/**
 * Global error handling middleware that formats and returns standardized error responses
 * Must be the last middleware in the chain
 *
 * @param {Error} error - The error object thrown in the application
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 * @returns {void} - Sends a JSON response with formatted error details
 */
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Enhanced error logging with context
  logger.error('Request error occurred', {
    error: error.message,
    stack: error.stack,
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    body: req.body,
    params: req.params,
    query: req.query,
    timestamp: new Date().toISOString(),
  });

  const errorResponse = formatErrorResponse(error);

  res.status(errorResponse.statusCode).json(errorResponse);
};

/**
 * 404 Not Found handler for unmatched routes
 *
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @returns {void} - Sends a 404 JSON response
 */
export const notFoundHandler = (req: Request, res: Response): void => {
  res.status(404).json({
    success: false,
    error: `Route ${req.method} ${req.path} not found`,
    statusCode: 404,
  });
};

/**
 * Async error wrapper to catch async errors in route handlers
 * Wraps async route handlers to automatically catch and forward errors to error middleware
 *
 * @param {Function} fn - The async route handler function
 * @returns {Function} - Wrapped function that catches async errors
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};
