import { Router } from 'express';
import { Auth<PERSON>ontroller } from '@/controllers/authController';
import { authenticate } from '@/middleware/auth';
import { authRateLimit, passwordResetRateLimit } from '@/middleware/security';

/**
 * Authentication routes
 * Base path: /api/auth
 */

const router = Router();

/**
 * @route   POST /api/auth/register
 * @desc    Register a new user
 * @access  Public
 * @body    { username, email, password, firstName, lastName, role? }
 */
router.post('/register', authRateLimit, AuthController.register);

/**
 * @route   POST /api/auth/login
 * @desc    Login user with credentials
 * @access  Public
 * @body    { username, password }
 */
router.post('/login', authRateLimit, AuthController.login);

/**
 * @route   POST /api/auth/refresh
 * @desc    Refresh access token using refresh token
 * @access  Public
 * @body    { refreshToken? } (can also use httpOnly cookie)
 */
router.post('/refresh', AuthController.refreshToken);

/**
 * @route   POST /api/auth/logout
 * @desc    Logout user and revoke refresh token
 * @access  Public
 * @body    { refreshToken? } (can also use httpOnly cookie)
 */
router.post('/logout', AuthController.logout);

/**
 * @route   GET /api/auth/me
 * @desc    Get current user information
 * @access  Private
 */
router.get('/me', authenticate, AuthController.getCurrentUser);

/**
 * @route   PUT /api/auth/change-password
 * @desc    Change user password
 * @access  Private
 * @body    { currentPassword, newPassword }
 */
router.put('/change-password', authenticate, passwordResetRateLimit, AuthController.changePassword);

export default router;
