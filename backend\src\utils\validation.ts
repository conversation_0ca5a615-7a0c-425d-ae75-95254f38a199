import { z } from 'zod';

export const createAppointmentSchema = z.object({
  patientId: z.string().uuid('Invalid patient ID'),
  providerId: z.string().uuid('Invalid provider ID').optional().or(z.literal('')),
  date: z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid date format'),
  duration: z.number().min(15, 'Duration must be at least 15 minutes').max(480, 'Duration cannot exceed 8 hours'),
  type: z.enum([
    'INITIAL_CONSULTATION',
    'FOLLOW_UP',
    'THERAPY_SESSION',
    'MEDICATION_REVIEW',
    'CRISIS_INTERVENTION',
    'GROUP_THERAPY',
    'FAMILY_THERAPY',
    'PSYCHOLOGICAL_TESTING',
    'OTHER',
  ]),
  status: z.enum(['SCHEDULED', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW', 'RESCHEDULED']).optional(),
  title: z.string().max(200).optional(),
  description: z.string().max(1000).optional(),
  location: z.string().max(200).optional(),
  isVirtual: z.boolean().optional(),
  virtualMeetingUrl: z.string().url().optional(),
  notes: z.string().max(2000).optional(),
  recurringAppointmentId: z.string().uuid().optional(),
});

export const updateAppointmentSchema = createAppointmentSchema.partial();

export const registerSchema = z.object({
  username: z.string()
    .min(3, 'Username must be at least 3 characters')
    .max(50, 'Username must be less than 50 characters')
    .regex(/^[a-zA-Z0-9._-]+$/, 'Username can only contain letters, numbers, dots, underscores, and hyphens'),
  email: z.string()
    .email('Invalid email format')
    .max(255, 'Email must be less than 255 characters'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters'),
  firstName: z.string()
    .min(1, 'First name is required')
    .max(100, 'First name must be less than 100 characters'),
  lastName: z.string()
    .min(1, 'Last name is required')
    .max(100, 'Last name must be less than 100 characters'),
  role: z.enum(['ADMIN', 'CLINICIAN', 'STAFF']).optional(),
});

export const loginSchema = z.object({
  username: z.string()
    .min(1, 'Username or email is required'),
  password: z.string()
    .min(1, 'Password is required'),
});

export const createLabResultSchema = z.object({
  patientId: z.string().uuid(),
  testType: z.enum([
    'CBC',
    'METABOLIC_PANEL',
    'LIPID_PANEL',
    'THYROID',
    'LIVER_FUNCTION',
    'KIDNEY_FUNCTION',
    'VITAMIN_LEVELS',
    'DRUG_SCREEN',
    'CARDIAC_MARKERS',
    'INFLAMMATORY',
    'COAGULATION',
    'URINALYSIS',
    'HEMOGLOBIN_A1C',
    'OTHER',
  ]),
  testDate: z.string().datetime(),
  orderedBy: z.string(),
  labName: z.string().optional(),
  results: z.record(z.any()),
  normalRanges: z.record(z.any()).optional(),
  flags: z.record(z.any()).optional(),
  notes: z.string().optional(),
  status: z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'AMENDED']).optional(),
});

export const updateLabResultSchema = createLabResultSchema.partial();

// Psychological Test Validation Schemas
export const createPsychTestSchema = z.object({
  patientId: z.string().uuid('Invalid patient ID'),
  testName: z.string().min(1, 'Test name is required').max(100, 'Test name must be less than 100 characters'),
  testCategory: z.enum(['depression', 'anxiety', 'ocd', 'psychosis', 'cognitive', 'personality', 'trauma', 'substance', 'other']),
  version: z.string().max(50).optional(),
  administeredBy: z.string().min(1, 'Administrator name is required').max(100),
  administeredDate: z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid date format'),
  completionTime: z.number().min(1).max(600).optional(), // 1-600 minutes
  location: z.enum(['office', 'hospital', 'telehealth', 'home', 'other']).optional(),
  rawScore: z.number().optional(),
  totalScore: z.number().optional(),
  subscaleScores: z.string().optional(), // JSON string
  scaledScore: z.number().optional(),
  percentile: z.number().min(0).max(100).optional(),
  tScore: z.number().optional(),
  zScore: z.number().optional(),
  severity: z.enum(['minimal', 'mild', 'moderate', 'severe', 'extreme']).optional(),
  clinicalRange: z.enum(['normal', 'borderline', 'clinical']).optional(),
  interpretation: z.string().max(2000).optional(),
  recommendations: z.string().max(2000).optional(),
  responses: z.string().min(1, 'Test responses are required'), // JSON string
  validity: z.enum(['valid', 'questionable', 'invalid']).optional(),
  validityIndices: z.string().optional(), // JSON string
  notes: z.string().max(1000).optional(),
  followUpDate: z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid date format').optional(),
  followUpRequired: z.boolean().optional(),
  batteryId: z.string().max(100).optional(),
  sessionNumber: z.number().min(1).optional(),
  baselineTest: z.boolean().optional(),
});

export const updatePsychTestSchema = createPsychTestSchema.partial();

export const createNotificationSchema = z.object({
  recipientId: z.string().uuid(),
  type: z.enum([
    'APPOINTMENT_REMINDER',
    'LAB_RESULT_AVAILABLE',
    'APPOINTMENT_CANCELLED',
    'APPOINTMENT_RESCHEDULED',
    'LAB_RESULT_CRITICAL',
    'SYSTEM_NOTIFICATION',
    'WELCOME',
    'PASSWORD_RESET',
    'ACCOUNT_LOCKED',
    'OTHER'
  ]),
  title: z.string(),
  message: z.string(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
  scheduledFor: z.string().datetime().optional(),
  patientId: z.string().uuid().optional(),
  appointmentId: z.string().uuid().optional(),
  labResultId: z.string().uuid().optional(),
  metadata: z.record(z.any()).optional(),
  channel: z.enum(['IN_APP', 'EMAIL', 'SMS']).optional(),
});

export const createPatientSchema = z.object({
  firstName: z.string(),
  lastName: z.string(),
  dateOfBirth: z.string().datetime(),
  gender: z.enum(['MALE', 'FEMALE', 'NON_BINARY', 'PREFER_NOT_TO_SAY', 'OTHER']),
  phone: z.string().optional(),
  email: z.string().email().optional(),
  address: z.object({
    street: z.string(),
    city: z.string(),
    state: z.string(),
    zipCode: z.string(),
    country: z.string().optional(),
  }).optional(),
  occupation: z.string().optional(),
  education: z.enum(['ELEMENTARY', 'HIGH_SCHOOL', 'SOME_COLLEGE', 'BACHELORS', 'MASTERS', 'DOCTORATE', 'PROFESSIONAL', 'OTHER']).optional(),
  maritalStatus: z.enum(['SINGLE', 'MARRIED', 'DIVORCED', 'WIDOWED', 'SEPARATED', 'DOMESTIC_PARTNERSHIP', 'OTHER']).optional(),
  emergencyContact: z.object({
    name: z.string(),
    phone: z.string(),
    relationship: z.string(),
    email: z.string().email().optional(),
  }).optional(),
  insuranceInfo: z.object({
    provider: z.string(),
    policyNumber: z.string(),
    groupNumber: z.string().optional(),
    subscriberName: z.string().optional(),
    effectiveDate: z.string().optional(),
    expirationDate: z.string().optional(),
  }).optional(),
  medicalHistory: z.string().optional(),
  allergies: z.string().optional(),
  currentMeds: z.string().optional(),
  notes: z.string().optional(),
});

export const updatePatientSchema = createPatientSchema.partial().extend({
  isActive: z.boolean().optional(),
}); 