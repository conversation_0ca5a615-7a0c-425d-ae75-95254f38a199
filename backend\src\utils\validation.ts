import { z } from 'zod';

export const createAppointmentSchema = z.object({
  patientId: z.string().uuid('Invalid patient ID'),
  providerId: z.string().uuid('Invalid provider ID').optional().or(z.literal('')),
  date: z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid date format'),
  duration: z.number().min(15, 'Duration must be at least 15 minutes').max(480, 'Duration cannot exceed 8 hours'),
  type: z.enum([
    'INITIAL_CONSULTATION',
    'FOLLOW_UP',
    'THERAPY_SESSION',
    'MEDICATION_REVIEW',
    'CRISIS_INTERVENTION',
    'GROUP_THERAPY',
    'FAMILY_THERAPY',
    'PSYCHOLOGICAL_TESTING',
    'OTHER',
  ]),
  status: z.enum(['SCHEDULED', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW', 'RESCHEDULED']).optional(),
  title: z.string().max(200).optional(),
  description: z.string().max(1000).optional(),
  location: z.string().max(200).optional(),
  isVirtual: z.boolean().optional(),
  virtualMeetingUrl: z.string().url().optional(),
  notes: z.string().max(2000).optional(),
  recurringAppointmentId: z.string().uuid().optional(),
});

export const updateAppointmentSchema = createAppointmentSchema.partial();

export const registerSchema = z.object({
  username: z.string()
    .min(3, 'Username must be at least 3 characters')
    .max(50, 'Username must be less than 50 characters')
    .regex(/^[a-zA-Z0-9._-]+$/, 'Username can only contain letters, numbers, dots, underscores, and hyphens'),
  email: z.string()
    .email('Invalid email format')
    .max(255, 'Email must be less than 255 characters'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters'),
  firstName: z.string()
    .min(1, 'First name is required')
    .max(100, 'First name must be less than 100 characters'),
  lastName: z.string()
    .min(1, 'Last name is required')
    .max(100, 'Last name must be less than 100 characters'),
  role: z.enum(['ADMIN', 'CLINICIAN', 'STAFF']).optional(),
});

export const loginSchema = z.object({
  username: z.string()
    .min(1, 'Username or email is required'),
  password: z.string()
    .min(1, 'Password is required'),
});

export const createLabResultSchema = z.object({
  patientId: z.string().uuid(),
  testType: z.enum([
    'CBC',
    'METABOLIC_PANEL',
    'LIPID_PANEL',
    'THYROID',
    'LIVER_FUNCTION',
    'KIDNEY_FUNCTION',
    'VITAMIN_LEVELS',
    'DRUG_SCREEN',
    'CARDIAC_MARKERS',
    'INFLAMMATORY',
    'COAGULATION',
    'URINALYSIS',
    'HEMOGLOBIN_A1C',
    'OTHER',
  ]),
  testDate: z.string().datetime(),
  orderedBy: z.string(),
  labName: z.string().optional(),
  results: z.record(z.any()),
  normalRanges: z.record(z.any()).optional(),
  flags: z.record(z.any()).optional(),
  notes: z.string().optional(),
  status: z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'AMENDED']).optional(),
});

export const updateLabResultSchema = createLabResultSchema.partial();

// Psychological Test Validation Schemas
export const createPsychTestSchema = z.object({
  patientId: z.string().uuid('Invalid patient ID'),
  testName: z.string().min(1, 'Test name is required').max(100, 'Test name must be less than 100 characters'),
  testCategory: z.enum(['depression', 'anxiety', 'ocd', 'psychosis', 'cognitive', 'personality', 'trauma', 'substance', 'other']),
  version: z.string().max(50).optional(),
  administeredBy: z.string().min(1, 'Administrator name is required').max(100),
  administeredDate: z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid date format'),
  completionTime: z.number().min(1).max(600).optional(), // 1-600 minutes
  location: z.enum(['office', 'hospital', 'telehealth', 'home', 'other']).optional(),
  rawScore: z.number().optional(),
  totalScore: z.number().optional(),
  subscaleScores: z.string().optional(), // JSON string
  scaledScore: z.number().optional(),
  percentile: z.number().min(0).max(100).optional(),
  tScore: z.number().optional(),
  zScore: z.number().optional(),
  severity: z.enum(['minimal', 'mild', 'moderate', 'severe', 'extreme']).optional(),
  clinicalRange: z.enum(['normal', 'borderline', 'clinical']).optional(),
  interpretation: z.string().max(2000).optional(),
  recommendations: z.string().max(2000).optional(),
  responses: z.string().min(1, 'Test responses are required'), // JSON string
  validity: z.enum(['valid', 'questionable', 'invalid']).optional(),
  validityIndices: z.string().optional(), // JSON string
  notes: z.string().max(1000).optional(),
  followUpDate: z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid date format').optional(),
  followUpRequired: z.boolean().optional(),
  batteryId: z.string().max(100).optional(),
  sessionNumber: z.number().min(1).optional(),
  baselineTest: z.boolean().optional(),
});

export const updatePsychTestSchema = createPsychTestSchema.partial();

// Mental Status Exam Validation Schemas
export const createMentalStatusExamSchema = z.object({
  patientId: z.string().uuid('Invalid patient ID'),
  examDate: z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid date format'),
  examinerId: z.string().uuid('Invalid examiner ID'),
  appearance_grooming: z.enum(['well-groomed', 'disheveled', 'inappropriate', 'poor']).optional(),
  appearance_dress: z.enum(['appropriate', 'bizarre', 'seductive', 'inappropriate']).optional(),
  appearance_hygiene: z.enum(['good', 'poor', 'neglected']).optional(),
  behavior_eye_contact: z.enum(['appropriate', 'poor', 'intense', 'avoidant']).optional(),
  behavior_motor: z.enum(['normal', 'agitated', 'retarded', 'restless', 'hyperactive']).optional(),
  behavior_cooperation: z.enum(['cooperative', 'guarded', 'hostile', 'withdrawn']).optional(),
  speech_rate: z.enum(['normal', 'pressured', 'slow', 'rapid']).optional(),
  speech_volume: z.enum(['normal', 'loud', 'soft', 'whispered']).optional(),
  speech_tone: z.enum(['normal', 'monotone', 'dramatic', 'anxious']).optional(),
  speech_fluency: z.enum(['fluent', 'dysfluent', 'stuttering']).optional(),
  mood_reported: z.string().max(200).optional(),
  mood_observed: z.string().max(200).optional(),
  affect_type: z.enum(['euthymic', 'depressed', 'anxious', 'irritable', 'euphoric']).optional(),
  affect_range: z.enum(['full', 'restricted', 'blunted', 'flat']).optional(),
  affect_appropriateness: z.enum(['appropriate', 'inappropriate', 'incongruent']).optional(),
  thought_process: z.enum(['linear', 'tangential', 'circumstantial', 'loose', 'flight_of_ideas']).optional(),
  thought_organization: z.enum(['organized', 'disorganized', 'coherent']).optional(),
  thought_flow: z.enum(['normal', 'rapid', 'slow', 'blocked']).optional(),
  thought_content: z.string().optional(),
  delusions: z.boolean().optional(),
  delusion_type: z.enum(['persecutory', 'grandiose', 'somatic', 'religious', 'nihilistic']).optional(),
  obsessions: z.boolean().optional(),
  compulsions: z.boolean().optional(),
  phobias: z.boolean().optional(),
  hallucinations: z.boolean().optional(),
  hallucination_type: z.enum(['auditory', 'visual', 'tactile', 'olfactory', 'gustatory']).optional(),
  illusions: z.boolean().optional(),
  depersonalization: z.boolean().optional(),
  derealization: z.boolean().optional(),
  orientation_person: z.boolean().optional(),
  orientation_place: z.boolean().optional(),
  orientation_time: z.boolean().optional(),
  orientation_situation: z.boolean().optional(),
  attention_span: z.enum(['good', 'fair', 'poor', 'distractible']).optional(),
  concentration: z.enum(['good', 'fair', 'poor', 'impaired']).optional(),
  memory_immediate: z.enum(['intact', 'impaired', 'unable_to_assess']).optional(),
  memory_recent: z.enum(['intact', 'impaired', 'unable_to_assess']).optional(),
  memory_remote: z.enum(['intact', 'impaired', 'unable_to_assess']).optional(),
  abstract_thinking: z.enum(['intact', 'concrete', 'impaired']).optional(),
  insight_level: z.enum(['good', 'fair', 'poor', 'absent']).optional(),
  insight_description: z.string().max(500).optional(),
  judgment_level: z.enum(['good', 'fair', 'poor', 'impaired']).optional(),
  judgment_description: z.string().max(500).optional(),
  suicidal_ideation: z.enum(['denied', 'passive', 'active', 'with_plan']).optional(),
  suicidal_risk: z.enum(['low', 'moderate', 'high', 'imminent']).optional(),
  homicidal_ideation: z.enum(['denied', 'present', 'with_plan']).optional(),
  homicidal_risk: z.enum(['low', 'moderate', 'high', 'imminent']).optional(),
  clinical_notes: z.string().max(2000).optional(),
  recommendations: z.string().max(1000).optional(),
  followup_needed: z.boolean().optional(),
});

export const updateMentalStatusExamSchema = createMentalStatusExamSchema.partial();

// Medication History Validation Schemas
export const createMedicationHistorySchema = z.object({
  patientId: z.string().uuid('Invalid patient ID'),
  medicationName: z.string().min(1, 'Medication name is required').max(200),
  genericName: z.string().max(200).optional(),
  brandName: z.string().max(200).optional(),
  strength: z.string().min(1, 'Strength is required').max(100),
  dosage: z.string().min(1, 'Dosage is required').max(100),
  frequency: z.string().min(1, 'Frequency is required').max(100),
  route: z.enum(['PO', 'IM', 'IV', 'SL', 'PR', 'Topical', 'Inhalation', 'Nasal', 'Ophthalmic', 'Otic']),
  indication: z.string().min(1, 'Indication is required').max(500),
  startDate: z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid start date format'),
  endDate: z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid end date format').optional(),
  duration: z.string().max(100).optional(),
  prescribedBy: z.string().min(1, 'Prescriber name is required').max(200),
  prescriberId: z.string().uuid().optional(),
  pharmacy: z.string().max(200).optional(),
  sideEffects: z.string().optional(), // JSON string
  effectiveness: z.enum(['excellent', 'good', 'fair', 'poor', 'unknown']).optional(),
  adherence: z.enum(['excellent', 'good', 'fair', 'poor', 'unknown']).optional(),
  adherenceNotes: z.string().max(500).optional(),
  discontinuedReason: z.string().max(500).optional(),
  allergicReaction: z.boolean().optional(),
  interactions: z.string().optional(), // JSON string
  monitoring: z.string().max(500).optional(),
  prn: z.boolean().optional(),
  notes: z.string().max(1000).optional(),
  isActive: z.boolean().optional(),
});

export const updateMedicationHistorySchema = createMedicationHistorySchema.partial();

export const createNotificationSchema = z.object({
  recipientId: z.string().uuid(),
  type: z.enum([
    'APPOINTMENT_REMINDER',
    'LAB_RESULT_AVAILABLE',
    'APPOINTMENT_CANCELLED',
    'APPOINTMENT_RESCHEDULED',
    'LAB_RESULT_CRITICAL',
    'SYSTEM_NOTIFICATION',
    'WELCOME',
    'PASSWORD_RESET',
    'ACCOUNT_LOCKED',
    'OTHER'
  ]),
  title: z.string(),
  message: z.string(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
  scheduledFor: z.string().datetime().optional(),
  patientId: z.string().uuid().optional(),
  appointmentId: z.string().uuid().optional(),
  labResultId: z.string().uuid().optional(),
  metadata: z.record(z.any()).optional(),
  channel: z.enum(['IN_APP', 'EMAIL', 'SMS']).optional(),
});

export const createPatientSchema = z.object({
  firstName: z.string(),
  lastName: z.string(),
  dateOfBirth: z.string().datetime(),
  gender: z.enum(['MALE', 'FEMALE', 'NON_BINARY', 'PREFER_NOT_TO_SAY', 'OTHER']),
  phone: z.string().optional(),
  email: z.string().email().optional(),
  address: z.object({
    street: z.string(),
    city: z.string(),
    state: z.string(),
    zipCode: z.string(),
    country: z.string().optional(),
  }).optional(),
  occupation: z.string().optional(),
  education: z.enum(['ELEMENTARY', 'HIGH_SCHOOL', 'SOME_COLLEGE', 'BACHELORS', 'MASTERS', 'DOCTORATE', 'PROFESSIONAL', 'OTHER']).optional(),
  maritalStatus: z.enum(['SINGLE', 'MARRIED', 'DIVORCED', 'WIDOWED', 'SEPARATED', 'DOMESTIC_PARTNERSHIP', 'OTHER']).optional(),
  emergencyContact: z.object({
    name: z.string(),
    phone: z.string(),
    relationship: z.string(),
    email: z.string().email().optional(),
  }).optional(),
  insuranceInfo: z.object({
    provider: z.string(),
    policyNumber: z.string(),
    groupNumber: z.string().optional(),
    subscriberName: z.string().optional(),
    effectiveDate: z.string().optional(),
    expirationDate: z.string().optional(),
  }).optional(),
  medicalHistory: z.string().optional(),
  allergies: z.string().optional(),
  currentMeds: z.string().optional(),
  notes: z.string().optional(),
});

export const updatePatientSchema = createPatientSchema.partial().extend({
  isActive: z.boolean().optional(),
}); 