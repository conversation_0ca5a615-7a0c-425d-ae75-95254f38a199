import { create } from 'zustand';

// Form State Types
interface FormField {
  value: any;
  error?: string;
  touched: boolean;
  dirty: boolean;
}

interface FormState {
  [formId: string]: {
    fields: Record<string, FormField>;
    isSubmitting: boolean;
    isValid: boolean;
    isDirty: boolean;
    submitCount: number;
    errors: Record<string, string>;
  };
}

interface FormStore {
  forms: FormState;
  
  // Form management
  initializeForm: (formId: string, initialValues: Record<string, any>) => void;
  destroyForm: (formId: string) => void;
  resetForm: (formId: string, values?: Record<string, any>) => void;
  
  // Field management
  setFieldValue: (formId: string, fieldName: string, value: any) => void;
  setFieldError: (formId: string, fieldName: string, error?: string) => void;
  setFieldTouched: (formId: string, fieldName: string, touched?: boolean) => void;
  
  // Form state
  setFormSubmitting: (formId: string, isSubmitting: boolean) => void;
  setFormErrors: (formId: string, errors: Record<string, string>) => void;
  incrementSubmitCount: (formId: string) => void;
  
  // Getters
  getFormState: (formId: string) => FormState[string] | undefined;
  getFieldValue: (formId: string, fieldName: string) => any;
  getFieldError: (formId: string, fieldName: string) => string | undefined;
  isFieldTouched: (formId: string, fieldName: string) => boolean;
  isFieldDirty: (formId: string, fieldName: string) => boolean;
  isFormValid: (formId: string) => boolean;
  isFormDirty: (formId: string) => boolean;
}

export const useFormStore = create<FormStore>((set, get) => ({
  forms: {},

  initializeForm: (formId, initialValues) =>
    set((state) => {
      const fields: Record<string, FormField> = {};
      Object.entries(initialValues).forEach(([key, value]) => {
        fields[key] = {
          value,
          touched: false,
          dirty: false,
        };
      });

      return {
        forms: {
          ...state.forms,
          [formId]: {
            fields,
            isSubmitting: false,
            isValid: true,
            isDirty: false,
            submitCount: 0,
            errors: {},
          },
        },
      };
    }),

  destroyForm: (formId) =>
    set((state) => {
      const { [formId]: _, ...rest } = state.forms;
      return { forms: rest };
    }),

  resetForm: (formId, values) =>
    set((state) => {
      const currentForm = state.forms[formId];
      if (!currentForm) return state;

      const fields: Record<string, FormField> = {};
      const valuesToUse = values || {};

      Object.keys(currentForm.fields).forEach((key) => {
        fields[key] = {
          value: valuesToUse[key] ?? currentForm.fields[key].value,
          touched: false,
          dirty: false,
        };
      });

      return {
        forms: {
          ...state.forms,
          [formId]: {
            ...currentForm,
            fields,
            isDirty: false,
            errors: {},
          },
        },
      };
    }),

  setFieldValue: (formId, fieldName, value) =>
    set((state) => {
      const form = state.forms[formId];
      if (!form) return state;

      const field = form.fields[fieldName];
      if (!field) return state;

      const isDirty = value !== field.value;
      const updatedField = {
        ...field,
        value,
        dirty: isDirty,
      };

      const updatedFields = {
        ...form.fields,
        [fieldName]: updatedField,
      };

      const formIsDirty = Object.values(updatedFields).some((f) => f.dirty);
      const formIsValid = Object.values(form.errors).every((error) => !error);

      return {
        forms: {
          ...state.forms,
          [formId]: {
            ...form,
            fields: updatedFields,
            isDirty: formIsDirty,
            isValid: formIsValid,
          },
        },
      };
    }),

  setFieldError: (formId, fieldName, error) =>
    set((state) => {
      const form = state.forms[formId];
      if (!form) return state;

      const updatedErrors = {
        ...form.errors,
        [fieldName]: error || '',
      };

      const isValid = Object.values(updatedErrors).every((err) => !err);

      return {
        forms: {
          ...state.forms,
          [formId]: {
            ...form,
            errors: updatedErrors,
            isValid,
          },
        },
      };
    }),

  setFieldTouched: (formId, fieldName, touched = true) =>
    set((state) => {
      const form = state.forms[formId];
      if (!form || !form.fields[fieldName]) return state;

      return {
        forms: {
          ...state.forms,
          [formId]: {
            ...form,
            fields: {
              ...form.fields,
              [fieldName]: {
                ...form.fields[fieldName],
                touched,
              },
            },
          },
        },
      };
    }),

  setFormSubmitting: (formId, isSubmitting) =>
    set((state) => {
      const form = state.forms[formId];
      if (!form) return state;

      return {
        forms: {
          ...state.forms,
          [formId]: {
            ...form,
            isSubmitting,
          },
        },
      };
    }),

  setFormErrors: (formId, errors) =>
    set((state) => {
      const form = state.forms[formId];
      if (!form) return state;

      const isValid = Object.values(errors).every((error) => !error);

      return {
        forms: {
          ...state.forms,
          [formId]: {
            ...form,
            errors,
            isValid,
          },
        },
      };
    }),

  incrementSubmitCount: (formId) =>
    set((state) => {
      const form = state.forms[formId];
      if (!form) return state;

      return {
        forms: {
          ...state.forms,
          [formId]: {
            ...form,
            submitCount: form.submitCount + 1,
          },
        },
      };
    }),

  // Getters
  getFormState: (formId) => get().forms[formId],
  getFieldValue: (formId, fieldName) => get().forms[formId]?.fields[fieldName]?.value,
  getFieldError: (formId, fieldName) => get().forms[formId]?.errors[fieldName],
  isFieldTouched: (formId, fieldName) => get().forms[formId]?.fields[fieldName]?.touched ?? false,
  isFieldDirty: (formId, fieldName) => get().forms[formId]?.fields[fieldName]?.dirty ?? false,
  isFormValid: (formId) => get().forms[formId]?.isValid ?? false,
  isFormDirty: (formId) => get().forms[formId]?.isDirty ?? false,
}));
