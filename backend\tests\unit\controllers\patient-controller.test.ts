import request from 'supertest';
import express from 'express';
import { PatientController } from '@/controllers/patientController';
import { PatientService } from '@/services/patientService';
import { authenticate } from '@/middleware/auth';
import { createTestUser } from '../../setup';

// Mock the services
jest.mock('@/services/patientService');
jest.mock('@/middleware/auth');

const mockPatientService = PatientService as jest.Mocked<typeof PatientService>;
const mockAuthenticate = authenticate as jest.MockedFunction<typeof authenticate>;

describe('PatientController', () => {
  let app: express.Application;
  let testUser: any;

  beforeEach(async () => {
    app = express();
    app.use(express.json());

    // Mock authentication middleware
    mockAuthenticate.mockImplementation((req: any, res: any, next: any) => {
      req.user = testUser;
      next();
    });

    testUser = {
      id: 'test-user-id',
      role: 'CLINICIAN',
      email: '<EMAIL>'
    };

    // Setup routes
    app.get('/patients', PatientController.getPatients);
    app.post('/patients', PatientController.createPatient);
    app.get('/patients/search', PatientController.searchPatients);
    app.get('/patients/:id', PatientController.getPatientById);
    app.put('/patients/:id', PatientController.updatePatient);
    app.delete('/patients/:id', PatientController.deletePatient);

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('GET /patients', () => {
    test('should return patients list successfully', async () => {
      const mockPatients = {
        success: true,
        data: [
          {
            id: 'patient-1',
            firstName: 'John',
            lastName: 'Doe',
            patientId: 'P-001'
          }
        ],
        pagination: {
          page: 1,
          limit: 10,
          total: 1,
          totalPages: 1
        }
      };

      mockPatientService.getPatients.mockResolvedValue(mockPatients);

      const response = await request(app)
        .get('/patients')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(1);
      expect(mockPatientService.getPatients).toHaveBeenCalledWith(
        expect.objectContaining({
          page: 1,
          limit: 10
        }),
        testUser.id,
        testUser.role
      );
    });

    test('should handle query parameters correctly', async () => {
      mockPatientService.getPatients.mockResolvedValue({
        success: true,
        data: [],
        pagination: { page: 2, limit: 5, total: 0, totalPages: 0 }
      });

      await request(app)
        .get('/patients?page=2&limit=5&search=John&gender=MALE')
        .expect(200);

      expect(mockPatientService.getPatients).toHaveBeenCalledWith(
        expect.objectContaining({
          page: 2,
          limit: 5,
          search: 'John',
          gender: 'MALE'
        }),
        testUser.id,
        testUser.role
      );
    });

    test('should return 401 when user is not authenticated', async () => {
      mockAuthenticate.mockImplementation((req: any, res: any, next: any) => {
        req.user = null;
        next();
      });

      const response = await request(app)
        .get('/patients')
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Authentication required');
    });
  });

  describe('POST /patients', () => {
    test('should create patient successfully', async () => {
      const patientData = {
        firstName: 'Jane',
        lastName: 'Smith',
        dateOfBirth: '1990-01-01',
        gender: 'FEMALE',
        email: '<EMAIL>'
      };

      const mockCreatedPatient = {
        success: true,
        data: {
          patient: {
            id: 'new-patient-id',
            ...patientData,
            patientId: 'P-002'
          }
        },
        message: 'Patient created successfully'
      };

      mockPatientService.createPatient.mockResolvedValue(mockCreatedPatient);

      const response = await request(app)
        .post('/patients')
        .send(patientData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.patient.firstName).toBe('Jane');
      expect(mockPatientService.createPatient).toHaveBeenCalledWith(
        expect.objectContaining(patientData),
        testUser.id,
        expect.objectContaining({
          ipAddress: expect.any(String),
          userAgent: expect.any(String)
        })
      );
    });

    test('should validate required fields', async () => {
      const invalidData = {
        firstName: 'Jane'
        // Missing required fields
      };

      const response = await request(app)
        .post('/patients')
        .send(invalidData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(mockPatientService.createPatient).not.toHaveBeenCalled();
    });

    test('should handle service errors', async () => {
      const patientData = {
        firstName: 'Jane',
        lastName: 'Smith',
        dateOfBirth: '1990-01-01',
        gender: 'FEMALE'
      };

      mockPatientService.createPatient.mockRejectedValue(
        new Error('Database connection failed')
      );

      await request(app)
        .post('/patients')
        .send(patientData)
        .expect(500);

      expect(mockPatientService.createPatient).toHaveBeenCalled();
    });
  });

  describe('GET /patients/search', () => {
    test('should search patients successfully', async () => {
      const mockSearchResults = {
        success: true,
        data: [
          {
            id: 'patient-1',
            firstName: 'John',
            lastName: 'Doe',
            patientId: 'P-001'
          }
        ]
      };

      mockPatientService.searchPatients.mockResolvedValue(mockSearchResults);

      const response = await request(app)
        .get('/patients/search?term=John')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(1);
      expect(mockPatientService.searchPatients).toHaveBeenCalledWith(
        'John',
        testUser.id,
        testUser.role
      );
    });

    test('should require search term', async () => {
      const response = await request(app)
        .get('/patients/search')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Search term is required');
      expect(mockPatientService.searchPatients).not.toHaveBeenCalled();
    });
  });

  describe('GET /patients/:id', () => {
    test('should get patient by ID successfully', async () => {
      const mockPatient = {
        success: true,
        data: {
          patient: {
            id: 'patient-1',
            firstName: 'John',
            lastName: 'Doe',
            patientId: 'P-001'
          }
        }
      };

      mockPatientService.getPatientById.mockResolvedValue(mockPatient);

      const response = await request(app)
        .get('/patients/patient-1')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.patient.id).toBe('patient-1');
      expect(mockPatientService.getPatientById).toHaveBeenCalledWith(
        'patient-1',
        testUser.id,
        testUser.role
      );
    });

    test('should handle patient not found', async () => {
      mockPatientService.getPatientById.mockRejectedValue(
        new Error('Patient not found')
      );

      await request(app)
        .get('/patients/non-existent-id')
        .expect(500);

      expect(mockPatientService.getPatientById).toHaveBeenCalledWith(
        'non-existent-id',
        testUser.id,
        testUser.role
      );
    });
  });

  describe('PUT /patients/:id', () => {
    test('should update patient successfully', async () => {
      const updateData = {
        firstName: 'John Updated',
        phone: '+**********'
      };

      const mockUpdatedPatient = {
        success: true,
        data: {
          patient: {
            id: 'patient-1',
            firstName: 'John Updated',
            lastName: 'Doe',
            phone: '+**********'
          }
        },
        message: 'Patient updated successfully'
      };

      mockPatientService.updatePatient.mockResolvedValue(mockUpdatedPatient);

      const response = await request(app)
        .put('/patients/patient-1')
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.patient.firstName).toBe('John Updated');
      expect(mockPatientService.updatePatient).toHaveBeenCalledWith(
        'patient-1',
        expect.objectContaining(updateData),
        testUser.id,
        testUser.role,
        expect.objectContaining({
          ipAddress: expect.any(String),
          userAgent: expect.any(String)
        })
      );
    });
  });

  describe('DELETE /patients/:id', () => {
    test('should delete patient successfully', async () => {
      const mockDeleteResult = {
        success: true,
        data: null,
        message: 'Patient deleted successfully'
      };

      mockPatientService.deletePatient.mockResolvedValue(mockDeleteResult);

      const response = await request(app)
        .delete('/patients/patient-1')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Patient deleted successfully');
      expect(mockPatientService.deletePatient).toHaveBeenCalledWith(
        'patient-1',
        testUser.id,
        testUser.role,
        expect.objectContaining({
          ipAddress: expect.any(String),
          userAgent: expect.any(String)
        })
      );
    });
  });
});
