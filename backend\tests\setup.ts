import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Global test setup
const prisma = new PrismaClient();

beforeAll(async () => {
  // Ensure test database is clean
  await cleanDatabase();
});

afterAll(async () => {
  // Clean up after all tests
  await cleanDatabase();
  await prisma.$disconnect();
});

beforeEach(async () => {
  // Clean database before each test to ensure isolation
  await cleanDatabase();
});

/**
 * Clean all data from test database
 */
async function cleanDatabase() {
  const tablenames = await prisma.$queryRaw<Array<{ name: string }>>`
    SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name NOT LIKE '_prisma_%';
  `;

  for (const { name } of tablenames) {
    try {
      await prisma.$executeRawUnsafe(`DELETE FROM "${name}";`);
    } catch (error) {
      console.warn(`Failed to clean table ${name}:`, error);
    }
  }
}

/**
 * Create test user for authentication
 */
export async function createTestUser(role: 'ADMIN' | 'CLINICIAN' | 'STAFF' = 'CLINICIAN') {
  const bcrypt = require('bcrypt');
  const hashedPassword = await bcrypt.hash('testpassword123', 4);

  return await prisma.user.create({
    data: {
      username: `testuser_${Date.now()}`,
      email: `test_${Date.now()}@example.com`,
      password: hashedPassword,
      firstName: 'Test',
      lastName: 'User',
      role,
      isActive: true,
    },
  });
}

/**
 * Create test patient
 */
export async function createTestPatient(createdBy: string) {
  return await prisma.patient.create({
    data: {
      patientId: `P-TEST-${Date.now()}`,
      firstName: 'John',
      lastName: 'Doe',
      dateOfBirth: new Date('1990-01-01'),
      gender: 'MALE',
      phone: '+**********',
      email: '<EMAIL>',
      address: JSON.stringify({
        street: '123 Test St',
        city: 'Test City',
        state: 'TS',
        zipCode: '12345',
        country: 'USA'
      }),
      occupation: 'Software Engineer',
      education: 'BACHELORS',
      maritalStatus: 'SINGLE',
      emergencyContact: JSON.stringify({
        name: 'Jane Doe',
        relationship: 'Sister',
        phone: '+**********'
      }),
      insuranceInfo: JSON.stringify({
        provider: 'Test Insurance',
        policyNumber: 'TEST123456',
        groupNumber: 'GRP789'
      }),
      medicalHistory: JSON.stringify({
        allergies: ['Penicillin'],
        medications: ['Vitamin D'],
        conditions: []
      }),
      createdBy,
      isActive: true,
      isDeleted: false,
    },
  });
}

/**
 * Create test lab result
 */
export async function createTestLabResult(patientId: string, createdBy: string) {
  return await prisma.labResult.create({
    data: {
      patientId,
      testType: 'CBC',
      testDate: new Date(),
      orderedBy: 'Dr. Test Physician',
      labName: 'Test Laboratory',
      results: JSON.stringify({
        WBC: { value: 7.5, unit: 'K/uL', normalRange: '4.0-11.0', isNormal: true },
        RBC: { value: 4.5, unit: 'M/uL', normalRange: '4.2-5.4', isNormal: true },
        Hemoglobin: { value: 14.2, unit: 'g/dL', normalRange: '12.0-16.0', isNormal: true },
        Hematocrit: { value: 42.1, unit: '%', normalRange: '36.0-46.0', isNormal: true },
        Platelets: { value: 250, unit: 'K/uL', normalRange: '150-450', isNormal: true }
      }),
      normalRanges: JSON.stringify({
        WBC: '4.0-11.0 K/uL',
        RBC: '4.2-5.4 M/uL',
        Hemoglobin: '12.0-16.0 g/dL',
        Hematocrit: '36.0-46.0 %',
        Platelets: '150-450 K/uL'
      }),
      status: 'COMPLETED',
      notes: 'Normal CBC results',
      createdBy,
      isDeleted: false,
    },
  });
}

/**
 * Create test appointment
 */
export async function createTestAppointment(patientId: string, providerId: string) {
  return await prisma.appointment.create({
    data: {
      patientId,
      providerId,
      date: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
      duration: 60,
      type: 'INITIAL_CONSULTATION',
      status: 'SCHEDULED',
      notes: 'Initial consultation appointment',
    },
  });
}

// Export prisma instance for tests
export { prisma };
