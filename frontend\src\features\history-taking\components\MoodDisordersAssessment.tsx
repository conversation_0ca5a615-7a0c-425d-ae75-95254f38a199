import React, { useState, useMemo } from 'react';
import type { CriterionTemplate, DisorderAssessment, AssessmentProgress } from '../types';
import { getDisordersByCategory, validateDisorderCriteria } from '../data/disorders-registry';
import { <PERSON>, CardHeader, CardTitle, CardContent, Badge } from '../../../components/ui';
import { CheckCircle, Circle, AlertCircle, Info } from 'lucide-react';

interface MoodDisordersAssessmentProps {
  patientId: string;
  onAssessmentChange: (assessment: Partial<DisorderAssessment>) => void;
  initialAssessment?: Partial<DisorderAssessment>;
}

const MoodDisordersAssessment: React.FC<MoodDisordersAssessmentProps> = ({
  patientId,
  onAssessmentChange,
  initialAssessment
}) => {
  const [selectedDisorder, setSelectedDisorder] = useState<string>(
    initialAssessment?.disorderId || ''
  );
  const [criteriaResponses, setCriteriaResponses] = useState<Record<string, boolean>>(
    initialAssessment?.criteria?.reduce((acc, c) => ({ ...acc, [c.id]: c.present }), {}) || {}
  );
  const [severityRatings, setSeverityRatings] = useState<Record<string, number>>(
    initialAssessment?.criteria?.reduce((acc, c) => ({ ...acc, [c.id]: c.severity || 1 }), {}) || {}
  );
  const [notes, setNotes] = useState<Record<string, string>>(
    initialAssessment?.criteria?.reduce((acc, c) => ({ ...acc, [c.id]: c.comments || '' }), {}) || {}
  );

  // Get mood disorders
  const moodDisorders = useMemo(() => {
    return [
      ...getDisordersByCategory('depressive'),
      ...getDisordersByCategory('bipolar-related')
    ];
  }, []);

  const selectedDisorderData = useMemo(() => {
    return moodDisorders.find(d => d.id === selectedDisorder);
  }, [moodDisorders, selectedDisorder]);

  // Calculate assessment progress
  const assessmentProgress = useMemo((): AssessmentProgress => {
    if (!selectedDisorderData) {
      return {
        totalCriteria: 0,
        completedCriteria: 0,
        requiredCriteria: 0,
        metCriteria: 0,
        percentComplete: 0,
        isValid: false,
        canDiagnose: false,
        missingRequired: []
      };
    }

    const validation = validateDisorderCriteria(selectedDisorder, criteriaResponses);
    const totalCriteria = selectedDisorderData.criteria.length;
    const completedCriteria = Object.keys(criteriaResponses).length;
    const requiredCriteria = selectedDisorderData.criteria.filter(c => c.required).length;
    const metCriteria = validation.metCriteria.length;

    return {
      totalCriteria,
      completedCriteria,
      requiredCriteria,
      metCriteria,
      percentComplete: totalCriteria > 0 ? (completedCriteria / totalCriteria) * 100 : 0,
      isValid: validation.isValid,
      canDiagnose: validation.canDiagnose,
      missingRequired: validation.missingRequired
    };
  }, [selectedDisorderData, selectedDisorder, criteriaResponses]);

  const handleCriterionChange = (criterionId: string, present: boolean) => {
    const newResponses = { ...criteriaResponses, [criterionId]: present };
    setCriteriaResponses(newResponses);
    updateAssessment(newResponses);
  };

  const handleSeverityChange = (criterionId: string, severity: number) => {
    setSeverityRatings(prev => ({ ...prev, [criterionId]: severity }));
    updateAssessment();
  };

  const handleNotesChange = (criterionId: string, comment: string) => {
    setNotes(prev => ({ ...prev, [criterionId]: comment }));
    updateAssessment();
  };

  const updateAssessment = (newResponses = criteriaResponses) => {
    if (!selectedDisorderData) return;

    const criteria = selectedDisorderData.criteria.map(criterion => ({
      id: criterion.id,
      code: criterion.code,
      description: criterion.description,
      present: newResponses[criterion.id] || false,
      severity: severityRatings[criterion.id] || 1,
      comments: notes[criterion.id] || ''
    }));

    const assessment: Partial<DisorderAssessment> = {
      disorderId: selectedDisorder,
      patientId,
      criteria,
      assessmentDate: new Date().toISOString(),
      status: 'draft'
    };

    onAssessmentChange(assessment);
  };

  const renderCriterion = (criterion: CriterionTemplate) => {
    const isPresent = criteriaResponses[criterion.id] || false;
    const severity = severityRatings[criterion.id] || 1;
    const comment = notes[criterion.id] || '';

    return (
      <Card key={criterion.id} className="mb-4">
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            <button
              onClick={() => handleCriterionChange(criterion.id, !isPresent)}
              className="mt-1 flex-shrink-0"
            >
              {isPresent ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <Circle className="h-5 w-5 text-gray-400" />
              )}
            </button>
            
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <Badge variant="outline">{criterion.code}</Badge>
                {criterion.required && (
                  <Badge variant="destructive" className="text-xs">Required</Badge>
                )}
              </div>
              
              <p className="text-sm text-gray-700 mb-3">{criterion.description}</p>
              
              {/* Sub-criteria */}
              {criterion.subCriteria && criterion.subCriteria.length > 0 && (
                <div className="ml-4 space-y-2">
                  {criterion.subCriteria?.map((subCriterion: CriterionTemplate) => (
                    <div key={subCriterion.id} className="flex items-start space-x-2">
                      <button
                        onClick={() => handleCriterionChange(subCriterion.id, !criteriaResponses[subCriterion.id])}
                        className="mt-1 flex-shrink-0"
                      >
                        {criteriaResponses[subCriterion.id] ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : (
                          <Circle className="h-4 w-4 text-gray-400" />
                        )}
                      </button>
                      <div className="flex-1">
                        <Badge variant="outline" className="text-xs mb-1">{subCriterion.code}</Badge>
                        <p className="text-xs text-gray-600">{subCriterion.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              {/* Severity rating */}
              {isPresent && criterion.type === 'symptom' && (
                <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Severity (1=Mild, 2=Moderate, 3=Severe, 4=Extreme)
                  </label>
                  <div className="flex space-x-2">
                    {[1, 2, 3, 4].map(level => (
                      <button
                        key={level}
                        onClick={() => handleSeverityChange(criterion.id, level)}
                        className={`px-3 py-1 rounded text-sm ${
                          severity === level
                            ? 'bg-blue-600 text-white'
                            : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        {level}
                      </button>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Comments */}
              {isPresent && (
                <div className="mt-3">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Comments
                  </label>
                  <textarea
                    value={comment}
                    onChange={(e) => handleNotesChange(criterion.id, e.target.value)}
                    placeholder="Additional notes about this criterion..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    rows={2}
                  />
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Disorder Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Select Mood Disorder</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {moodDisorders.map(disorder => (
              <button
                key={disorder.id}
                onClick={() => setSelectedDisorder(disorder.id)}
                className={`p-4 text-left border rounded-lg transition-colors ${
                  selectedDisorder === disorder.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="font-medium text-gray-900">{disorder.name}</div>
                <div className="text-sm text-gray-500 mt-1">{disorder.code}</div>
                <div className="text-xs text-gray-400 mt-2">{disorder.description}</div>
              </button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Assessment Progress */}
      {selectedDisorderData && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-gray-900">Assessment Progress</h3>
                <p className="text-sm text-gray-500">
                  {assessmentProgress.completedCriteria} of {assessmentProgress.totalCriteria} criteria assessed
                </p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900">
                  {Math.round(assessmentProgress.percentComplete)}%
                </div>
                {assessmentProgress.canDiagnose ? (
                  <Badge variant="success">Can Diagnose</Badge>
                ) : (
                  <Badge variant="secondary">Incomplete</Badge>
                )}
              </div>
            </div>
            
            {assessmentProgress.missingRequired.length > 0 && (
              <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center space-x-2">
                  <AlertCircle className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm font-medium text-yellow-800">
                    Missing required criteria: {assessmentProgress.missingRequired.join(', ')}
                  </span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Disorder Information */}
      {selectedDisorderData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <span>{selectedDisorderData.name}</span>
              <Badge variant="outline">{selectedDisorderData.code}</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Description</h4>
                <p className="text-sm text-gray-600">{selectedDisorderData.description}</p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Key Diagnostic Features</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  {selectedDisorderData.diagnosticFeatures.map((feature, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <Info className="h-3 w-3 mt-1 text-blue-500 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Criteria Assessment */}
      {selectedDisorderData && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Diagnostic Criteria</h3>
          {selectedDisorderData.criteria.map(criterion => 
            renderCriterion(criterion)
          )}
        </div>
      )}
    </div>
  );
};

export default MoodDisordersAssessment;
