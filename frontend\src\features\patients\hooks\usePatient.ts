import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type { Patient } from '../types';
import { patientsApi } from '../services/patientsApi';
import { queryKeys, getInvalidationKeys } from '../../../lib/queryKeys';

export const usePatient = (id: string) => {
  const queryClient = useQueryClient();

  // Query for fetching individual patient
  const {
    data: patient,
    isLoading: loading,
    error,
    refetch: fetchPatient,
  } = useQuery({
    queryKey: queryKeys.patients.detail(id),
    queryFn: () => patientsApi.getById(id),
    enabled: !!id, // Only run query if id is provided
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Mutation for updating this specific patient
  const updatePatientMutation = useMutation({
    mutationFn: (updates: Partial<Patient>) => patientsApi.update(id, updates),
    onSuccess: (updatedPatient) => {
      // Update the individual patient cache
      queryClient.setQueryData(queryKeys.patients.detail(id), updatedPatient);
      
      // Update the patient in any lists that might contain it
      queryClient.setQueriesData(
        { queryKey: queryKeys.patients.lists() },
        (old: Patient[] | undefined) => {
          if (!old) return old;
          return old.map((p) => (p.id === id ? updatedPatient : p));
        }
      );

      // Invalidate related queries
      getInvalidationKeys.onPatientChange().forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });
    },
  });

  // Mutation for deleting this specific patient
  const deletePatientMutation = useMutation({
    mutationFn: () => patientsApi.delete(id),
    onSuccess: () => {
      // Remove the individual patient cache
      queryClient.removeQueries({ queryKey: queryKeys.patients.detail(id) });
      
      // Remove the patient from any lists that might contain it
      queryClient.setQueriesData(
        { queryKey: queryKeys.patients.lists() },
        (old: Patient[] | undefined) => {
          if (!old) return old;
          return old.filter((p) => p.id !== id);
        }
      );

      // Invalidate related queries
      getInvalidationKeys.onPatientChange().forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });
    },
  });

  return {
    patient,
    loading,
    error: error?.message || null,
    fetchPatient,
    updatePatient: updatePatientMutation.mutateAsync,
    deletePatient: deletePatientMutation.mutateAsync,
    // Additional mutation states
    isUpdating: updatePatientMutation.isPending,
    isDeleting: deletePatientMutation.isPending,
  };
};
