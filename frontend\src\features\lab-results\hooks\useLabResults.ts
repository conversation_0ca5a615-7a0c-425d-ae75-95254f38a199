import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type { LabResult, LabResultFormData, LabResultFilters } from '../types';
import { labResultsApi } from '../services/labResultsApi';
import { queryKeys, getInvalidationKeys } from '../../../lib/queryKeys';

export const useLabResults = (filters?: LabResultFilters) => {
  const queryClient = useQueryClient();

  // Query for fetching lab results
  const {
    data: labResults = [],
    isLoading: loading,
    error,
    refetch: fetchLabResults,
  } = useQuery({
    queryKey: queryKeys.labResults.list(filters),
    queryFn: () => labResultsApi.getAll(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Mutation for creating lab results
  const createLabResultMutation = useMutation({
    mutationFn: (labResultData: LabResultFormData) => labResultsApi.create(labResultData),
    onSuccess: (newLabResult) => {
      // Add to the lab results list cache
      queryClient.setQueriesData(
        { queryKey: queryKeys.labResults.lists() },
        (old: LabResult[] | undefined) => {
          if (!old) return [newLabResult];
          return [newLabResult, ...old];
        }
      );

      // Invalidate related queries
      getInvalidationKeys.onLabResultChange().forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });
    },
  });

  // Mutation for updating lab results
  const updateLabResultMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<LabResultFormData> }) =>
      labResultsApi.update(id, updates),
    onSuccess: (updatedLabResult) => {
      // Update the lab result in any lists that might contain it
      queryClient.setQueriesData(
        { queryKey: queryKeys.labResults.lists() },
        (old: LabResult[] | undefined) => {
          if (!old) return old;
          return old.map((lr) => (lr.id === updatedLabResult.id ? updatedLabResult : lr));
        }
      );

      // Update individual lab result cache
      queryClient.setQueryData(
        queryKeys.labResults.detail(updatedLabResult.id),
        updatedLabResult
      );

      // Invalidate related queries
      getInvalidationKeys.onLabResultChange().forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });
    },
  });

  // Mutation for deleting lab results
  const deleteLabResultMutation = useMutation({
    mutationFn: (id: string) => labResultsApi.delete(id),
    onSuccess: (_, deletedId) => {
      // Remove the lab result from any lists that might contain it
      queryClient.setQueriesData(
        { queryKey: queryKeys.labResults.lists() },
        (old: LabResult[] | undefined) => {
          if (!old) return old;
          return old.filter((lr) => lr.id !== deletedId);
        }
      );

      // Remove individual lab result cache
      queryClient.removeQueries({ queryKey: queryKeys.labResults.detail(deletedId) });

      // Invalidate related queries
      getInvalidationKeys.onLabResultChange().forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });
    },
  });

  return {
    labResults,
    loading,
    error: error?.message || null,
    fetchLabResults,
    createLabResult: createLabResultMutation.mutateAsync,
    updateLabResult: updateLabResultMutation.mutateAsync,
    deleteLabResult: deleteLabResultMutation.mutateAsync,
    // Additional mutation states
    isCreating: createLabResultMutation.isPending,
    isUpdating: updateLabResultMutation.isPending,
    isDeleting: deleteLabResultMutation.isPending,
  };
};

export const useLabResult = (id: string) => {

  // Query for fetching individual lab result
  const {
    data: labResult,
    isLoading: loading,
    error,
    refetch: fetchLabResult,
  } = useQuery({
    queryKey: queryKeys.labResults.detail(id),
    queryFn: () => labResultsApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return {
    labResult,
    loading,
    error: error?.message || null,
    fetchLabResult,
  };
};

export const usePatientLabResults = (patientId: string, filters?: { testType?: string; limit?: number; dateFrom?: string; dateTo?: string }) => {
  // Query for fetching patient's lab results
  const {
    data: labResults = [],
    isLoading: loading,
    error,
    refetch: fetchLabResults,
  } = useQuery({
    queryKey: queryKeys.labResults.byPatient(patientId),
    queryFn: () => labResultsApi.getPatientLabResults(patientId, filters),
    enabled: !!patientId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return {
    labResults,
    loading,
    error: error?.message || null,
    fetchLabResults,
  };
};

export const useLabResultTrends = (patientId: string, filters?: { testType?: string; dateFrom?: string; dateTo?: string }) => {
  // Query for fetching lab result trends
  const {
    data: trends,
    isLoading: loading,
    error,
    refetch: fetchTrends,
  } = useQuery({
    queryKey: [...queryKeys.labResults.byPatient(patientId), 'trends', filters],
    queryFn: () => labResultsApi.getLabResultTrends(patientId, filters),
    enabled: !!patientId,
    staleTime: 10 * 60 * 1000, // 10 minutes (trends don't change as frequently)
  });

  return {
    trends,
    loading,
    error: error?.message || null,
    fetchTrends,
  };
};
