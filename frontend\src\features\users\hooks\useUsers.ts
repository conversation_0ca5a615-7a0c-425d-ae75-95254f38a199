import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type { User, UserFormData, UserFilters } from '../types';
import { usersApi } from '../services/usersApi';
import { queryKeys, getInvalidationKeys } from '../../../lib/queryKeys';

export const useUsers = (filters?: UserFilters) => {
  const queryClient = useQueryClient();

  // Query for fetching users
  const {
    data: users = [],
    isLoading: loading,
    error,
    refetch: fetchUsers,
  } = useQuery({
    queryKey: queryKeys.users.list(filters),
    queryFn: () => usersApi.getAll(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Mutation for creating users
  const createUserMutation = useMutation({
    mutationFn: (userData: UserFormData) => usersApi.create(userData),
    onSuccess: (newUser) => {
      // Update the cache with the new user
      queryClient.setQueryData(queryKeys.users.list(filters), (old: User[] = []) => [
        ...old,
        newUser,
      ]);
      // Invalidate related queries
      getInvalidationKeys.onUserChange().forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });
    },
  });

  // Mutation for updating users
  const updateUserMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<UserFormData> }) =>
      usersApi.update(id, updates),
    onSuccess: (updatedUser) => {
      // Update the cache with the updated user
      queryClient.setQueryData(queryKeys.users.list(filters), (old: User[] = []) =>
        old.map((u) => (u.id === updatedUser.id ? updatedUser : u))
      );
      // Update individual user cache if it exists
      queryClient.setQueryData(queryKeys.users.detail(updatedUser.id), updatedUser);
      // Invalidate related queries
      getInvalidationKeys.onUserChange().forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });
    },
  });

  // Mutation for deleting users
  const deleteUserMutation = useMutation({
    mutationFn: (id: string) => usersApi.delete(id),
    onSuccess: (_, deletedId) => {
      // Remove the user from the cache
      queryClient.setQueryData(queryKeys.users.list(filters), (old: User[] = []) =>
        old.filter((u) => u.id !== deletedId)
      );
      // Remove individual user cache
      queryClient.removeQueries({ queryKey: queryKeys.users.detail(deletedId) });
      // Invalidate related queries
      getInvalidationKeys.onUserChange().forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });
    },
  });

  return {
    users,
    loading,
    error: error?.message || null,
    fetchUsers,
    createUser: createUserMutation.mutateAsync,
    updateUser: (id: string, updates: Partial<UserFormData>) =>
      updateUserMutation.mutateAsync({ id, updates }),
    deleteUser: deleteUserMutation.mutateAsync,
    // Additional mutation states
    isCreating: createUserMutation.isPending,
    isUpdating: updateUserMutation.isPending,
    isDeleting: deleteUserMutation.isPending,
  };
};
