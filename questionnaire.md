Okay, I've integrated the new content into the `questionnaire.md` document, aiming to expand and refine the existing sections while keeping the core structure and details intact. I've added the PCL-5 assessment and expanded on the implementation instructions for scoring, frontend components, and specific utilities.

Here's the enhanced `questionnaire.md`:

```markdown
# 📋 PSYCHOLOGICAL ASSESSMENT QUESTIONNAIRES GUIDE

## PURPOSE
This guide provides detailed implementation instructions for psychological assessment tools in the Psychiatry Patient Management System. Each questionnaire includes the exact items, scoring algorithms, and clinical interpretations, along with implementation notes.

---

## 🧠 DEPRESSION ASSESSMENTS

### PHQ-9 (Patient Health Questionnaire - Depression)

**Database Storage:**
```json
{
  "testName": "PHQ-9",
  "testCategory": "depression",
  "items": [
    {
      "id": 1,
      "question": "Little interest or pleasure in doing things",
      "options": ["Not at all", "Several days", "More than half the days", "Nearly every day"],
      "scores": [0, 1, 2, 3]
    },
    {
      "id": 2,
      "question": "Feeling down, depressed, or hopeless",
      "options": ["Not at all", "Several days", "More than half the days", "Nearly every day"],
      "scores": [0, 1, 2, 3]
    },
    {
      "id": 3,
      "question": "Trouble falling or staying asleep, or sleeping too much",
      "options": ["Not at all", "Several days", "More than half the days", "Nearly every day"],
      "scores": [0, 1, 2, 3]
    },
    {
      "id": 4,
      "question": "Feeling tired or having little energy",
      "options": ["Not at all", "Several days", "More than half the days", "Nearly every day"],
      "scores": [0, 1, 2, 3]
    },
    {
      "id": 5,
      "question": "Poor appetite or overeating",
      "options": ["Not at all", "Several days", "More than half the days", "Nearly every day"],
      "scores": [0, 1, 2, 3]
    },
    {
      "id": 6,
      "question": "Feeling bad about yourself - or that you are a failure or have let yourself or your family down",
      "options": ["Not at all", "Several days", "More than half the days", "Nearly every day"],
      "scores": [0, 1, 2, 3]
    },
    {
      "id": 7,
      "question": "Trouble concentrating on things, such as reading the newspaper or watching television",
      "options": ["Not at all", "Several days", "More than half the days", "Nearly every day"],
      "scores": [0, 1, 2, 3]
    },
    {
      "id": 8,
      "question": "Moving or speaking so slowly that other people could have noticed? Or the opposite - being so fidgety or restless that you have been moving around a lot more than usual",
      "options": ["Not at all", "Several days", "More than half the days", "Nearly every day"],
      "scores": [0, 1, 2, 3]
    },
    {
      "id": 9,
      "question": "Thoughts that you would be better off dead, or of hurting yourself in some way",
      "options": ["Not at all", "Several days", "More than half the days", "Nearly every day"],
      "scores": [0, 1, 2, 3]
    }
  ]
}
```

**Scoring Algorithm:**
```typescript
// In psychTestScoring.ts
export const scorePHQ9 = (responses: number[]): TestResult => {
  if (responses.length !== 9) {
    throw new Error('PHQ-9 requires exactly 9 responses');
  }
  
  const totalScore = responses.reduce((sum, score) => sum + score, 0);
  
  const severity = 
    totalScore <= 4 ? 'Minimal depression' :
    totalScore <= 9 ? 'Mild depression' :
    totalScore <= 14 ? 'Moderate depression' :
    totalScore <= 19 ? 'Moderately severe depression' :
    'Severe depression';
  
  const interpretation = getDepressionInterpretation(totalScore);
  const riskFlags: string[] = [];
  
  // Suicidal ideation flag (item 9)
  if (responses[8] > 0) {
    riskFlags.push('SUICIDAL_IDEATION_REPORTED');
  }
  if (responses[8] >= 2) {
    riskFlags.push('HIGH_SUICIDE_RISK');
  }
  
  // Functional impairment assessment (items 1, 2, 4, 6)
  const coreSymptoms = [responses[0], responses[1], responses[3], responses[5]];
  const coreScore = coreSymptoms.reduce((sum, score) => sum + score, 0);
  if (coreScore >= 6) {
    riskFlags.push('SIGNIFICANT_FUNCTIONAL_IMPAIRMENT');
  }
  
  const recommendations = generateDepressionRecommendations(totalScore, responses[8]);
  
  return {
    totalScore,
    severity,
    interpretation,
    riskFlags,
    recommendations
  };
};

const getDepressionInterpretation = (score: number): string => {
  if (score <= 4) return 'Minimal or no depression symptoms. Monitor at routine intervals.';
  if (score <= 9) return 'Mild depression symptoms. Consider watchful waiting, patient education, and repeat screening in 2-4 weeks.';
  if (score <= 14) return 'Moderate depression. Active treatment recommended with antidepressant medication or evidence-based psychotherapy.';
  if (score <= 19) return 'Moderately severe depression. Active treatment with combination of medication and psychotherapy strongly recommended.';
  return 'Severe depression. Immediate clinical attention required. Consider intensive treatment, partial hospitalization, or inpatient care.';
};

const generateDepressionRecommendations = (totalScore: number, suicidalItem: number): string[] => {
  const recommendations = [];
  
  if (suicidalItem > 0) {
    recommendations.push('🚨 URGENT: Complete suicide risk assessment required');
    recommendations.push('Consider safety planning and increased monitoring');
  }
  
  if (totalScore >= 10) {
    recommendations.push('Initiate evidence-based treatment (medication or psychotherapy)');
    recommendations.push('Schedule follow-up within 1-2 weeks');
  }
  
  if (totalScore >= 15) {
    recommendations.push('Consider combination therapy (medication + psychotherapy)');
    recommendations.push('Evaluate need for psychiatric consultation');
  }
  
  if (totalScore >= 20) {
    recommendations.push('Consider intensive outpatient program or partial hospitalization');
    recommendations.push('Evaluate need for inpatient psychiatric care');
  }
  
  recommendations.push('Repeat PHQ-9 at each visit to monitor treatment response');
  
  return recommendations;
};
```

**Clinical Interpretation:**
- **0-4**: Minimal depression symptoms
- **5-9**: Mild depression; watchful waiting, repeat PHQ-9 at follow-up
- **10-14**: Moderate depression; treatment plan, antidepressants or psychotherapy
- **15-19**: Moderately severe depression; active treatment with antidepressants or psychotherapy
- **20-27**: Severe depression; immediate treatment, consider hospitalization if item 9 > 1

---

## 😰 ANXIETY ASSESSMENTS

### GAD-7 (Generalized Anxiety Disorder Scale)

**Database Storage:**
```json
{
  "testName": "GAD-7",
  "testCategory": "anxiety",
  "items": [
    {
      "id": 1,
      "question": "Feeling nervous, anxious, or on edge",
      "options": ["Not at all", "Several days", "More than half the days", "Nearly every day"],
      "scores": [0, 1, 2, 3]
    },
    {
      "id": 2,
      "question": "Not being able to stop or control worrying",
      "options": ["Not at all", "Several days", "More than half the days", "Nearly every day"],
      "scores": [0, 1, 2, 3]
    },
    {
      "id": 3,
      "question": "Worrying too much about different things",
      "options": ["Not at all", "Several days", "More than half the days", "Nearly every day"],
      "scores": [0, 1, 2, 3]
    },
    {
      "id": 4,
      "question": "Trouble relaxing",
      "options": ["Not at all", "Several days", "More than half the days", "Nearly every day"],
      "scores": [0, 1, 2, 3]
    },
    {
      "id": 5,
      "question": "Being so restless that it is hard to sit still",
      "options": ["Not at all", "Several days", "More than half the days", "Nearly every day"],
      "scores": [0, 1, 2, 3]
    },
    {
      "id": 6,
      "question": "Becoming easily annoyed or irritable",
      "options": ["Not at all", "Several days", "More than half the days", "Nearly every day"],
      "scores": [0, 1, 2, 3]
    },
    {
      "id": 7,
      "question": "Feeling afraid, as if something awful might happen",
      "options": ["Not at all", "Several days", "More than half the days", "Nearly every day"],
      "scores": [0, 1, 2, 3]
    }
  ]
}
```

**Scoring Algorithm:**
```typescript
// In psychTestScoring.ts
export const scoreGAD7 = (responses: number[]): TestResult => {
  if (responses.length !== 7) {
    throw new Error('GAD-7 requires exactly 7 responses');
  }
  
  const totalScore = responses.reduce((sum, score) => sum + score, 0);
  
  const severity = 
    totalScore <= 4 ? 'Minimal anxiety' :
    totalScore <= 9 ? 'Mild anxiety' :
    totalScore <= 14 ? 'Moderate anxiety' :
    'Severe anxiety';
    
  const interpretation = getAnxietyInterpretation(totalScore);
  const riskFlags: string[] = [];
  
  if (totalScore >= 10) {
    riskFlags.push('CLINICALLY_SIGNIFICANT_ANXIETY');
  }
  
  if (totalScore >= 15) {
    riskFlags.push('SEVERE_ANXIETY_SYMPTOMS');
  }
  
  const recommendations = generateAnxietyRecommendations(totalScore);
  
  return {
    totalScore,
    severity,
    interpretation,
    riskFlags,
    recommendations
  };
};

const getAnxietyInterpretation = (score: number): string => {
  if (score <= 4) return 'Minimal anxiety symptoms. Monitor as needed.';
  if (score <= 9) return 'Mild anxiety. Consider counseling or self-help strategies if persistent.';
  if (score <= 14) return 'Moderate anxiety. Probable GAD, active treatment indicated (e.g., CBT, medication).';
  return 'Severe anxiety. Active treatment required, consider combination therapy and careful monitoring.';
};

const generateAnxietyRecommendations = (totalScore: number): string[] => {
  const recommendations = [];
  
  if (totalScore >= 10) {
    recommendations.push('Consider CBT, mindfulness, or relaxation techniques');
    recommendations.push('Discuss potential for anxiety medication');
    recommendations.push('Schedule follow-up to assess response');
  }
  
  if (totalScore >= 15) {
    recommendations.push('Intensify treatment for anxiety');
    recommendations.push('Consider referral for specialized anxiety treatment');
  }
  
  recommendations.push('Repeat GAD-7 at regular intervals');
  
  return recommendations;
};
```

**Clinical Interpretation:**
- **0-4**: Minimal anxiety symptoms
- **5-9**: Mild anxiety; monitor, consider treatment if persistent
- **10-14**: Moderate anxiety; probable GAD, active treatment indicated
- **15-21**: Severe anxiety; active treatment required, consider combination therapy

---

## 🔄 OBSESSIVE-COMPULSIVE DISORDER

### Y-BOCS (Yale-Brown Obsessive Compulsive Scale)

**Database Storage (Obsessions Checklist):**
```json
{
  "testName": "Y-BOCS",
  "testCategory": "ocd",
  "sections": {
    "obsessions_checklist": [
      {
        "category": "Aggressive Obsessions",
        "items": [
          "Fear might harm self",
          "Fear might harm others",
          "Violent or horrific images",
          "Fear of blurting out obscenities or insults",
          "Fear of doing something else embarrassing"
        ]
      },
      {
        "category": "Contamination Obsessions", 
        "items": [
          "Concerns with dirt or germs",
          "Concerns with bodily waste or secretions",
          "Concerns with environmental contaminants",
          "Concerns with household items",
          "Concerns with animals/insects"
        ]
      },
      {
        "category": "Sexual Obsessions",
        "items": [
          "Forbidden or perverse sexual thoughts/images",
          "Sexual behavior toward others",
          "Homosexual obsessions",
          "Sexual behavior toward children",
          "Incestuous obsessions"
        ]
      },
      {
        "category": "Hoarding/Saving Obsessions",
        "items": [
          "Inability to discard items",
          "Need to collect items",
          "Symmetry/exactness concerns"
        ]
      },
      {
        "category": "Religious Obsessions",
        "items": [
          "Concerns with sacrilege/blasphemy",
          "Excess concern with right/wrong",
          "Religious images/thoughts"
        ]
      },
      {
        "category": "Symmetry/Exactness Obsessions",
        "items": [
          "Need for symmetry or exactness",
          "Concerns with getting things 'just right'"
        ]
      },
      {
        "category": "Miscellaneous Obsessions",
        "items": [
          "Need to know or remember",
          "Fear of saying certain things",
          "Fear of not saying just the right thing",
          "Intrusive sounds/words/music"
        ]
      },
      {
        "category": "Somatic Obsessions",
        "items": [
          "Concern with illness or disease",
          "Excessive concern with body part/appearance"
        ]
      }
    ],
    "compulsions_checklist": [
      {
        "category": "Cleaning/Washing Compulsions",
        "items": [
          "Excessive hand washing",
          "Excessive showering/bathing/tooth brushing",
          "Excessive cleaning of household items",
          "Excessive cleaning of other items"
        ]
      },
      {
        "category": "Checking Compulsions",
        "items": [
          "Checking locks, stove, appliances",
          "Checking that nothing terrible did/will happen",
          "Checking that nothing is wrong with body",
          "Checking that didn't make mistake"
        ]
      },
      {
        "category": "Repeating Compulsions",
        "items": [
          "Rereading or rewriting",
          "Need to repeat routine activities",
          "Repeating activities until they feel 'just right'"
        ]
      },
      {
        "category": "Counting Compulsions",
        "items": [
          "Counting while doing activities",
          "Counting objects",
          "Counting to certain numbers"
        ]
      },
      {
        "category": "Ordering/Arranging Compulsions",
        "items": [
          "Need to arrange items in certain ways",
          "Need for symmetry or balance"
        ]
      },
      {
        "category": "Hoarding/Collecting Compulsions",
        "items": [
          "Saving newspapers, mail, containers",
          "Difficulty throwing things away"
        ]
      },
      {
        "category": "Miscellaneous Compulsions",
        "items": [
          "Need to tell, ask, or confess",
          "Need to touch, tap, or rub",
          "Mental rituals (other than checking/counting)"
        ]
      }
    ],
    "severity_scale": [
      {
        "domain": "Obsessions",
        "items": [
          {
            "id": 1,
            "question": "Time occupied by obsessive thoughts - How much of your time is occupied by obsessive thoughts?",
            "options": [
              "0 = None",
              "1 = Less than 1 hr/day or occasional",
              "2 = 1-3 hrs/day or frequent", 
              "3 = Greater than 3 and up to 8 hrs/day or very frequent",
              "4 = Greater than 8 hrs/day or nearly constant"
            ]
          },
          {
            "id": 2, 
            "question": "Interference due to obsessive thoughts - How much do your obsessive thoughts interfere with your social or work functioning?",
            "options": [
              "0 = No interference",
              "1 = Slight interference, but overall performance not impaired",
              "2 = Definite interference, but still manageable",
              "3 = Substantial interference",
              "4 = Severe interference"
            ]
          },
          {
            "id": 3,
            "question": "Distress associated with obsessive thoughts - How much distress do your obsessive thoughts cause you?",
            "options": [
              "0 = No distress",
              "1 = Mild distress", 
              "2 = Moderate distress",
              "3 = Severe distress",
              "4 = Extreme distress"
            ]
          },
          {
            "id": 4,
            "question": "Resistance against obsessions - How much of an effort do you make to resist the obsessive thoughts?",
            "options": [
              "0 = Always resist or symptoms so minimal doesn't need to resist",
              "1 = Try to resist most of the time",
              "2 = Make some effort to resist",
              "3 = Yield to all obsessions without attempting to control them, but do so with some reluctance",
              "4 = Completely yield to all obsessions without attempting to control them"
            ]
          },
          {
            "id": 5,
            "question": "Degree of control over obsessive thoughts - How much control do you have over your obsessive thoughts?",
            "options": [
              "0 = Complete control",
              "1 = Much control, usually able to stop or divert obsessions",
              "2 = Moderate control, sometimes able to stop or divert obsessions",
              "3 = Little control, rarely successful in stopping obsessions",
              "4 = No control, rarely able to even momentarily alter obsessions"
            ]
          }
        ]
      },
      {
        "domain": "Compulsions",
        "items": [
          {
            "id": 6,
            "question": "Time spent performing compulsive behaviors - How much time do you spend performing compulsive behaviors?",
            "options": [
              "0 = None",
              "1 = Less than 1 hr/day or occasional",
              "2 = 1-3 hrs/day or frequent",
              "3 = Greater than 3 and up to 8 hrs/day or very frequent", 
              "4 = Greater than 8 hrs/day or nearly constant"
            ]
          },
          {
            "id": 7,
            "question": "Interference due to compulsive behaviors - How much do your compulsive behaviors interfere with your social or work functioning?",
            "options": [
              "0 = No interference",
              "1 = Slight interference, but overall performance not impaired",
              "2 = Definite interference, but still manageable",
              "3 = Substantial interference",
              "4 = Severe interference"
            ]
          },
          {
            "id": 8,
            "question": "Distress associated with compulsive behavior - How would you feel if prevented from performing your compulsion(s)?",
            "options": [
              "0 = No distress",
              "1 = Slightly anxious",
              "2 = Moderately anxious",
              "3 = Severely anxious",
              "4 = Extremely anxious"
            ]
          },
          {
            "id": 9,
            "question": "Resistance against compulsions - How much of an effort do you make to resist the compulsions?",
            "options": [
              "0 = Always resist or symptoms so minimal doesn't need to resist",
              "1 = Try to resist most of the time",
              "2 = Make some effort to resist", 
              "3 = Yield to almost all compulsions without attempting to control them",
              "4 = Completely yield to all compulsions without attempting to control them"
            ]
          },
          {
            "id": 10,
            "question": "Degree of control over compulsive behavior - How strong is the drive to perform the compulsive behavior?",
            "options": [
              "0 = Complete control",
              "1 = Much control, experiences pressure to perform but usually able to exercise voluntary control",
              "2 = Moderate control, strong pressure to perform, can control with difficulty",
              "3 = Little control, very strong drive to perform, must be carried to completion",
              "4 = No control, drive to perform felt as completely involuntary"
            ]
          }
        ]
      }
    ]
  }
}
```

**Scoring Algorithm:**
```typescript
// In psychTestScoring.ts
export const scoreYBOCS = (severityResponses: number[], obsessionChecklist: string[], compulsionChecklist: string[]): TestResult => {
  if (severityResponses.length !== 10) {
    throw new Error('Y-BOCS severity scale requires exactly 10 responses');
  }
  
  const obsessionScore = severityResponses.slice(0, 5).reduce((sum, score) => sum + score, 0);
  const compulsionScore = severityResponses.slice(5, 10).reduce((sum, score) => sum + score, 0);
  const totalScore = obsessionScore + compulsionScore;
  
  const severity = 
    totalScore <= 7 ? 'Subclinical' :
    totalScore <= 15 ? 'Mild' :
    totalScore <= 23 ? 'Moderate' :
    totalScore <= 31 ? 'Severe' :
    'Extreme';
  
  const subscaleScores = {
    'Obsessions': obsessionScore,
    'Compulsions': compulsionScore
  };
  
  const interpretation = getOCDInterpretation(totalScore, obsessionScore, compulsionScore);
  const riskFlags = generateOCDRiskFlags(totalScore, obsessionChecklist, compulsionChecklist);
  const recommendations = generateOCDRecommendations(totalScore);
  
  return {
    totalScore,
    severity,
    interpretation,
    riskFlags,
    subscaleScores,
    recommendations
  };
};

const getOCDInterpretation = (totalScore: number, obsessionScore: number, compulsionScore: number): string => {
  let interpretation = `Total Score: ${totalScore} (${getSeverity(totalScore)}). Obsessions: ${obsessionScore}, Compulsions: ${compulsionScore}. `;
  if (totalScore <= 7) {
    interpretation += "Subclinical OCD symptoms. Monitor if causing distress.";
  } else if (totalScore <= 15) {
    interpretation += "Mild OCD. Treatment may be beneficial, especially for prominent obsessions or compulsions.";
  } else if (totalScore <= 23) {
    interpretation += "Moderate OCD. Active treatment is indicated.";
  } else if (totalScore <= 31) {
    interpretation += "Severe OCD. Intensive treatment recommended.";
  } else {
    interpretation += "Extreme OCD. Requires intensive treatment, possibly inpatient or residential.";
  }
  return interpretation;
};

const getSeverity = (score: number): string => {
  if (score <= 7) return 'Subclinical';
  if (score <= 15) return 'Mild';
  if (score <= 23) return 'Moderate';
  if (score <= 31) return 'Severe';
  return 'Extreme';
};

const generateOCDRiskFlags = (totalScore: number, obsessions: string[], compulsions: string[]): string[] => {
  const riskFlags = [];
  
  if (totalScore >= 16) {
    riskFlags.push('CLINICALLY_SIGNIFICANT_OCD');
  }
  
  // Check for specific high-risk obsessions
  const aggressiveObsessions = obsessions.filter(obs => 
    obs.toLowerCase().includes('harm') || obs.toLowerCase().includes('violent') || obs.toLowerCase().includes('aggressive')
  );
  if (aggressiveObsessions.length > 0) {
    riskFlags.push('AGGRESSIVE_OBSESSIONS_PRESENT');
  }
  
  // Check for sexual obsessions involving children
  const sexualObsessions = obsessions.filter(obs => 
    obs.toLowerCase().includes('children') || obs.toLowerCase().includes('sexual behavior toward children')
  );
  if (sexualObsessions.length > 0) {
    riskFlags.push('HIGH_RISK_SEXUAL_OBSESSIONS');
  }
  
  return riskFlags;
};

const generateOCDRecommendations = (totalScore: number): string[] => {
  const recommendations = [];
  
  if (totalScore >= 16) {
    recommendations.push('Evidence-based treatment recommended (CBT with ERP or SSRI medication)');
    recommendations.push('Consider referral to OCD specialist');
  }
  
  if (totalScore >= 24) {
    recommendations.push('Intensive treatment recommended (combination therapy)');
    recommendations.push('Consider higher-dose SSRI or clomipramine');
    recommendations.push('Evaluate need for intensive outpatient program');
  }
  
  if (totalScore >= 32) {
    recommendations.push('Severe OCD - consider inpatient or residential treatment');
    recommendations.push('Medication augmentation strategies');
    recommendations.push('Family involvement in treatment planning');
  }
  
  recommendations.push('Monitor treatment response with Y-BOCS at regular intervals');
  
  return recommendations;
};
```

**Clinical Interpretation:**
- **0-7**: Subclinical OCD symptoms
- **8-15**: Mild OCD; consider treatment if causing distress
- **16-23**: Moderate OCD; active treatment indicated
- **24-31**: Severe OCD; intensive treatment required
- **32-40**: Extreme OCD; intensive treatment, possible hospitalization

---

## 🧠 COGNITIVE ASSESSMENTS

### MMSE (Mini-Mental State Examination)

**Database Storage:**
```json
{
  "testName": "MMSE",
  "testCategory": "cognitive",
  "domains": [
    {
      "domain": "Orientation",
      "maxScore": 10,
      "items": [
        {
          "question": "What is the year?",
          "points": 1,
          "type": "orientation_time"
        },
        {
          "question": "What is the season?",
          "points": 1,
          "type": "orientation_time"
        },
        {
          "question": "What is the date?",
          "points": 1,
          "type": "orientation_time"
        },
        {
          "question": "What is the day of the week?",
          "points": 1,
          "type": "orientation_time"
        },
        {
          "question": "What is the month?",
          "points": 1,
          "type": "orientation_time"
        },
        {
          "question": "What country are we in?",
          "points": 1,
          "type": "orientation_place"
        },
        {
          "question": "What state/province are we in?",
          "points": 1,
          "type": "orientation_place"
        },
        {
          "question": "What city/town are we in?",
          "points": 1,
          "type": "orientation_place"
        },
        {
          "question": "What building are we in?",
          "points": 1,
          "type": "orientation_place"
        },
        {
          "question": "What floor are we on?",
          "points": 1,
          "type": "orientation_place"
        }
      ]
    },
    {
      "domain": "Registration",
      "maxScore": 3,
      "items": [
        {
          "question": "Repeat: Apple, Penny, Table",
          "points": 3,
          "type": "immediate_recall",
          "instructions": "Say the three words clearly and ask patient to repeat. Score 1 point for each correct on first trial."
        }
      ]
    },
    {
      "domain": "Attention and Calculation",
      "maxScore": 5,
      "items": [
        {
          "question": "Serial 7s: 100-7=93, 93-7=86, 86-7=79, 79-7=72, 72-7=65",
          "points": 5,
          "type": "calculation",
          "alternative": "Spell WORLD backwards: D-L-R-O-W"
        }
      ]
    },
    {
      "domain": "Recall",
      "maxScore": 3,
      "items": [
        {
          "question": "Recall the three words from registration",
          "points": 3,
          "type": "delayed_recall"
        }
      ]
    },
    {
      "domain": "Language",
      "maxScore": 9,
      "items": [
        {
          "question": "Name: pencil",
          "points": 1,
          "type": "naming"
        },
        {
          "question": "Name: watch",
          "points": 1,
          "type": "naming"
        },
        {
          "question": "Repeat: 'No ifs, ands, or buts'",
          "points": 1,
          "type": "repetition"
        },
        {
          "question": "Follow 3-stage command: 'Take paper in right hand, fold it in half, put it on floor'",
          "points": 3,
          "type": "comprehension"
        },
        {
          "question": "Read and obey: 'Close your eyes'",
          "points": 1,
          "type": "reading"
        },
        {
          "question": "Write a sentence",
          "points": 1,
          "type": "writing"
        },
        {
          "question": "Copy intersecting pentagons",
          "points": 1,
          "type": "construction"
        }
      ]
    }
  ]
}
```

**Scoring Algorithm:**
```typescript
// In psychTestScoring.ts
export const scoreMMSE = (domainScores: Record<string, number>): TestResult => {
  const totalScore = Object.values(domainScores).reduce((sum, score) => sum + score, 0);
  const maxScore = 30;
  
  const severity = 
    totalScore >= 24 ? 'Normal' :
    totalScore >= 18 ? 'Mild impairment' :
    totalScore >= 10 ? 'Moderate impairment' :
    'Severe impairment';
  
  const interpretation = getCognitiveInterpretation(totalScore, domainScores);
  const riskFlags = generateCognitiveRiskFlags(totalScore, domainScores);
  const recommendations = generateCognitiveRecommendations(totalScore, domainScores);
  
  return {
    totalScore,
    severity,
    interpretation,
    riskFlags,
    subscaleScores: domainScores,
    recommendations
  };
};

const getCognitiveInterpretation = (totalScore: number, domainScores: Record<string, number>): string => {
  let interpretation = `Total Score: ${totalScore}/30. Cognitive Status: ${getMMSEStatus(totalScore)}. `;
  if (totalScore >= 24) {
    interpretation += "Normal cognitive function. No cognitive impairment detected.";
  } else if (totalScore >= 18) {
    interpretation += "Mild cognitive impairment. May affect daily functioning.";
  } else if (totalScore >= 10) {
    interpretation += "Moderate cognitive impairment. Significant impact on daily functioning.";
  } else {
    interpretation += "Severe cognitive impairment. Requires substantial support.";
  }
  
  // Add domain-specific notes
  if (domainScores['Orientation'] < 8) interpretation += "\nOrientation deficits noted.";
  if (domainScores['Recall'] < 2) interpretation += "\nMemory deficits noted.";
  if (domainScores['Language'] < 7) interpretation += "\nLanguage difficulties noted.";
  
  return interpretation;
};

const getMMSEStatus = (score: number): string => {
  if (score >= 24) return 'Normal';
  if (score >= 18) return 'Mild Impairment';
  if (score >= 10) return 'Moderate Impairment';
  return 'Severe Impairment';
};

const generateCognitiveRiskFlags = (totalScore: number, domainScores: Record<string, number>): string[] => {
  const riskFlags = [];
  
  if (totalScore < 24) {
    riskFlags.push('COGNITIVE_IMPAIRMENT_DETECTED');
  }
  
  if (domainScores['Orientation'] < 8) {
    riskFlags.push('ORIENTATION_IMPAIRMENT');
  }
  
  if (domainScores['Recall'] < 2) {
    riskFlags.push('MEMORY_IMPAIRMENT');
  }
  
  if (totalScore < 10) {
    riskFlags.push('SEVERE_COGNITIVE_DECLINE');
  }
  
  return riskFlags;
};

const generateCognitiveRecommendations = (totalScore: number, domainScores: Record<string, number>): string[] => {
  const recommendations = [];
  
  if (totalScore < 24) {
    recommendations.push('Further neuropsychological evaluation recommended');
    recommendations.push('Rule out reversible causes of cognitive impairment');
  }
  
  if (totalScore < 18) {
    recommendations.push('Consider neuroimaging studies');
    recommendations.push('Evaluate capacity for independent living');
    recommendations.push('Discuss advance directives and care planning');
  }
  
  // Domain-specific recommendations
  if (domainScores['Orientation'] < 8) {
    recommendations.push('Significant disorientation - assess safety and supervision needs');
  }
  
  if (domainScores['Recall'] < 2) {
    recommendations.push('Severe memory impairment - consider memory aids and strategies');
  }
  
  if (domainScores['Language'] < 7) {
    recommendations.push('Language difficulties noted - consider speech therapy evaluation');
  }
  
  recommendations.push('Repeat cognitive assessment in 6-12 months or with status changes');
  
  return recommendations;
};
```

**Clinical Interpretation:**
- **24-30**: Normal cognitive function
- **18-23**: Mild cognitive impairment
- **10-17**: Moderate cognitive impairment
- **0-9**: Severe cognitive impairment

---

## 🏥 PSYCHOSIS ASSESSMENTS

### PANSS (Positive and Negative Syndrome Scale)

**Database Storage (Selected Key Items):**
```json
{
  "testName": "PANSS",
  "testCategory": "psychosis",
  "subscales": {
    "positive": [
      {
        "id": "P1",
        "item": "Delusions",
        "description": "Beliefs that are unfounded, unrealistic, and idiosyncratic"
      },
      {
        "id": "P2", 
        "item": "Conceptual disorganization",
        "description": "Disorganized process of thinking characterized by disruption of goal-directed sequencing"
      },
      {
        "id": "P3",
        "item": "Hallucinatory behavior", 
        "description": "Verbal report or behavior indicating perceptions which are not generated by external stimuli"
      },
      {
        "id": "P4",
        "item": "Excitement",
        "description": "Hyperactivity as reflected in accelerated motor behavior, heightened responsivity to stimuli"
      },
      {
        "id": "P5",
        "item": "Grandiosity",
        "description": "Inflated self-esteem (self-confidence), or unrealistic sense of superiority"
      },
      {
        "id": "P6",
        "item": "Suspiciousness/persecution",
        "description": "Unrealistic or exaggerated ideas of persecution, as reflected in guardedness, distrustful attitude"
      },
      {
        "id": "P7",
        "item": "Hostility",
        "description": "Verbal and nonverbal expressions of anger and resentment"
      }
    ],
    "negative": [
      {
        "id": "N1",
        "item": "Blunted affect",
        "description": "Diminished emotional responsiveness as characterized by a reduction in facial expression, modulation of feelings, and communicative gestures"
      },
      {
        "id": "N2",
        "item": "Emotional withdrawal", 
        "description": "Lack of interest in, involvement with, and affective commitment to life's events"
      },
      {
        "id": "N3",
        "item": "Poor rapport",
        "description": "Lack of interpersonal empathy, openness in conversation, and sense of closeness, interest, or involvement with the interviewer"
      },
      {
        "id": "N4",
        "item": "Passive/apathetic social withdrawal",
        "description": "Diminished interest and initiative in social interactions due to passivity, apathy, energy, or avolition"
      },
      {
        "id": "N5",
        "item": "Difficulty in abstract thinking",
        "description": "Impairment in the use of abstract-symbolic process"
      },
      {
        "id": "N6",
        "item": "Lack of spontaneity and flow of conversation",
        "description": "Reduction in the normal flow of communication associated with apathy, avolition, defensiveness or cognitive deficit"
      },
      {
        "id": "N7",
        "item": "Stereotyped thinking",
        "description": "Decreased fluidity, spontaneity, and flexibility of thinking"
      }
    ],
    "general": [
      { "id": "G1", "item": "Somatic concern" },
      { "id": "G2", "item": "Anxiety" },
      { "id": "G3", "item": "Guilt feelings" },
      { "id": "G4", "item": "Tension/Worry" },
      { "id": "G5", "item": "Appetite disorders" },
      { "id": "G6", "item": "Depression" },
      { "id": "G7", "item": "Loss of energy" },
      { "id": "G8", "item": "Uncooperativeness" },
      { "id": "G9", "item": "Unusual thought content" },
      { "id": "G10", "item": "Poor impulse control" },
      { "id": "G11", "item": "Preoccupation with bodily functions" },
      { "id": "G12", "item": "Poor judgment and insight" },
      { "id": "G13", "item": "Disturbance of volition" },
      { "id": "G14", "item": "Poor impulse control" },
      { "id": "G15", "item": "Difficulty in attention" }
    ]
  },
  "ratingScale": {
    "1": "Absent - Not present",
    "2": "Minimal - Questionable pathology; may be at the upper extreme of normal limits",
    "3": "Mild - Clearly present but not severe; causes little impairment in functioning",  
    "4": "Moderate - Clearly present and of sufficient severity to cause functioning difficulties",
    "5": "Moderate severe - Present and quite severe; causes substantial impairment in functioning",
    "6": "Severe - Severely impaired functioning in this area",
    "7": "Extreme - Among the most severely ill patients"
  }
}
```

**Scoring Algorithm:**
```typescript
// In psychTestScoring.ts
export const scorePANSS = (positiveScores: number[], negativeScores: number[], generalScores: number[]): TestResult => {
  const positiveScore = positiveScores.reduce((sum, score) => sum + score, 0);
  const negativeScore = negativeScores.reduce((sum, score) => sum + score, 0);
  const generalScore = generalScores.reduce((sum, score) => sum + score, 0);
  const totalScore = positiveScore + negativeScore + generalScore;
  
  // Calculate composite indices
  const cognitiveComposite = calculateCognitiveComposite(positiveScores, negativeScores, generalScores);
  const excitementComposite = calculateExcitementComposite(positiveScores, generalScores);
  const depressionComposite = calculateDepressionComposite(negativeScores, generalScores);
  
  const severity = 
    totalScore <= 58 ? 'Mild' :
    totalScore <= 75 ? 'Moderate' :
    totalScore <= 95 ? 'Marked' :
    totalScore <= 116 ? 'Severe' :
    'Extreme';
  
  const subscaleScores = {
    'Positive Symptoms': positiveScore,
    'Negative Symptoms': negativeScore,
    'General Psychopathology': generalScore,
    'Cognitive Composite': cognitiveComposite,
    'Excitement Composite': excitementComposite,
    'Depression Composite': depressionComposite
  };
  
  const interpretation = getPsychosisInterpretation(totalScore, positiveScore, negativeScore);
  const riskFlags = generatePsychosisRiskFlags(positiveScores, generalScores);
  const recommendations = generatePsychosisRecommendations(totalScore, subscaleScores);
  
  return {
    totalScore,
    severity,
    interpretation,
    riskFlags,
    subscaleScores,
    recommendations
  };
};

const getPsychosisInterpretation = (totalScore: number, positiveScore: number, negativeScore: number): string => {
  let interpretation = `Total Score: ${totalScore} (${getPANSSSeverity(totalScore)}). Positive: ${positiveScore}, Negative: ${negativeScore}. `;
  if (totalScore <= 58) {
    interpretation += "Mild to moderate psychosis. Focus on managing positive symptoms and supporting functioning.";
  } else if (totalScore <= 75) {
    interpretation += "Moderate psychosis. Active treatment of positive and negative symptoms is key.";
  } else if (totalScore <= 95) {
    interpretation += "Marked psychosis. Significant positive and negative symptoms impacting functioning.";
  } else {
    interpretation += "Severe psychosis. Requires intensive intervention, medication management, and support.";
  }
  return interpretation;
};

const getPANSSSeverity = (score: number): string => {
  if (score <= 58) return 'Mild';
  if (score <= 75) return 'Moderate';
  if (score <= 95) return 'Marked';
  if (score <= 116) return 'Severe';
  return 'Extreme';
};

const generatePsychosisRiskFlags = (positiveScores: number[], generalScores: number[]): string[] => {
  const riskFlags = [];
  
  // Check for severe positive symptoms
  if (positiveScores[0] >= 5) { // Delusions
    riskFlags.push('SEVERE_DELUSIONS');
  }
  
  if (positiveScores[2] >= 5) { // Hallucinations
    riskFlags.push('SEVERE_HALLUCINATIONS');
  }
  
  if (positiveScores[6] >= 5) { // Hostility
    riskFlags.push('HIGH_HOSTILITY_RISK');
  }
  
  // Check for poor impulse control (from general psychopathology - G10, G14)
  if (generalScores && (generalScores[9] >= 5 || generalScores[13] >= 5)) { 
    riskFlags.push('POOR_IMPULSE_CONTROL');
  }
  
  return riskFlags;
};

const generatePsychosisRecommendations = (totalScore: number, subscaleScores: Record<string, number>): string[] => {
  const recommendations = [];
  
  if (totalScore >= 75) {
    recommendations.push('Antipsychotic medication indicated');
    recommendations.push('Consider hospitalization for severe symptoms');
  }
  
  if (subscaleScores['Positive Symptoms'] >= 20) {
    recommendations.push('Focus on positive symptom reduction with antipsychotics');
  }
  
  if (subscaleScores['Negative Symptoms'] >= 20) {
    recommendations.push('Address negative symptoms - consider atypical antipsychotics');
    recommendations.push('Psychosocial rehabilitation interventions');
  }
  
  if (subscaleScores['Cognitive Composite'] >= 15) {
    recommendations.push('Cognitive remediation therapy may be beneficial');
  }
  
  recommendations.push('Monitor treatment response with PANSS at regular intervals');
  recommendations.push('Assess medication adherence and side effects');
  
  return recommendations;
};

// Helper functions for PANSS composite scores (ensure these are defined if used elsewhere)
const calculateCognitiveComposite = (positive: number[], negative: number[], general: number[]): number => {
  // Based on published PANSS factor structure
  // Items: P2 (conceptual disorganization), N5 (abstract thinking), G11 (difficulty in attention), G12 (poor judgment and insight), G13 (disturbance of volition)
  return (positive[1] || 0) + (negative[4] || 0) + (general[14] || 0) + (general[11] || 0) + (general[12] || 0);
};

const calculateExcitementComposite = (positive: number[], general: number[]): number => {
  // Items: P4 (excitement), P7 (hostility), G8 (uncooperativeness), G10 (poor impulse control)
  return (positive[3] || 0) + (positive[6] || 0) + (general[7] || 0) + (general[9] || 0);
};

const calculateDepressionComposite = (negative: number[], general: number[]): number => {
  // Items: N2 (emotional withdrawal), N4 (social withdrawal), G1 (somatic concern), G2 (anxiety), G3 (guilt), G6 (depression)
  return (negative[1] || 0) + (negative[3] || 0) + (general[0] || 0) + (general[1] || 0) + (general[2] || 0) + (general[5] || 0);
};
```

---

## 💭 PTSD ASSESSMENT

### PCL-5 (PTSD Checklist for DSM-5)

**Database Storage:**
```json
{
  "testName": "PCL-5",
  "testCategory": "ptsd",
  "instructions": "Below is a list of problems and complaints that people sometimes have in response to stressful life experiences. Please read each problem carefully and then circle one of the numbers to indicate how much you have been bothered by that problem in the past month.",
  "items": [
    {
      "id": 1,
      "question": "Repeated, disturbing, and unwanted memories of the stressful experience?",
      "cluster": "B - Intrusion"
    },
    {
      "id": 2,
      "question": "Repeated, disturbing dreams of the stressful experience?", 
      "cluster": "B - Intrusion"
    },
    {
      "id": 3,
      "question": "Suddenly feeling or acting as if the stressful experience were actually happening again (as if you were actually back there reliving it)?",
      "cluster": "B - Intrusion"
    },
    {
      "id": 4,
      "question": "Feeling very upset when something reminded you of the stressful experience?",
      "cluster": "B - Intrusion"
    },
    {
      "id": 5,
      "question": "Having strong physical reactions when something reminded you of the stressful experience (for example, heart pounding, trouble breathing, sweating)?",
      "cluster": "B - Intrusion"
    },
    {
      "id": 6,
      "question": "Avoiding memories, thoughts, or feelings related to the stressful experience?",
      "cluster": "C - Avoidance"
    },
    {
      "id": 7,
      "question": "Avoiding external reminders of the stressful experience (for example, people, places, conversations, activities, objects, or situations)?",
      "cluster": "C - Avoidance"
    },
    {
      "id": 8,
      "question": "Trouble remembering important parts of the stressful experience?",
      "cluster": "D - Negative Cognitions"
    },
    {
      "id": 9,
      "question": "Having strong negative beliefs about yourself, other people, or the world (for example, having thoughts such as: I am bad, there is something seriously wrong with me, no one can be trusted, the world is completely dangerous)?",
      "cluster": "D - Negative Cognitions"
    },
    {
      "id": 10,
      "question": "Blaming yourself or someone else for the stressful experience or what happened after it?",
      "cluster": "D - Negative Cognitions"
    },
    {
      "id": 11,
      "question": "Having strong negative feelings such as fear, horror, anger, guilt, or shame?",
      "cluster": "D - Negative Cognitions"
    },
    {
      "id": 12,
      "question": "Loss of interest in activities that you used to enjoy?",
      "cluster": "D - Negative Cognitions"
    },
    {
      "id": 13,
      "question": "Feeling distant or cut off from other people?",
      "cluster": "D - Negative Cognitions"
    },
    {
      "id": 14,
      "question": "Trouble experiencing positive feelings (for example, being unable to feel happiness, satisfaction, love, joy, or hope)?",
      "cluster": "D - Negative Cognitions"
    },
    {
      "id": 15,
      "question": "Irritable behavior, angry outbursts, or acting aggressively?",
      "cluster": "E - Arousal and Reactivity"
    },
    {
      "id": 16,
      "question": "Taking too many risks or doing things that could cause you harm?",
      "cluster": "E - Arousal and Reactivity"
    },
    {
      "id": 17,
      "question": "Being 'superalert' or watchful or on guard?",
      "cluster": "E - Arousal and Reactivity"
    },
    {
      "id": 18,
      "question": "Feeling jumpy or easily startled?",
      "cluster": "E - Arousal and Reactivity"
    },
    {
      "id": 19,
      "question": "Having difficulty concentrating?",
      "cluster": "E - Arousal and Reactivity"
    },
    {
      "id": 20,
      "question": "Trouble falling or staying asleep?",
      "cluster": "E - Arousal and Reactivity"
    }
  ],
  "responseScale": {
    "0": "Not at all",
    "1": "A little bit", 
    "2": "Moderately",
    "3": "Quite a bit",
    "4": "Extremely"
  }
}
```

**Scoring Algorithm:**
```typescript
// In psychTestScoring.ts
export const scorePCL5 = (responses: number[]): TestResult => {
  if (responses.length !== 20) {
    throw new Error('PCL-5 requires exactly 20 responses');
  }
  
  const totalScore = responses.reduce((sum, score) => sum + score, 0);
  
  const clusterScores = {
    'Cluster B (Intrusion)': responses.slice(0, 5).reduce((sum, score) => sum + score, 0),
    'Cluster C (Avoidance)': responses.slice(5, 7).reduce((sum, score) => sum + score, 0),
    'Cluster D (Negative Cognitions)': responses.slice(7, 14).reduce((sum, score) => sum + score, 0),
    'Cluster E (Arousal/Reactivity)': responses.slice(14, 20).reduce((sum, score) => sum + score, 0)
  };
  
  const severity = 
    totalScore < 31 ? 'Below PTSD threshold' :
    totalScore < 45 ? 'Mild PTSD symptoms' :
    totalScore < 60 ? 'Moderate PTSD symptoms' :
    'Severe PTSD symptoms';
  
  // DSM-5 diagnostic criteria evaluation
  // Criterion B: 1+ symptom
  const clusterBMet = responses.slice(0, 5).filter(score => score >= 2).length >= 1;
  // Criterion C: 1+ symptom
  const clusterCMet = responses.slice(5, 7).filter(score => score >= 2).length >= 1; 
  // Criterion D: 2+ symptoms
  const clusterDMet = responses.slice(7, 14).filter(score => score >= 2).length >= 2;
  // Criterion E: 2+ symptoms
  const clusterEMet = responses.slice(14, 20).filter(score => score >= 2).length >= 2;
  
  const meetsDSMCriteria = clusterBMet && clusterCMet && clusterDMet && clusterEMet;
  
  const interpretation = getPTSDInterpretation(totalScore, meetsDSMCriteria, clusterScores);
  const riskFlags = generatePTSDRiskFlags(totalScore, responses, meetsDSMCriteria);
  const recommendations = generatePTSDRecommendations(totalScore, meetsDSMCriteria);
  
  return {
    totalScore,
    severity,
    interpretation,
    riskFlags,
    subscaleScores: clusterScores,
    recommendations
  };
};

const getPTSDInterpretation = (totalScore: number, meetsDSMCriteria: boolean, clusterScores: Record<string, number>): string => {
  let interpretation = `Total Score: ${totalScore}. `;
  if (meetsDSMCriteria) {
    interpretation += "Meets DSM-5 criteria for PTSD. ";
  } else {
    interpretation += "Does not meet full PTSD criteria based on symptom counts. ";
  }
  
  interpretation += `Severity: ${getPSDTScoreSeverity(totalScore)}. `;
  
  if (clusterScores['Cluster B (Intrusion)'] >= 10) interpretation += "High intrusion symptoms. ";
  if (clusterScores['Cluster C (Avoidance)'] >= 4) interpretation += "High avoidance symptoms. ";
  if (clusterScores['Cluster D (Negative Cognitions)'] >= 14) interpretation += "High negative cognitions/mood symptoms. ";
  if (clusterScores['Cluster E (Arousal/Reactivity)'] >= 14) interpretation += "High arousal/reactivity symptoms. ";
  
  return interpretation;
};

const getPSDTScoreSeverity = (score: number): string => {
  if (score < 31) return 'Below threshold';
  if (score < 45) return 'Mild symptoms';
  if (score < 60) return 'Moderate symptoms';
  return 'Severe symptoms';
};

const generatePTSDRiskFlags = (totalScore: number, responses: number[], meetsDSMCriteria: boolean): string[] => {
  const riskFlags = [];
  
  if (meetsDSMCriteria) {
    riskFlags.push('MEETS_PTSD_CRITERIA');
  }
  
  if (totalScore >= 60) {
    riskFlags.push('SEVERE_PTSD_SYMPTOMS');
  }
  
  // Check for high arousal/reactivity symptoms
  const arousalScore = responses.slice(14, 20).reduce((sum, score) => sum + score, 0);
  if (arousalScore >= 18) {
    riskFlags.push('SEVERE_HYPERAROUSAL');
  }
  
  // Check for risky behavior (item 16)
  if (responses[15] >= 3) {
    riskFlags.push('HIGH_RISK_BEHAVIOR');
  }
  
  // Check for suicidal ideation (indirectly via negative cognitions, e.g., guilt, loss of interest)
  // More direct assessment needed if available, but can infer risk.
  const negativeCognitionScores = responses.slice(7, 14);
  const guiltScore = negativeCognitionScores[2]; // Item 10 (Blaming yourself)
  const lossOfInterest = negativeCognitionScores[4]; // Item 12 (Loss of interest)
  const lackOfPositiveFeeling = negativeCognitionScores[5]; // Item 14 (Trouble experiencing positive feelings)
  
  if (guiltScore >= 3 || lossOfInterest >= 3 || lackOfPositiveFeeling >= 3) {
    riskFlags.push('INCREASED_SUICIDE_RISK_INDICATORS');
  }
  
  return riskFlags;
};

const generatePTSDRecommendations = (totalScore: number, meetsDSMCriteria: boolean): string[] => {
  const recommendations = [];
  
  if (meetsDSMCriteria) {
    recommendations.push('PTSD diagnosis likely - trauma-focused therapy recommended');
    recommendations.push('Consider EMDR, CPT, or PE therapy');
  }
  
  if (totalScore >= 45) {
    recommendations.push('Consider SSRI or SNRI medication');
    recommendations.push('Evaluate need for combination therapy');
  }
  
  if (totalScore >= 60) {
    recommendations.push('Severe PTSD - intensive treatment indicated');
    recommendations.push('Consider partial hospitalization or residential treatment');
  }
  
  recommendations.push('Assess for comorbid conditions (depression, anxiety, substance use)');
  recommendations.push('Monitor treatment response with PCL-5');
  recommendations.push('Evaluate safety and suicide risk regularly');
  
  return recommendations;
};
```

---

## 🔧 IMPLEMENTATION INSTRUCTIONS

### 1. Database Setup
Ensure your `PsychTestTemplate` table (or equivalent) can store the JSON for `items`, `options`, `scores`, `checklist` data, and `scoring` logic (though logic will be in code).

```sql
-- Example: If using PostgreSQL with JSONB

-- Create or Alter PsychTestTemplate table
-- CREATE TABLE IF NOT EXISTS PsychTestTemplate (
--     id SERIAL PRIMARY KEY,
--     testName VARCHAR(255) UNIQUE NOT NULL,
--     category VARCHAR(255),
--     description TEXT,
--     items JSONB,           -- Store questions, options, clusters, etc.
--     scoringAlgorithm JSONB, -- Store scoring logic parameters or references (though main logic is in code)
--     createdAt TIMESTAMP DEFAULT NOW(),
--     updatedAt TIMESTAMP DEFAULT NOW()
-- );

-- Sample INSERT statements (using placeholder JSON data for brevity)
INSERT INTO PsychTestTemplate (testName, category, description, items) VALUES 
('PHQ-9', 'depression', 'Patient Health Questionnaire - Depression', '[PHQ-9 JSON DATA HERE]'),
('GAD-7', 'anxiety', 'Generalized Anxiety Disorder Scale', '[GAD-7 JSON DATA HERE]'),
('Y-BOCS', 'ocd', 'Yale-Brown Obsessive Compulsive Scale', '[Y-BOCS JSON DATA HERE]'),
('MMSE', 'cognitive', 'Mini-Mental State Examination', '[MMSE JSON DATA HERE]'),
('PANSS', 'psychosis', 'Positive and Negative Syndrome Scale', '[PANSS JSON DATA HERE]'),
('PCL-5', 'ptsd', 'PTSD Checklist for DSM-5', '[PCL-5 JSON DATA HERE]');
```

### 2. Frontend Component Structure

Create a dedicated directory for each test within `frontend/src/components/PsychTesting/tests/`.

```
frontend/src/components/PsychTesting/
├── PsychTestingDashboard.tsx     # Main dashboard component
├── TestBatteryManager.tsx        # Component for managing comprehensive test batteries
├── QuickScreeners.tsx            # Component for quick access to PHQ-9, GAD-7 etc.
├── TestHistory.tsx               # Component to display patient's past test results
├── MentalStatusExam.tsx          # Component for MSE interface
├── tests/
│   ├── PHQ9.tsx                  # PHQ-9 questionnaire interface
│   ├── GAD7.tsx                  # GAD-7 questionnaire interface
│   ├── YBOCS.tsx                 # Y-BOCS questionnaire interface
│   ├── MMSE.tsx                  # MMSE questionnaire interface
│   ├── PANSS.tsx                 # PANSS questionnaire interface
│   ├── PCL5.tsx                  # PCL-5 questionnaire interface
│   └── ...other_tests.tsx
└── components/                   # Reusable UI elements for tests
    ├── TestCard.tsx              # Card for quick screener links
    ├── QuestionItem.tsx          # Renders a single question with response options
    ├── ScoreDisplay.tsx          # Displays calculated scores, severity, and interpretation
    ├── RiskAlerts.tsx            # Displays risk flags
    ├── ProgressTracker.tsx       # For multi-step tests like Y-BOCS or PANSS
    └── ValidityWarnings.tsx      # Displays results from response validation
```

**Key Features for Individual Test Components (e.g., `PHQ9.tsx`):**
-   **State Management:** Manage current question index, user responses, start/end times.
-   **Dynamic Rendering:** Use data from `PsychTestTemplate` to render questions and options.
-   **Real-time Scoring:** Integrate `psychTestScoring.ts` utility functions to calculate scores as responses are entered or upon completion.
-   **Clinical Interpretation & Risk Flags:** Display the output from scoring functions, highlighting any critical risks.
-   **Response Validation:** Implement checks for response patterns, speed (if applicable), and completeness.
-   **Save Functionality:** Call backend API to save test results (including raw responses, scores, interpretation, completion time, validation results) to the `PsychTest` or `LabResult` table.
-   **Navigation:** Include "Previous" and "Next" buttons for multi-question tests, with a "Submit" or "Complete" button on the last question.

### 3. Scoring Implementation (Code Details)

```typescript
// frontend/src/utils/psychTestScoring.ts

import { Patient } from '@/types'; // Assuming Patient type is defined here

// --- Main Result Interface ---
export interface TestResult {
  totalScore: number;
  severity: string; // e.g., 'Minimal depression', 'Mild anxiety'
  interpretation: string; // Textual interpretation of the score
  riskFlags: string[]; // e.g., ['SUICIDAL_IDEATION_REPORTED', 'CLINICALLY_SIGNIFICANT_ANXIETY']
  subscaleScores?: Record<string, number>; // For tests with multiple scales (Y-BOCS, PANSS, PCL-5)
  completionTime?: number; // In minutes or seconds
  validityIndices?: Record<string, number>; // e.g., responseVariance, acquiescenceBias
  recommendations: string[]; // Clinician recommendations based on score
}

// --- Response Validation Interface ---
export interface ValidationResult {
  isValid: boolean; // Indicates if the data meets basic validation criteria
  validityIndices: Record<string, number>; // Statistical measures of response patterns
  warnings: string[]; // Explanations for potential response validity issues
}

// --- Individual Test Scoring Functions ---

// PHQ-9 Scoring Utility
export const scorePHQ9 = (responses: number[]): TestResult => {
  // ... (implementation as provided above) ...
  // Ensure error handling for incorrect number of responses
  if (responses.length !== 9) throw new Error('PHQ-9 requires exactly 9 responses');
  // ... rest of the logic ...
  return { totalScore: 0, severity: '', interpretation: '', riskFlags: [], recommendations: [] }; // Placeholder
};

// GAD-7 Scoring Utility
export const scoreGAD7 = (responses: number[]): TestResult => {
  // ... (implementation as provided above) ...
  if (responses.length !== 7) throw new Error('GAD-7 requires exactly 7 responses');
  // ... rest of the logic ...
  return { totalScore: 0, severity: '', interpretation: '', riskFlags: [], recommendations: [] }; // Placeholder
};

// Y-BOCS Scoring Utility with Checklist Analysis
export const scoreYBOCS = (severityResponses: number[], obsessionChecklist: string[], compulsionChecklist: string[]): TestResult => {
  // ... (implementation as provided above) ...
  if (severityResponses.length !== 10) throw new Error('Y-BOCS severity scale requires exactly 10 responses');
  // ... rest of the logic ...
  return { totalScore: 0, severity: '', interpretation: '', riskFlags: [], subscaleScores: {}, recommendations: [] }; // Placeholder
};

// MMSE Scoring with Domain Analysis
export const scoreMMSE = (domainScores: Record<string, number>): TestResult => {
  // ... (implementation as provided above) ...
  // Ensure all required domain scores are present
  // ... rest of the logic ...
  return { totalScore: 0, severity: '', interpretation: '', riskFlags: [], subscaleScores: {}, recommendations: [] }; // Placeholder
};

// PANSS Scoring with Composite Scores
export const scorePANSS = (positiveScores: number[], negativeScores: number[], generalScores: number[]): TestResult => {
  // ... (implementation as provided above) ...
  // Ensure correct array lengths and types
  // ... rest of the logic ...
  return { totalScore: 0, severity: '', interpretation: '', riskFlags: [], subscaleScores: {}, recommendations: [] }; // Placeholder
};

// PCL-5 Scoring with DSM-5 Criteria
export const scorePCL5 = (responses: number[]): TestResult => {
  // ... (implementation as provided above) ...
  if (responses.length !== 20) throw new Error('PCL-5 requires exactly 20 responses');
  // ... rest of the logic ...
  return { totalScore: 0, severity: '', interpretation: '', riskFlags: [], subscaleScores: {}, recommendations: [] }; // Placeholder
};


// --- Response Validation Utilities ---
export const validateResponses = (testName: string, responses: number[], items: any[]): ValidationResult => {
  const warnings: string[] = [];
  let isValid = true;
  const validityIndices: Record<string, number> = {};
  
  // 1. Check for missing responses
  if (responses.length !== items.length) {
    isValid = false;
    warnings.push(`Expected ${items.length} responses, but received ${responses.length}.`);
  }
  
  // 2. Check response patterns for unusual distributions (e.g., too flat, too peaked)
  const responseVariance = calculateVariance(responses);
  validityIndices.responseVariance = responseVariance;
  
  // Thresholds are empirical and might need adjustment
  if (responses.length > 5 && responseVariance < 0.5) { 
    warnings.push('Very low response variance detected. Consider if the patient answered inconsistently or randomly.');
  }
  
  // 3. Check for acquiescence bias (tendency to agree/select higher scores)
  const highResponsesCount = responses.filter(r => r >= 3).length;
  const acquiescenceRatio = responses.length > 0 ? highResponsesCount / responses.length : 0;
  validityIndices.acquiescenceRatio = acquiescenceRatio;
  
  if (acquiescenceRatio > 0.8) { // If >80% of responses are 'More than half the days' or 'Nearly every day'
    warnings.push('High agreement pattern detected. Patient may be overly agreeable or endorsing all symptoms.');
  }
  
  // 4. Check for random responding (e.g., alternating answers, if pattern is obvious)
  // This is more complex and might involve checking specific patterns. For now, we'll rely on variance.

  // 5. Completion Time (This would typically be passed from the component)
  // if (completionTime && testName === 'PHQ-9') { // Example for PHQ-9
  //   if (completionTime < 1.5) { // e.g., < 1.5 minutes for PHQ-9
  //     warnings.push('Very rapid completion time. May indicate hasty responding.');
  //   }
  // }
  
  // If any warnings are critical, set isValid to false. For now, we'll just report warnings.
  if (warnings.length > 0) {
    // Decide if warnings automatically invalidate the score. Often, they just flag for clinician review.
    // isValid = false; // Uncomment if warnings should automatically invalidate
  }

  return {
    isValid,
    validityIndices,
    warnings
  };
};

// Helper to calculate variance
const calculateVariance = (values: number[]): number => {
  if (values.length === 0) return 0;
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
  const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
  return squaredDiffs.reduce((sum, val) => sum + val, 0) / values.length;
};

// --- Normative Data and Percentile Calculations ---
export const calculatePercentile = (testName: string, score: number, demographicInfo?: {
  age?: number;
  gender?: string;
  education?: string;
}): number | null => {
  // This section requires actual normative data tables.
  // For now, placeholders or simple approximations can be used.
  // The implementation would involve looking up percentiles based on score and potentially demographics.
  
  switch (testName) {
    case 'PHQ-9':
      return getPHQ9Percentile(score);
    case 'GAD-7':
      return getGAD7Percentile(score);
    case 'MMSE':
      return getMMSEPercentile(score, demographicInfo?.age);
    // Add other tests that have established percentiles
    default:
      return null; // No percentile available or not implemented
  }
};

// Example: PHQ-9 Percentile Calculation (using rough approximations)
const getPHQ9Percentile = (score: number): number => {
  // This is a simplified example. Real implementation needs proper lookup tables.
  const percentileTable = [
    { score: 0, percentile: 25 },
    { score: 2, percentile: 40 },
    { score: 4, percentile: 55 },
    { score: 7, percentile: 70 },
    { score: 10, percentile: 80 },
    { score: 15, percentile: 90 },
    { score: 20, percentile: 95 },
    { score: 27, percentile: 99 }
  ];
  
  for (let i = 0; i < percentileTable.length - 1; i++) {
    if (score <= percentileTable[i + 1].score) {
      // Linear interpolation for scores between table points
      const lower = percentileTable[i];
      const upper = percentileTable[i + 1];
      const range = upper.score - lower.score;
      if (range === 0) return lower.percentile;
      const portion = (score - lower.score) / range;
      return lower.percentile + portion * (upper.percentile - lower.percentile);
    }
  }
  return 99; // For scores above the max in the table
};

// Placeholder for GAD-7 percentiles
const getGAD7Percentile = (score: number): number | null => {
  // Normative data varies by population. This is a placeholder.
  // Refer to specific studies for accurate GAD-7 norms.
  return null; 
};

// Placeholder for MMSE percentiles with age adjustment
const getMMSEPercentile = (score: number, age?: number): number | null => {
  // MMSE norms are heavily influenced by age and education.
  // This would require complex lookup tables based on these factors.
  // For example, a score of 25 might be normal for a 50-year-old but indicate impairment for an 80-year-old.
  return null; 
};


// --- Risk Assessment Utilities ---
// (These functions are called within the specific score functions)

// PCL-5 Risk Flags Example:
const generatePTSDRiskFlags = (totalScore: number, responses: number[], meetsDSMCriteria: boolean): string[] => {
  const riskFlags: string[] = [];
  
  if (meetsDSMCriteria) {
    riskFlags.push('MEETS_PTSD_CRITERIA');
  }
  
  if (totalScore >= 60) {
    riskFlags.push('SEVERE_PTSD_SYMPTOMS');
  }
  
  // Check for high arousal/reactivity symptoms (Cluster E)
  const arousalScore = responses.slice(14, 20).reduce((sum, score) => sum + score, 0);
  if (arousalScore >= 18) {
    riskFlags.push('SEVERE_HYPERAROUSAL');
  }
  
  // Check for risky behavior (item 16)
  if (responses[15] >= 3) {
    riskFlags.push('HIGH_RISK_BEHAVIOR');
  }
  
  // Check for suicidal ideation indicators (e.g., Guilt, Loss of Interest, Lack of Positive Feelings)
  const negativeCognitionScores = responses.slice(7, 14);
  const guiltScore = negativeCognitionScores[2]; // Item 10 (Blaming yourself)
  const lossOfInterest = negativeCognitionScores[4]; // Item 12 (Loss of interest)
  const lackOfPositiveFeeling = negativeCognitionScores[5]; // Item 14 (Trouble experiencing positive feelings)
  
  if (guiltScore >= 3 || lossOfInterest >= 3 || lackOfPositiveFeeling >= 3) {
    riskFlags.push('INCREASED_SUICIDE_RISK_INDICATORS');
  }
  
  return riskFlags;
};

// ... other risk flag generation functions (e.g., generatePsychosisRiskFlags) ...


// --- Treatment Recommendation Generators ---
// (These functions are called within the specific score functions)

// Example: PTSD Recommendations
const generatePTSDRecommendations = (totalScore: number, meetsDSMCriteria: boolean): string[] => {
  const recommendations: string[] = [];
  
  if (meetsDSMCriteria) {
    recommendations.push('PTSD diagnosis likely - trauma-focused therapy recommended');
    recommendations.push('Consider EMDR, Cognitive Processing Therapy (CPT), or Prolonged Exposure (PE) therapy');
  }
  
  if (totalScore >= 45) {
    recommendations.push('Consider SSRI or SNRI medication for symptom management');
    recommendations.push('Evaluate need for combination therapy (therapy + medication)');
  }
  
  if (totalScore >= 60) {
    recommendations.push('Severe PTSD - intensive treatment may be indicated');
    recommendations.push('Consider partial hospitalization or residential treatment programs');
  }
  
  recommendations.push('Assess for and treat comorbid conditions (e.g., depression, anxiety, substance use disorders)');
  recommendations.push('Monitor treatment response regularly using PCL-5');
  recommendations.push('Regularly evaluate safety and suicide risk');
  
  return recommendations;
};

// ... other recommendation generation functions ...

```

### 4. React Component Structure Example

#### `frontend/src/components/PsychTesting/PsychTestingDashboard.tsx`
```typescript
import React, { useState, useEffect } from 'react';
import { Patient } from '@/types'; // Assuming Patient type is defined
import { fetchPatients } from '@/api/patients'; // Mock API call
import { fetchPsychTestHistory } from '@/api/psychTests'; // Mock API call

import { TestBatteryManager } from './TestBatteryManager';
import { QuickScreeners } from './QuickScreeners';
import { TestHistory } from './TestHistory';
import { MentalStatusExam } from './MentalStatusExam';
import { PsychTestResult } from './types'; // Define this type based on TestResult interface

// Mock TabButton and PatientSelector components
const TabButton: React.FC<{ active: boolean; onClick: () => void; icon: string; label: string }> = ({ active, onClick, icon, label }) => (
  <button
    onClick={onClick}
    className={`py-2 px-4 rounded-md font-medium transition-colors ${
      active ? 'bg-blue-500 text-white' : 'text-gray-600 hover:bg-gray-100'
    }`}
  >
    {icon} {label}
  </button>
);

const PatientSelector: React.FC<{
  selectedPatient: Patient | null;
  onPatientSelect: (patient: Patient | null) => void;
}> = ({ selectedPatient, onPatientSelect }) => {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setLoading(true);
    fetchPatients()
      .then(data => {
        // Filter out dummy/test patients if necessary
        setPatients(data.filter(p => !p.firstName.toLowerCase().includes('test'))); 
      })
      .catch(error => console.error("Error fetching patients:", error))
      .finally(() => setLoading(false));
  }, []);

  return (
    <div className="relative">
      <label htmlFor="patient-select" className="block text-sm font-medium text-gray-700">
        Select Patient:
      </label>
      <select
        id="patient-select"
        className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
        value={selectedPatient?.id || ''}
        onChange={(e) => {
          const patientId = e.target.value;
          if (!patientId) {
            onPatientSelect(null);
            return;
          }
          const selected = patients.find(p => p.id === patientId);
          onPatientSelect(selected || null);
        }}
      >
        <option value="">-- Please select a patient --</option>
        {loading ? <option disabled>Loading patients...</option> : (
          patients.map(patient => (
            <option key={patient.id} value={patient.id}>
              {patient.firstName} {patient.lastName} ({patient.dob})
            </option>
          ))
        )}
      </select>
    </div>
  );
};

export const PsychTestingDashboard: React.FC = () => {
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [activeTab, setActiveTab] = useState<'screeners' | 'comprehensive' | 'mse' | 'history'>('screeners');
  const [testHistory, setTestHistory] = useState<PsychTestResult[]>([]);

  // Fetch test history when a patient is selected
  useEffect(() => {
    if (selectedPatient) {
      fetchPsychTestHistory(selectedPatient.id)
        .then(data => setTestHistory(data))
        .catch(error => console.error("Error fetching test history:", error));
    } else {
      setTestHistory([]);
    }
  }, [selectedPatient]);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow">
        <div className="px-6 py-4 container mx-auto">
          <PatientSelector
            selectedPatient={selectedPatient}
            onPatientSelect={setSelectedPatient}
          />
        </div>
      </div>

      {selectedPatient && (
        <div className="container mx-auto px-6 py-6">
          <div className="mb-6 overflow-x-auto">
            <nav className="flex space-x-4 md:space-x-8 whitespace-nowrap">
              <TabButton
                active={activeTab === 'screeners'}
                onClick={() => setActiveTab('screeners')}
                icon="🔍"
                label="Quick Screeners"
              />
              <TabButton
                active={activeTab === 'comprehensive'}
                onClick={() => setActiveTab('comprehensive')}
                icon="📋"
                label="Comprehensive Tests"
              />
              <TabButton
                active={activeTab === 'mse'}
                onClick={() => setActiveTab('mse')}
                icon="🧠"
                label="Mental Status Exam"
              />
              <TabButton
                active={activeTab === 'history'}
                onClick={() => setActiveTab('history')}
                icon="📊"
                label="Test History"
              />
            </nav>
          </div>

          <div className="animate-fade-in"> {/* Add animation */}
            {activeTab === 'screeners' && (
              <QuickScreeners patient={selectedPatient} />
            )}
            {activeTab === 'comprehensive' && (
              <TestBatteryManager patient={selectedPatient} />
            )}
            {activeTab === 'mse' && (
              <MentalStatusExam patient={selectedPatient} />
            )}
            {activeTab === 'history' && (
              <TestHistory patient={selectedPatient} history={testHistory} />
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// Mock helper functions for context
const getLastTestDate = (patientId: string, testName: string): string | null => null; // Implement actual fetching
const getLastRiskLevel = (patientId: string, testName: string): string | null => null; // Implement actual fetching
const startQuickTest = (testName: string, patient: Patient) => console.log(`Starting ${testName} for ${patient.firstName}`); // Implement navigation
```

#### `frontend/src/components/PsychTesting/QuickScreeners.tsx`
```typescript
import React from 'react';
import { Patient } from '@/types';
import { TestResult } from './types'; // Define TestResult type

// Mock components
const TestCard: React.FC<{ title: string; description: string; lastCompleted: string | null; riskLevel: string | null; onStartTest: () => void }> = 
  ({ title, description, lastCompleted, riskLevel, onStartTest }) => (
  <div className="bg-white p-6 rounded-lg shadow-lg flex flex-col justify-between">
    <div>
      <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
      <p className="mt-1 text-sm text-gray-600">{description}</p>
      {lastCompleted && <p className="mt-2 text-xs text-gray-500">Last completed: {lastCompleted}</p>}
      {riskLevel && <p className={`mt-1 text-xs font-semibold ${riskLevel.includes('HIGH') || riskLevel.includes('SEVERE') ? 'text-red-600' : 'text-yellow-600'}`}>{riskLevel}</p>}
    </div>
    <button
      onClick={onStartTest}
      className="mt-4 w-full py-2 px-4 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
    >
      Start Test
    </button>
  </div>
);

const RecentScoresChart: React.FC<{ patient: Patient }> = ({ patient }) => (
  <div className="bg-white p-6 rounded-lg shadow-lg">
    <h3 className="text-lg font-semibold text-gray-800 mb-4">Recent Scores</h3>
    {/* Placeholder for a chart component */}
    <div className="h-40 bg-gray-200 rounded flex items-center justify-center text-gray-500">
      Chart Placeholder (e.g., PHQ-9 trend)
    </div>
  </div>
);


export const QuickScreeners: React.FC<{ patient: Patient }> = ({ patient }) => {
  const handleStartPHQ9 = () => {
    // Logic to navigate to PHQ9 component or open modal
    console.log(`Starting PHQ-9 for ${patient.firstName}`);
  };

  const handleStartGAD7 = () => {
    // Logic to navigate to GAD7 component or open modal
    console.log(`Starting GAD-7 for ${patient.firstName}`);
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <TestCard
        title="PHQ-9 Depression Screen"
        description="9-item patient health questionnaire for depression symptoms."
        lastCompleted={getLastTestDate(patient.id, 'PHQ-9')}
        riskLevel={getLastRiskLevel(patient.id, 'PHQ-9')}
        onStartTest={handleStartPHQ9}
      />
      
      <TestCard
        title="GAD-7 Anxiety Screen"
        description="7-item scale for screening and monitoring generalized anxiety disorder."
        lastCompleted={getLastTestDate(patient.id, 'GAD-7')}
        riskLevel={getLastRiskLevel(patient.id, 'GAD-7')}
        onStartTest={handleStartGAD7}
      />
      
      {/* Add cards for other quick screeners (e.g., PCL-5 snippet, AUDIT) */}
      
      <div className="lg:col-span-2">
        <RecentScoresChart patient={patient} />
      </div>
    </div>
  );
};
```

#### `frontend/src/components/PsychTesting/tests/PHQ9.tsx` (Example Implementation)
```typescript
import React, { useState } from 'react';
import { Patient } from '@/types';
import { scorePHQ9, validateResponses, TestResult, ValidationResult } from '@/utils/psychTestScoring';
import { savePsychTestResult } from '@/api/psychTests'; // Mock API call
import { ChevronLeft, ChevronRight, CheckCircle } from 'lucide-react'; // Assuming Lucide icons

// Mock QuestionItem and ScoreDisplay components
const QuestionItem: React.FC<{
  question: string;
  options: { value: number; label: string }[];
  currentValue: number;
  onValueChange: (value: number) => void;
}> = ({ question, options, currentValue, onValueChange }) => (
  <div className="mb-6 p-4 border border-gray-200 rounded-md bg-white">
    <p className="mb-4 font-medium text-gray-700">{question}</p>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
      {options.map(option => (
        <button
          key={option.value}
          onClick={() => onValueChange(option.value)}
          className={`w-full text-left py-3 px-4 rounded-md border transition-colors
            ${currentValue === option.value
              ? 'bg-blue-100 border-blue-500 text-blue-800 font-semibold'
              : 'bg-white border-gray-300 text-gray-600 hover:bg-gray-50 hover:border-gray-400'
            }`}
        >
          {option.label}
        </button>
      ))}
    </div>
  </div>
);

const ScoreDisplay: React.FC<{ result: TestResult }> = ({ result }) => (
  <div className="bg-blue-50 border border-blue-200 p-6 rounded-lg shadow-inner text-center">
    <h3 className="text-2xl font-bold text-blue-800 mb-3">Your PHQ-9 Score: {result.totalScore}</h3>
    <p className="text-lg font-semibold text-gray-700 mb-2">{result.severity}</p>
    <p className="text-md text-gray-600 mb-4">{result.interpretation}</p>
    
    {result.riskFlags.length > 0 && (
      <div className="mb-4 p-3 bg-red-100 border border-red-300 rounded-md">
        <p className="font-semibold text-red-700 mb-2 flex items-center">
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
          Critical Alerts:
        </p>
        <ul className="list-disc list-inside text-left text-sm text-red-600">
          {result.riskFlags.map((flag, index) => <li key={index}>{flag.replace(/_/g, ' ')}</li>)}
        </ul>
      </div>
    )}

    {result.recommendations.length > 0 && (
      <div className="mt-4 text-left">
        <p className="font-semibold text-gray-700 mb-2">Recommendations:</p>
        <ul className="list-disc list-inside text-sm text-gray-600">
          {result.recommendations.map((rec, index) => <li key={index}>{rec}</li>)}
        </ul>
      </div>
    )}
  </div>
);


export const PHQ9: React.FC<{ patient: Patient; onComplete: (result: TestResult) => void }> = ({ 
  patient, 
  onComplete 
}) => {
  const [responses, setResponses] = useState<number[]>(new Array(9).fill(0));
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [startTime] = useState(Date.now());
  const [showResults, setShowResults] = useState(false);
  const [finalResult, setFinalResult] = useState<TestResult | null>(null);
  const [validationWarnings, setValidationWarnings] = useState<string[]>([]);

  // Data fetched from DB template, could be a prop or fetched here
  const questionsData = [
    { question: "Little interest or pleasure in doing things", options: [{ value: 0, label: "Not at all" }, { value: 1, label: "Several days" }, { value: 2, label: "More than half the days" }, { value: 3, label: "Nearly every day" }] },
    { question: "Feeling down, depressed, or hopeless", options: [{ value: 0, label: "Not at all" }, { value: 1, label: "Several days" }, { value: 2, label: "More than half the days" }, { value: 3, label: "Nearly every day" }] },
    { question: "Trouble falling or staying asleep, or sleeping too much", options: [{ value: 0, label: "Not at all" }, { value: 1, label: "Several days" }, { value: 2, label: "More than half the days" }, { value: 3, label: "Nearly every day" }] },
    { question: "Feeling tired or having little energy", options: [{ value: 0, label: "Not at all" }, { value: 1, label: "Several days" }, { value: 2, label: "More than half the days" }, { value: 3, label: "Nearly every day" }] },
    { question: "Poor appetite or overeating", options: [{ value: 0, label: "Not at all" }, { value: 1, label: "Several days" }, { value: 2, label: "More than half the days" }, { value: 3, label: "Nearly every day" }] },
    { question: "Feeling bad about yourself - or that you are a failure or have let yourself or your family down", options: [{ value: 0, label: "Not at all" }, { value: 1, label: "Several days" }, { value: 2, label: "More than half the days" }, { value: 3, label: "Nearly every day" }] },
    { question: "Trouble concentrating on things, such as reading the newspaper or watching television", options: [{ value: 0, label: "Not at all" }, { value: 1, label: "Several days" }, { value: 2, label: "More than half the days" }, { value: 3, label: "Nearly every day" }] },
    { question: "Moving or speaking so slowly that other people could have noticed? Or the opposite - being so fidgety or restless that you have been moving around a lot more than usual", options: [{ value: 0, label: "Not at all" }, { value: 1, label: "Several days" }, { value: 2, label: "More than half the days" }, { value: 3, label: "Nearly every day" }] },
    { question: "Thoughts that you would be better off dead, or of hurting yourself in some way", options: [{ value: 0, label: "Not at all" }, { value: 1, label: "Several days" }, { value: 2, label: "More than half the days" }, { value: 3, label: "Nearly every day" }] }
  ];

  const handleResponse = (value: number) => {
    const newResponses = [...responses];
    newResponses[currentQuestion] = value;
    setResponses(newResponses);
  };

  const handleNext = () => {
    if (currentQuestion < questionsData.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1);
    }
  };

  const handleComplete = async () => {
    const completionTime = Math.round((Date.now() - startTime) / 1000); // in seconds
    const calculatedResult = scorePHQ9(responses);
    const validationResult = validateResponses('PHQ-9', responses, questionsData);
    setValidationWarnings(validationResult.warnings);

    if (!validationResult.isValid) {
      // Optionally show a modal or alert if validation fails critically
      console.warn("PHQ-9 validation warnings:", validationResult.warnings);
      // Decide if you want to proceed or prevent submission based on warnings
    }

    const testResultPayload = {
      patientId: patient.id,
      testName: 'PHQ-9',
      testCategory: 'depression',
      responses: responses,
      ...calculatedResult,
      completionTime: completionTime,
      validityIndices: validationResult.validityIndices,
      administeredDate: new Date(),
      administeredBy: 'Self-administered', // Get from auth context if available
    };

    try {
      await savePsychTestResult(testResultPayload);
      setFinalResult(calculatedResult);
      setShowResults(true);
      onComplete(calculatedResult); // Pass result to dashboard
    } catch (error) {
      console.error("Error saving PHQ-9 result:", error);
      // Handle error state, e.g., show error message to user
    }
  };

  if (showResults && finalResult) {
    return (
      <div className="max-w-2xl mx-auto mt-10">
        {validationWarnings.length > 0 && (
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-6" role="alert">
            <strong className="font-bold">Response Warning:</strong>
            <span className="block sm:inline"> {validationWarnings.join(' ')}</span>
          </div>
        )}
        <ScoreDisplay result={finalResult} />
        <button
          onClick={() => setShowResults(false)} // Button to go back or close
          className="mt-6 w-full py-2 px-4 bg-gray-500 text-white rounded-md hover:bg-gray-600"
        >
          Back to Dashboard
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto mt-10 bg-white rounded-lg shadow-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">PHQ-9 Depression Screen</h2>
        <span className="text-sm text-gray-500">
          Question {currentQuestion + 1} of {questionsData.length}
        </span>
      </div>
      
      <div className="progress-bar-container mb-6 h-2 bg-gray-200 rounded-full">
        <div
          className="progress-bar h-2 bg-blue-500 rounded-full transition-all duration-300 ease-in-out"
          style={{ width: `${((currentQuestion + 1) / questionsData.length) * 100}%` }}
        ></div>
      </div>

      <QuestionItem
        question={questionsData[currentQuestion].question}
        options={questionsData[currentQuestion].options}
        currentValue={responses[currentQuestion]}
        onValueChange={handleResponse}
      />

      <div className="mt-8 flex justify-between">
        <button
          onClick={handlePrevious}
          disabled={currentQuestion === 0}
          className={`py-2 px-5 rounded-md font-medium transition-colors flex items-center ${
            currentQuestion === 0
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          <ChevronLeft className="w-4 h-4 mr-1" /> Previous
        </button>
        <button
          onClick={handleNext}
          className="py-2 px-5 rounded-md font-medium bg-blue-500 text-white hover:bg-blue-600 flex items-center"
        >
          {currentQuestion < questionsData.length - 1 ? 'Next' : 'Finish'}
          {currentQuestion < questionsData.length - 1 ? <ChevronRight className="w-4 h-4 ml-1" /> : <CheckCircle className="w-4 h-4 ml-1" />}
        </button>
      </div>
    </div>
  );
};

// Mock functions used above (replace with actual imports)
const getLastTestDate = (patientId: string, testName: string): string | null => '2023-10-27';
const getLastRiskLevel = (patientId: string, testName: string): string | null => 'HIGH_SUICIDE_RISK';
// Assume savePsychTestResult API call is implemented in '@/api/psychTests'
```

---

## 🎯 FRONTEND IMPLEMENTATION NOTES

### 5. Data Persistence and API Interaction
-   **Saving Results:** When a test is completed, the frontend must send a POST request to the backend API (e.g., `/api/psych-tests`) with the collected data: `patientId`, `testName`, `testCategory`, `responses` (raw answers), `scores`, `interpretation`, `riskFlags`, `completionTime`, `validityIndices`, `administeredDate`, and `administeredBy`.
-   **Retrieving History:** The `TestHistory` component will fetch a patient's past test results via GET request (e.g., `/api/psych-tests/patient/:patientId`) and display them chronologically, possibly with summary views or links to detailed results.
-   **Error Handling:** Implement robust error handling for API calls (network issues, server errors) and validation failures. Provide user feedback for successful saves or errors.

### 6. User Experience (UX) Considerations
-   **Progress Indicators:** For longer tests, display progress (e.g., "Question 5 of 20") and a progress bar.
-   **Clear Navigation:** Easy-to-use "Next," "Previous," and "Finish" buttons.
-   **Mobile Responsiveness:** Ensure all test interfaces are fully responsive and usable on mobile devices.
-   **Accessibility:** Follow WCAG guidelines for color contrast, keyboard navigation, and screen reader compatibility.
-   **Visual Feedback:** Highlight selected answers, provide loading states, and clear success/error messages.
-   **Session Timeouts:** For sensitive data, consider implementing session timeouts with warnings.
-   **Auto-Save:** For very long or complex tests, consider implementing auto-save functionality to prevent data loss.
```