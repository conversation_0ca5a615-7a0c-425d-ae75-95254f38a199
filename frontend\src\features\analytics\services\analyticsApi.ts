import { api, handleApiError } from '../../../lib/api';
import type { 
  DashboardAnalytics, 
  PatientAnalytics, 
  AppointmentAnalytics, 
  LabResultAnalytics, 
  SystemAnalytics,
  DateRange 
} from '../types';

export const analyticsApi = {
  async getDashboardAnalytics(): Promise<DashboardAnalytics> {
    try {
      const response = await api.get('/api/analytics/dashboard');
      const backendData = response.data.data.analytics;

      // Transform backend data structure to match frontend types
      return {
        patients: {
          total: backendData.patients.total,
          recent: backendData.patients.newThisMonth || 0,
        },
        appointments: {
          total: backendData.appointments.total,
          upcoming: backendData.appointments.upcoming || 0,
        },
        labResults: {
          total: backendData.labResults.total,
          recent: backendData.labResults.pending || 0,
        },
        system: {
          totalUsers: backendData.system.totalUsers,
        },
      };
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async getPatientAnalytics(dateRange: DateRange): Promise<PatientAnalytics> {
    try {
      const params = new URLSearchParams({
        from: dateRange.from,
        to: dateRange.to,
      });

      const response = await api.get(`/api/analytics/patients?${params.toString()}`);
      return response.data.data as PatientAnalytics;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async getAppointmentAnalytics(dateRange: DateRange): Promise<AppointmentAnalytics> {
    try {
      const params = new URLSearchParams({
        from: dateRange.from,
        to: dateRange.to,
      });

      const response = await api.get(`/api/analytics/appointments?${params.toString()}`);
      return response.data.data as AppointmentAnalytics;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async getLabResultAnalytics(dateRange: DateRange): Promise<LabResultAnalytics> {
    try {
      const params = new URLSearchParams({
        from: dateRange.from,
        to: dateRange.to,
      });

      const response = await api.get(`/api/analytics/lab-results?${params.toString()}`);
      return response.data.data as LabResultAnalytics;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  async getSystemAnalytics(dateRange: DateRange): Promise<SystemAnalytics> {
    try {
      const params = new URLSearchParams({
        from: dateRange.from,
        to: dateRange.to,
      });

      const response = await api.get(`/api/analytics/system?${params.toString()}`);
      return response.data.data as SystemAnalytics;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },
};
