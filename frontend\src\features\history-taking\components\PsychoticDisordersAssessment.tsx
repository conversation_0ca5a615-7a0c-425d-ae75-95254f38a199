import React, { useState, useMemo } from 'react';
import type { DisorderAssessment, AssessmentProgress } from '../types';
import { getDisordersByCategory, validateDisorderCriteria } from '../data/disorders-registry';
import { <PERSON>, CardHeader, CardTitle, CardContent, Badge } from '../../../components/ui';
import { CheckCircle, Circle, AlertCircle, Clock, Brain } from 'lucide-react';

interface PsychoticDisordersAssessmentProps {
  patientId: string;
  onAssessmentChange: (assessment: Partial<DisorderAssessment>) => void;
  initialAssessment?: Partial<DisorderAssessment>;
}

const PsychoticDisordersAssessment: React.FC<PsychoticDisordersAssessmentProps> = ({
  patientId,
  onAssessmentChange,
  initialAssessment
}) => {
  const [selectedDisorder, setSelectedDisorder] = useState<string>(
    initialAssessment?.disorderId || ''
  );
  const [criteriaResponses, setCriteriaResponses] = useState<Record<string, boolean>>(
    initialAssessment?.criteria?.reduce((acc, c) => ({ ...acc, [c.id]: c.present }), {}) || {}
  );
  const [severityRatings] = useState<Record<string, number>>(
    initialAssessment?.criteria?.reduce((acc, c) => ({ ...acc, [c.id]: c.severity || 1 }), {}) || {}
  );
  const [durationInfo, setDurationInfo] = useState<Record<string, string>>(
    initialAssessment?.criteria?.reduce((acc, c) => ({ ...acc, [c.id]: c.duration || '' }), {}) || {}
  );

  // Get psychotic disorders
  const psychoticDisorders = useMemo(() => {
    return getDisordersByCategory('schizophrenia-spectrum');
  }, []);

  const selectedDisorderData = useMemo(() => {
    return psychoticDisorders.find(d => d.id === selectedDisorder);
  }, [psychoticDisorders, selectedDisorder]);

  // Calculate assessment progress
  const assessmentProgress = useMemo((): AssessmentProgress => {
    if (!selectedDisorderData) {
      return {
        totalCriteria: 0,
        completedCriteria: 0,
        requiredCriteria: 0,
        metCriteria: 0,
        percentComplete: 0,
        isValid: false,
        canDiagnose: false,
        missingRequired: []
      };
    }

    const validation = validateDisorderCriteria(selectedDisorder, criteriaResponses);
    const totalCriteria = selectedDisorderData.criteria.length;
    const completedCriteria = Object.keys(criteriaResponses).length;
    const requiredCriteria = selectedDisorderData.criteria.filter(c => c.required).length;

    return {
      totalCriteria,
      completedCriteria,
      requiredCriteria,
      metCriteria: validation.metCriteria.length,
      percentComplete: totalCriteria > 0 ? (completedCriteria / totalCriteria) * 100 : 0,
      isValid: validation.isValid,
      canDiagnose: validation.canDiagnose,
      missingRequired: validation.missingRequired
    };
  }, [selectedDisorderData, selectedDisorder, criteriaResponses]);

  const handleCriterionChange = (criterionId: string, present: boolean) => {
    const newResponses = { ...criteriaResponses, [criterionId]: present };
    setCriteriaResponses(newResponses);
    updateAssessment(newResponses);
  };

  const updateAssessment = (newResponses = criteriaResponses) => {
    if (!selectedDisorderData) return;

    const criteria = selectedDisorderData.criteria.map(criterion => ({
      id: criterion.id,
      code: criterion.code,
      description: criterion.description,
      present: newResponses[criterion.id] || false,
      severity: severityRatings[criterion.id] || 1,
      duration: durationInfo[criterion.id] || '',
      comments: ''
    }));

    const assessment: Partial<DisorderAssessment> = {
      disorderId: selectedDisorder,
      patientId,
      criteria,
      assessmentDate: new Date().toISOString(),
      status: 'draft'
    };

    onAssessmentChange(assessment);
  };

  const getSymptomTypeIcon = (criterionId: string) => {
    if (criterionId.includes('a1') || criterionId.includes('a2')) {
      return <Brain className="h-4 w-4 text-purple-600" />; // Positive symptoms
    }
    if (criterionId.includes('a5')) {
      return <Circle className="h-4 w-4 text-gray-600" />; // Negative symptoms
    }
    return <AlertCircle className="h-4 w-4 text-orange-600" />; // Disorganization
  };

  const getSymptomTypeLabel = (criterionId: string) => {
    if (criterionId.includes('a1') || criterionId.includes('a2')) {
      return 'Positive Symptom';
    }
    if (criterionId.includes('a5')) {
      return 'Negative Symptom';
    }
    if (criterionId.includes('a3') || criterionId.includes('a4')) {
      return 'Disorganization';
    }
    return 'Other';
  };

  return (
    <div className="space-y-6">
      {/* Disorder Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Brain className="h-5 w-5 text-purple-600" />
            <span>Select Psychotic Disorder</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {psychoticDisorders.map(disorder => (
              <button
                key={disorder.id}
                onClick={() => setSelectedDisorder(disorder.id)}
                className={`p-4 text-left border rounded-lg transition-all ${
                  selectedDisorder === disorder.id
                    ? 'border-purple-500 bg-purple-50 shadow-md'
                    : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
                }`}
              >
                <div className="font-medium text-gray-900">{disorder.name}</div>
                <div className="text-sm text-purple-600 mt-1">{disorder.code}</div>
                <div className="text-xs text-gray-500 mt-2 line-clamp-2">{disorder.description}</div>
                {disorder.durationRequirement && (
                  <div className="flex items-center space-x-1 mt-2">
                    <Clock className="h-3 w-3 text-gray-400" />
                    <span className="text-xs text-gray-500">{disorder.durationRequirement}</span>
                  </div>
                )}
              </button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Assessment Progress */}
      {selectedDisorderData && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-gray-900">Assessment Progress</h3>
                <p className="text-sm text-gray-500">
                  {assessmentProgress.completedCriteria} of {assessmentProgress.totalCriteria} criteria assessed
                </p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900">
                  {Math.round(assessmentProgress.percentComplete)}%
                </div>
                {assessmentProgress.canDiagnose ? (
                  <Badge variant="success">Diagnostic Criteria Met</Badge>
                ) : (
                  <Badge variant="secondary">Assessment Incomplete</Badge>
                )}
              </div>
            </div>
            
            <div className="mt-4 bg-gray-200 rounded-full h-2">
              <div 
                className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${assessmentProgress.percentComplete}%` }}
              />
            </div>

            {assessmentProgress.missingRequired.length > 0 && (
              <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center space-x-2">
                  <AlertCircle className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm font-medium text-yellow-800">
                    Missing required criteria: {assessmentProgress.missingRequired.join(', ')}
                  </span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Disorder Information */}
      {selectedDisorderData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <span>{selectedDisorderData.name}</span>
              <Badge variant="outline">{selectedDisorderData.code}</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Clinical Description</h4>
                <p className="text-sm text-gray-600">{selectedDisorderData.description}</p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Key Features</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {selectedDisorderData.diagnosticFeatures.map((feature, index) => (
                    <div key={index} className="flex items-start space-x-2 p-2 bg-gray-50 rounded">
                      <CheckCircle className="h-3 w-3 mt-1 text-green-500 flex-shrink-0" />
                      <span className="text-xs text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>

              {selectedDisorderData.prevalence && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Prevalence</h4>
                  <p className="text-sm text-gray-600">{selectedDisorderData.prevalence}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Criteria Assessment */}
      {selectedDisorderData && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
            <span>Diagnostic Criteria Assessment</span>
            <Badge variant="outline" className="text-xs">
              DSM-5-TR {selectedDisorderData.code}
            </Badge>
          </h3>
          
          {selectedDisorderData.criteria.map(criterion => (
            <Card key={criterion.id} className="border-l-4 border-l-purple-500">
              <CardContent className="p-4">
                <div className="flex items-start space-x-3">
                  <button
                    onClick={() => handleCriterionChange(criterion.id, !criteriaResponses[criterion.id])}
                    className="mt-1 flex-shrink-0"
                  >
                    {criteriaResponses[criterion.id] ? (
                      <CheckCircle className="h-6 w-6 text-green-600" />
                    ) : (
                      <Circle className="h-6 w-6 text-gray-400" />
                    )}
                  </button>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <Badge variant="outline" className="font-mono">{criterion.code}</Badge>
                      {criterion.required && (
                        <Badge variant="destructive" className="text-xs">Required</Badge>
                      )}
                      <Badge variant="secondary" className="text-xs">{criterion.type}</Badge>
                    </div>
                    
                    <p className="text-sm text-gray-700 mb-3 leading-relaxed">
                      {criterion.description}
                    </p>
                    
                    {/* Sub-criteria for complex criteria */}
                    {criterion.subCriteria && criterion.subCriteria.length > 0 && (
                      <div className="ml-4 space-y-3 border-l-2 border-gray-200 pl-4">
                        <h5 className="text-sm font-medium text-gray-800">Specific Symptoms:</h5>
                        {criterion.subCriteria.map(subCriterion => (
                          <div key={subCriterion.id} className="flex items-start space-x-2">
                            <button
                              onClick={() => handleCriterionChange(subCriterion.id, !criteriaResponses[subCriterion.id])}
                              className="mt-1 flex-shrink-0"
                            >
                              {criteriaResponses[subCriterion.id] ? (
                                <CheckCircle className="h-4 w-4 text-green-600" />
                              ) : (
                                <Circle className="h-4 w-4 text-gray-400" />
                              )}
                            </button>
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-1">
                                <Badge variant="outline" className="text-xs font-mono">{subCriterion.code}</Badge>
                                {getSymptomTypeIcon(subCriterion.id)}
                                <span className="text-xs text-gray-500">{getSymptomTypeLabel(subCriterion.id)}</span>
                              </div>
                              <p className="text-xs text-gray-600 leading-relaxed">{subCriterion.description}</p>
                            </div>
                          </div>
                        ))}
                        
                        {/* Validation info for sub-criteria */}
                        {criterion.validationRules?.map(rule => (
                          <div key={rule.type} className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-xs">
                            <strong>Note:</strong> {rule.message}
                          </div>
                        ))}
                      </div>
                    )}
                    
                    {/* Duration tracking for time-sensitive criteria */}
                    {criteriaResponses[criterion.id] && criterion.type === 'duration' && (
                      <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Duration Information
                        </label>
                        <input
                          type="text"
                          value={durationInfo[criterion.id] || ''}
                          onChange={(e) => setDurationInfo(prev => ({ ...prev, [criterion.id]: e.target.value }))}
                          placeholder="e.g., 6 months, 2 weeks, etc."
                          className="w-full px-3 py-2 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default PsychoticDisordersAssessment;
