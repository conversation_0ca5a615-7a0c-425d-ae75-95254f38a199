// Using built-in fetch in Node.js 18+

async function testLogin() {
  try {
    console.log('Testing login...');
    
    const response = await fetch('http://localhost:3002/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: '123456789'
      })
    });
    
    const data = await response.json();
    console.log('Login response:', JSON.stringify(data, null, 2));
    
    if (data.success && data.data.accessToken) {
      console.log('Login successful! Testing appointments endpoint...');
      
      // Test POST /api/appointments
      const appointmentResponse = await fetch('http://localhost:3002/api/appointments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${data.data.accessToken}`
        },
        body: JSON.stringify({
          patientId: 'test-patient-id',
          providerId: data.data.user.id,
          date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          duration: 60,
          type: 'CONSULTATION'
        })
      });
      
      console.log('Appointment POST status:', appointmentResponse.status);
      const appointmentData = await appointmentResponse.text();
      console.log('Appointment POST response:', appointmentData);
      
      // Test GET /api/analytics/dashboard
      console.log('Testing analytics dashboard...');
      const analyticsResponse = await fetch('http://localhost:3002/api/analytics/dashboard', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${data.data.accessToken}`
        }
      });
      
      console.log('Analytics dashboard status:', analyticsResponse.status);
      console.log('Analytics dashboard headers:', Object.fromEntries(analyticsResponse.headers.entries()));
      const analyticsData = await analyticsResponse.text();
      console.log('Analytics dashboard response:', analyticsData);
    }
    
  } catch (error) {
    console.error('Test error:', error);
  }
}

testLogin();