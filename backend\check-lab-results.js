const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkLabResults() {
  try {
    const labResults = await prisma.labResult.findMany({
      take: 5
    });
    console.log('Current lab results:', labResults.length);
    labResults.forEach(lr => {
      console.log(`- ID: ${lr.id}, Type: ${lr.testType}, Date: ${lr.testDate}`);
    });
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkLabResults();
