import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// UI State Types
interface SidebarState {
  isCollapsed: boolean;
  activeSection: string;
}

interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
}

interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actionUrl?: string;
}

interface ModalState {
  activeModal: string | null;
  modalData: any;
}

interface TableState {
  [tableId: string]: {
    page: number;
    pageSize: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    filters: Record<string, any>;
  };
}

interface UIState {
  // Sidebar
  sidebar: SidebarState;
  setSidebarCollapsed: (collapsed: boolean) => void;
  setActiveSection: (section: string) => void;

  // Notifications
  notifications: NotificationState;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;
  markNotificationRead: (id: string) => void;
  markAllNotificationsRead: () => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;

  // Modals
  modal: ModalState;
  openModal: (modalId: string, data?: any) => void;
  closeModal: () => void;

  // Tables
  tables: TableState;
  setTableState: (tableId: string, state: Partial<TableState[string]>) => void;
  resetTableState: (tableId: string) => void;

  // Theme
  theme: 'light' | 'dark' | 'system';
  setTheme: (theme: 'light' | 'dark' | 'system') => void;

  // Loading states
  globalLoading: boolean;
  setGlobalLoading: (loading: boolean) => void;

  // Search
  globalSearch: string;
  setGlobalSearch: (search: string) => void;
}

export const useUIStore = create<UIState>()(
  persist(
    (set) => ({
      // Sidebar
      sidebar: {
        isCollapsed: false,
        activeSection: 'dashboard',
      },
      setSidebarCollapsed: (collapsed) =>
        set((state) => ({
          sidebar: { ...state.sidebar, isCollapsed: collapsed },
        })),
      setActiveSection: (section) =>
        set((state) => ({
          sidebar: { ...state.sidebar, activeSection: section },
        })),

      // Notifications
      notifications: {
        notifications: [],
        unreadCount: 0,
      },
      addNotification: (notification) =>
        set((state) => {
          const newNotification: Notification = {
            ...notification,
            id: Math.random().toString(36).substr(2, 9),
            timestamp: new Date(),
            read: false,
          };
          return {
            notifications: {
              notifications: [newNotification, ...state.notifications.notifications],
              unreadCount: state.notifications.unreadCount + 1,
            },
          };
        }),
      markNotificationRead: (id) =>
        set((state) => ({
          notifications: {
            notifications: state.notifications.notifications.map((n) =>
              n.id === id ? { ...n, read: true } : n
            ),
            unreadCount: Math.max(0, state.notifications.unreadCount - 1),
          },
        })),
      markAllNotificationsRead: () =>
        set((state) => ({
          notifications: {
            notifications: state.notifications.notifications.map((n) => ({ ...n, read: true })),
            unreadCount: 0,
          },
        })),
      removeNotification: (id) =>
        set((state) => {
          const notification = state.notifications.notifications.find((n) => n.id === id);
          const wasUnread = notification && !notification.read;
          return {
            notifications: {
              notifications: state.notifications.notifications.filter((n) => n.id !== id),
              unreadCount: wasUnread
                ? Math.max(0, state.notifications.unreadCount - 1)
                : state.notifications.unreadCount,
            },
          };
        }),
      clearNotifications: () =>
        set(() => ({
          notifications: {
            notifications: [],
            unreadCount: 0,
          },
        })),

      // Modals
      modal: {
        activeModal: null,
        modalData: null,
      },
      openModal: (modalId, data) =>
        set(() => ({
          modal: {
            activeModal: modalId,
            modalData: data,
          },
        })),
      closeModal: () =>
        set(() => ({
          modal: {
            activeModal: null,
            modalData: null,
          },
        })),

      // Tables
      tables: {},
      setTableState: (tableId, state) =>
        set((currentState) => ({
          tables: {
            ...currentState.tables,
            [tableId]: {
              ...currentState.tables[tableId],
              ...state,
            },
          },
        })),
      resetTableState: (tableId) =>
        set((state) => {
          const { [tableId]: _, ...rest } = state.tables;
          return { tables: rest };
        }),

      // Theme
      theme: 'system',
      setTheme: (theme) => set(() => ({ theme })),

      // Loading states
      globalLoading: false,
      setGlobalLoading: (loading) => set(() => ({ globalLoading: loading })),

      // Search
      globalSearch: '',
      setGlobalSearch: (search) => set(() => ({ globalSearch: search })),
    }),
    {
      name: 'ui-store',
      partialize: (state) => ({
        sidebar: state.sidebar,
        theme: state.theme,
        tables: state.tables,
      }),
    }
  )
);
