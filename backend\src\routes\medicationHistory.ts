import { Router } from 'express';
import { MedicationHistoryController } from '../controllers/medicationHistoryController';
import { authenticate } from '../middleware/auth';
import { authorize } from '../middleware/auth';
import { asyncHandler } from '../utils/asyncHandler';

const router = Router();

// Apply authentication to all routes
router.use(authenticate);

/**
 * @route   GET /api/medication-history
 * @desc    Get all medication history with optional filters
 * @access  Private (All authenticated users)
 * @query   patientId, medicationName, isActive, page, limit
 */
router.get('/', asyncHandler(MedicationHistoryController.getAllMedicationHistory));

/**
 * @route   POST /api/medication-history
 * @desc    Create a new medication history entry
 * @access  Private (ADMIN, CLINICIAN)
 * @body    Medication history data
 */
router.post('/', authorize(['ADMIN', 'CLINICIAN']), asyncHandler(MedicationHistoryController.createMedicationHistory));

/**
 * @route   GET /api/medication-history/:id
 * @desc    Get medication history by ID
 * @access  Private (All authenticated users)
 */
router.get('/:id', asyncHandler(MedicationHistoryController.getMedicationHistoryById));

/**
 * @route   PUT /api/medication-history/:id
 * @desc    Update medication history
 * @access  Private (ADMIN, CLINICIAN)
 */
router.put('/:id', authorize(['ADMIN', 'CLINICIAN']), asyncHandler(MedicationHistoryController.updateMedicationHistory));

/**
 * @route   DELETE /api/medication-history/:id
 * @desc    Delete medication history
 * @access  Private (ADMIN, CLINICIAN)
 */
router.delete('/:id', authorize(['ADMIN', 'CLINICIAN']), asyncHandler(MedicationHistoryController.deleteMedicationHistory));

/**
 * @route   GET /api/medication-history/patient/:patientId
 * @desc    Get medication history by patient ID
 * @access  Private (All authenticated users)
 * @query   isActive, limit
 */
router.get('/patient/:patientId', asyncHandler(MedicationHistoryController.getMedicationHistoryByPatient));

/**
 * @route   GET /api/medication-history/patient/:patientId/active
 * @desc    Get active medications for patient
 * @access  Private (All authenticated users)
 */
router.get('/patient/:patientId/active', asyncHandler(MedicationHistoryController.getActiveMedications));

/**
 * @route   PUT /api/medication-history/:id/discontinue
 * @desc    Discontinue medication
 * @access  Private (ADMIN, CLINICIAN)
 * @body    discontinuedReason, endDate
 */
router.put('/:id/discontinue', authorize(['ADMIN', 'CLINICIAN']), asyncHandler(MedicationHistoryController.discontinueMedication));

export default router;
