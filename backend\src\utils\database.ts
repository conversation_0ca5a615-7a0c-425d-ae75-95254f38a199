import { PrismaClient } from '@prisma/client';
import { logger } from './logger';

/**
 * Enhanced Prisma client with logging
 */
class DatabaseClient extends PrismaClient {
  constructor() {
    super({
      log: [
        {
          emit: 'event',
          level: 'query',
        },
        {
          emit: 'event',
          level: 'error',
        },
        {
          emit: 'event',
          level: 'info',
        },
        {
          emit: 'event',
          level: 'warn',
        },
      ],
    });

    // Prisma event listeners disabled due to version compatibility issues
    // TODO: Re-enable when Prisma client is properly generated
  }

  /**
   * Enhanced query method with logging
   */
  async loggedQuery<T>(
    operation: string,
    queryFn: () => Promise<T>,
    context?: Record<string, any>
  ): Promise<T> {
    const start = Date.now();
    
    logger.info('Database operation started', {
      operation,
      context,
      timestamp: new Date().toISOString(),
    });

    try {
      const result = await queryFn();
      const duration = Date.now() - start;
      
      logger.info('Database operation completed', {
        operation,
        duration: `${duration}ms`,
        context,
        timestamp: new Date().toISOString(),
      });

      return result;
    } catch (error) {
      const duration = Date.now() - start;
      
      logger.error('Database operation failed', {
        operation,
        duration: `${duration}ms`,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        context,
        timestamp: new Date().toISOString(),
      });

      throw error;
    }
  }
}

// Export singleton instance
export const prisma = new DatabaseClient();

// Export connection health check
export const checkDatabaseConnection = async (): Promise<boolean> => {
  try {
    await prisma.$queryRaw`SELECT 1`;
    logger.info('Database connection healthy');
    return true;
  } catch (error) {
    logger.error('Database connection failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    return false;
  }
};

// Export graceful disconnect
export const disconnectDatabase = async (): Promise<void> => {
  try {
    await prisma.$disconnect();
    logger.info('Database disconnected gracefully');
  } catch (error) {
    logger.error('Database disconnect failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};
