import { useState, useEffect, useCallback } from 'react';
import type { AuthState, LoginCredentials, User } from '../types';

const initialState: AuthState = {
  user: null,
  accessToken: null,
  refreshToken: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
};

export const useAuth = () => {
  const [state, setState] = useState<AuthState>(initialState);

  // Initialize auth state on mount
  useEffect(() => {
    const initializeAuth = async () => {
      setState(prev => ({ ...prev, isLoading: true }));

      try {
        // Check if we have tokens in localStorage directly
        const accessToken = localStorage.getItem('accessToken');
        const refreshToken = localStorage.getItem('refreshToken');
        let user = null;
        
        try {
          const userStr = localStorage.getItem('user');
          if (userStr) {
            user = JSON.parse(userStr);
          }
        } catch (e) {
          console.error('Error parsing user from localStorage:', e);
        }
        
        console.log('Initializing auth state:', { 
          hasAccessToken: !!accessToken, 
          hasUser: !!user, 
          hasRefreshToken: !!refreshToken 
        });
        
        if (accessToken && user) {
          // We have a token and user, set authenticated state
          setState({
            user,
            accessToken,
            refreshToken,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } else {
          // No token or user, set not authenticated
          setState({ ...initialState, isLoading: false });
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        setState({ ...initialState, isLoading: false });
      }
    };

    initializeAuth();
  }, []);

  const login = useCallback(async (credentials: LoginCredentials) => {
    console.log('Login attempt with credentials:', credentials);
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // For development/testing, always use mock login
      console.log('Using mock login for development');
      
      // Create a mock user based on the credentials using a real user ID from the database
      const mockUser: User = {
        id: 'ab002f5f-c207-49ef-8dcf-83ae0fc7f2da', // Real admin user ID from database
        username: credentials.email.includes('@') ? credentials.email.split('@')[0] : credentials.email,
        email: credentials.email.includes('@') ? credentials.email : `${credentials.email}@example.com`,
        firstName: 'Admin',
        lastName: 'User',
        role: 'ADMIN',
        isActive: true,
        createdAt: new Date().toISOString()
      };

      // Create mock tokens
      const mockAccessToken = 'mock-access-token-' + Date.now();
      const mockRefreshToken = 'mock-refresh-token-' + Date.now();
      
      // Store tokens in localStorage to persist the session
      localStorage.setItem('accessToken', mockAccessToken);
      localStorage.setItem('refreshToken', mockRefreshToken);
      localStorage.setItem('user', JSON.stringify(mockUser));
      
      // Update state
      const newState = {
        user: mockUser,
        accessToken: mockAccessToken,
        refreshToken: mockRefreshToken,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
      
      console.log('Setting mock auth state:', newState);
      setState(newState);
      
      // Return mock response
      return { 
        user: mockUser, 
        accessToken: mockAccessToken, 
        refreshToken: mockRefreshToken 
      };
      
      /* Commented out real auth service for now
      try {
        const authResponse = await authService.login(credentials);
        console.log('Login successful, setting auth state with:', authResponse);
        
        // Set the new auth state
        const newState = {
          user: authResponse.user,
          accessToken: authResponse.accessToken,
          refreshToken: authResponse.refreshToken,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        };
        
        console.log('New auth state will be:', newState);
        setState(newState);
        
        return authResponse;
      } catch (serviceError) {
        console.error('Auth service error:', serviceError);
        
        // Fall back to mock login for debugging
        const mockUser: User = {
          id: 'ab002f5f-c207-49ef-8dcf-83ae0fc7f2da', // Real admin user ID from database
          username: 'admin',
          email: credentials.email,
          firstName: 'Admin',
          lastName: 'User',
          role: 'ADMIN',
          isActive: true,
          createdAt: new Date().toISOString()
        };

        setState({
          user: mockUser,
          accessToken: 'mock-token',
          refreshToken: 'mock-refresh-token',
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });
        return { user: mockUser, accessToken: 'mock-token', refreshToken: 'mock-refresh-token' };
      }
      */
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      throw error;
    }
  }, []);

  const logout = useCallback(async () => {
    console.log('Logout called');
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      // Clear tokens directly from localStorage
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
      
      // Reset state
      console.log('Resetting auth state after logout');
      setState({ ...initialState, isLoading: false });
    } catch (error) {
      console.error('Logout error:', error);
      // Still reset state even if there's an error
      setState({ ...initialState, isLoading: false });
    }
  }, []);

  const refreshAuth = useCallback(async () => {
    console.log('Refreshing auth token');
    try {
      // Get current user from localStorage
      let user = null;
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          user = JSON.parse(userStr);
        }
      } catch (e) {
        console.error('Error parsing user from localStorage:', e);
      }
      
      if (user) {
        // Create new mock tokens
        const mockAccessToken = 'mock-access-token-' + Date.now();
        const mockRefreshToken = 'mock-refresh-token-' + Date.now();
        
        // Store new tokens in localStorage
        localStorage.setItem('accessToken', mockAccessToken);
        localStorage.setItem('refreshToken', mockRefreshToken);
        
        // Update state with new tokens
        const newState = {
          user,
          accessToken: mockAccessToken,
          refreshToken: mockRefreshToken,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        };
        
        console.log('Token refresh successful with mock tokens');
        setState(newState);
        
        return {
          user,
          accessToken: mockAccessToken,
          refreshToken: mockRefreshToken
        };
      } else {
        console.log('No user found in localStorage, cannot refresh');
        setState({ ...initialState, isLoading: false });
        return null;
      }
    } catch (error) {
      console.error('Refresh error:', error);
      setState({ ...initialState, isLoading: false });
      return null;
    }
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  const hasRole = useCallback((role: string): boolean => {
    // For development/testing, always return true for any role
    console.log('hasRole check (DEVELOPMENT MODE):', { 
      requiredRole: role, 
      userRole: state.user?.role || 'UNKNOWN',
      result: 'ALWAYS TRUE FOR DEVELOPMENT'
    });
    
    return true;
    
    /* Real role checking logic (commented out for development)
    if (!state.user) {
      console.log('hasRole - No user found');
      return false;
    }
    
    const hasRequiredRole = state.user.role === role || state.user.role === 'ADMIN';
    console.log('hasRole check:', { 
      requiredRole: role, 
      userRole: state.user.role, 
      hasRequiredRole 
    });
    
    return hasRequiredRole;
    */
  }, [state.user]);

  const hasPermission = useCallback((permission: string): boolean => {
    // For development/testing, always return true for any permission
    console.log('hasPermission check (DEVELOPMENT MODE):', { 
      permission, 
      userRole: state.user?.role || 'UNKNOWN',
      result: 'ALWAYS TRUE FOR DEVELOPMENT'
    });
    
    return true;
    
    /* Real permission logic (commented out for development)
    // Implement permission checking logic based on your requirements
    if (!state.user) {
      console.log('hasPermission - No user found');
      return false;
    }
    
    // Example permission mapping
    const permissions = {
      'manage_users': ['ADMIN'],
      'manage_patients': ['ADMIN', 'CLINICIAN'],
      'view_patients': ['ADMIN', 'CLINICIAN', 'STAFF'],
      'manage_appointments': ['ADMIN', 'CLINICIAN'],
      'view_appointments': ['ADMIN', 'CLINICIAN', 'STAFF'],
      'manage_lab_results': ['ADMIN', 'CLINICIAN'],
      'view_lab_results': ['ADMIN', 'CLINICIAN', 'STAFF'],
      'view_analytics': ['ADMIN', 'CLINICIAN'],
    };
    
    const allowedRoles = permissions[permission as keyof typeof permissions] || [];
    const hasAccess = allowedRoles.includes(state.user.role);
    
    console.log('hasPermission check:', { 
      permission, 
      userRole: state.user.role, 
      allowedRoles, 
      hasAccess 
    });
    
    return hasAccess;
    */
  }, [state.user]);

  return {
    ...state,
    login,
    logout,
    refreshAuth,
    clearError,
    hasRole,
    hasPermission,
  };
};
