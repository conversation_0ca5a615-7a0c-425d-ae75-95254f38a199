const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addSampleData() {
  try {
    console.log('Adding sample data...');

    // Get existing patients to add lab results and appointments
    const patients = await prisma.patient.findMany();
    
    if (patients.length === 0) {
      console.log('No patients found. Please add some patients first.');
      return;
    }

    console.log(`Found ${patients.length} patients`);

    // Add sample lab results
    const labResults = [
      {
        patientId: patients[0].id,
        testType: 'CBC',
        testDate: new Date('2024-01-15'),
        orderedBy: 'Dr. <PERSON>',
        labName: 'Central Lab',
        results: JSON.stringify({
          hemoglobin: { value: '12.5', unit: 'g/dL' },
          hematocrit: { value: '38.2', unit: '%' },
          wbc: { value: '7.2', unit: 'K/uL' }
        }),
        normalRanges: JSON.stringify({
          hemoglobin: '12.0-15.5 g/dL',
          hematocrit: '36-46%',
          wbc: '4.5-11.0 K/uL'
        }),
        notes: 'Normal complete blood count',
        status: 'COMPLETED'
      },
      {
        patientId: patients[0].id,
        testType: 'THYROID',
        testDate: new Date('2024-01-15'),
        orderedBy: 'Dr. Smith',
        labName: 'Central Lab',
        results: JSON.stringify({
          tsh: { value: '2.1', unit: 'mIU/L' },
          t4: { value: '8.5', unit: 'ug/dL' }
        }),
        normalRanges: JSON.stringify({
          tsh: '0.4-4.0 mIU/L',
          t4: '4.5-12.0 ug/dL'
        }),
        notes: 'Thyroid function within normal range',
        status: 'COMPLETED'
      }
    ];

    if (patients.length > 1) {
      labResults.push({
        patientId: patients[1].id,
        testType: 'LIPID_PANEL',
        testDate: new Date('2024-01-20'),
        orderedBy: 'Dr. Johnson',
        labName: 'Central Lab',
        results: JSON.stringify({
          totalCholesterol: { value: '180', unit: 'mg/dL' },
          ldl: { value: '110', unit: 'mg/dL' },
          hdl: { value: '55', unit: 'mg/dL' },
          triglycerides: { value: '120', unit: 'mg/dL' }
        }),
        normalRanges: JSON.stringify({
          totalCholesterol: '<200 mg/dL',
          ldl: '<100 mg/dL',
          hdl: '>40 mg/dL',
          triglycerides: '<150 mg/dL'
        }),
        notes: 'Lipid levels within acceptable range',
        status: 'COMPLETED'
      });
    }

    for (const labResult of labResults) {
      await prisma.labResult.create({ data: labResult });
    }

    console.log(`Added ${labResults.length} lab results`);

    // Add sample appointments
    const appointments = [
      {
        patientId: patients[0].id,
        date: new Date('2024-02-15T10:00:00'),
        duration: 60,
        type: 'FOLLOW_UP',
        status: 'SCHEDULED',
        notes: 'Follow-up consultation'
      },
      {
        patientId: patients[0].id,
        date: new Date('2024-02-22T14:30:00'),
        duration: 30,
        type: 'FOLLOW_UP',
        status: 'SCHEDULED',
        notes: 'Review lab results'
      }
    ];

    if (patients.length > 1) {
      appointments.push({
        patientId: patients[1].id,
        date: new Date('2024-02-16T09:00:00'),
        duration: 45,
        type: 'INITIAL_CONSULTATION',
        status: 'SCHEDULED',
        notes: 'Initial consultation'
      });
    }

    for (const appointment of appointments) {
      await prisma.appointment.create({ data: appointment });
    }

    console.log(`Added ${appointments.length} appointments`);

    // Add some notifications - need to get a valid user ID first
    const users = await prisma.user.findMany();
    if (users.length > 0) {
      const notifications = [
        {
          type: 'APPOINTMENT_REMINDER',
          title: 'Upcoming Appointment',
          message: 'You have an appointment scheduled for tomorrow at 10:00 AM',
          recipientId: users[0].id,
          isRead: false
        },
        {
          type: 'LAB_RESULT',
          title: 'Lab Results Available',
          message: 'New lab results are available for review',
          recipientId: users[0].id,
          isRead: false
        }
      ];

      for (const notification of notifications) {
        await prisma.notification.create({ data: notification });
      }

      console.log(`Added ${notifications.length} notifications`);
    } else {
      console.log('No users found, skipping notifications');
    }

    console.log('Sample data added successfully!');

  } catch (error) {
    console.error('Error adding sample data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addSampleData();
