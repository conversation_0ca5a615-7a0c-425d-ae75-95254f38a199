const { PrismaClient } = require('@prisma/client');

async function checkUsers() {
  const prisma = new PrismaClient();
  
  try {
    console.log('Checking available users...');
    
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true
      }
    });
    
    console.log('Available users:');
    users.forEach(user => {
      console.log(`- ${user.firstName} ${user.lastName} (${user.username}) - Role: ${user.role}, Active: ${user.isActive}, ID: ${user.id}`);
    });
    
    const clinicians = users.filter(u => ['ADMIN', 'CLINICIAN'].includes(u.role) && u.isActive);
    console.log('\nAvailable providers (ADMIN/CLINICIAN):');
    clinicians.forEach(user => {
      console.log(`- ${user.firstName} ${user.lastName} (${user.username}) - ID: ${user.id}`);
    });
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUsers();