const http = require('http');

console.log('🧪 Running simple health check test...');

// Test the health endpoint
const options = {
  hostname: 'localhost',
  port: 3002,
  path: '/health',
  method: 'GET',
  timeout: 5000
};

const req = http.request(options, (res) => {
  let data = '';
  
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      
      console.log('✅ Health check response:', response);
      
      if (res.statusCode === 200 && response.success === true) {
        console.log('✅ Health check PASSED');
        console.log('✅ Server is responding correctly');
        process.exit(0);
      } else {
        console.log('❌ Health check FAILED - Invalid response');
        console.log('Status Code:', res.statusCode);
        console.log('Response:', response);
        process.exit(1);
      }
    } catch (error) {
      console.log('❌ Health check FAILED - Invalid JSON response');
      console.log('Raw response:', data);
      console.log('Error:', error.message);
      process.exit(1);
    }
  });
});

req.on('error', (error) => {
  console.log('❌ Health check FAILED - Connection error');
  console.log('Error:', error.message);
  console.log('Make sure the server is running on port 3002');
  process.exit(1);
});

req.on('timeout', () => {
  console.log('❌ Health check FAILED - Request timeout');
  console.log('Server may be unresponsive');
  req.destroy();
  process.exit(1);
});

req.end();
