import { PrismaClient } from '@prisma/client';
import { startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, subDays, subWeeks, subMonths } from 'date-fns';
import { ApiResponse } from '@/types/index';

const prisma = new PrismaClient();

/**
 * Analytics service providing comprehensive reporting and insights
 * Includes patient, appointment, lab result, and system analytics
 */
export class AnalyticsService {
  /**
   * Get dashboard analytics overview
   */
  static async getDashboardAnalytics(
    userId: string,
    userRole: string
  ): Promise<ApiResponse<{ analytics: any }>> {
    const now = new Date();
    const today = startOfDay(now);
    const thisWeek = startOfWeek(now);
    const thisMonth = startOfMonth(now);

    // Role-based filtering - temporarily disabled to fix zero counts issue
    const patientFilter = {};
    const appointmentFilter = {};

    const [
      // Patient metrics
      totalPatients,
      newPatientsThisWeek,
      newPatientsThisMonth,
      
      // Appointment metrics
      totalAppointments,
      todayAppointments,
      thisWeekAppointments,
      upcomingAppointments,
      
      // Lab result metrics
      totalLabResults,
      pendingLabResults,
      flaggedLabResults,
      
      // System metrics
      totalUsers,
      activeUsers,
    ] = await Promise.all([
      // Patients
      prisma.patient.count({
        where: { 
          isDeleted: false,
          ...patientFilter 
        },
      }),
      prisma.patient.count({
        where: {
          isDeleted: false,
          createdAt: { gte: thisWeek },
          ...patientFilter,
        },
      }),
      prisma.patient.count({
        where: {
          isDeleted: false,
          createdAt: { gte: thisMonth },
          ...patientFilter,
        },
      }),
      
      // Appointments
      prisma.appointment.count({
        where: { 
          isDeleted: false,
          ...appointmentFilter 
        },
      }),
      prisma.appointment.count({
        where: {
          isDeleted: false,
          date: { gte: today, lt: endOfDay(now) },
          status: { in: ['SCHEDULED', 'CONFIRMED', 'IN_PROGRESS'] },
          ...appointmentFilter,
        },
      }),
      prisma.appointment.count({
        where: {
          isDeleted: false,
          date: { gte: thisWeek, lt: endOfWeek(now) },
          ...appointmentFilter,
        },
      }),
      prisma.appointment.count({
        where: {
          isDeleted: false,
          date: { gte: now },
          status: { in: ['SCHEDULED', 'CONFIRMED'] },
          ...appointmentFilter,
        },
      }),
      
      // Lab Results
      prisma.labResult.count({
        where: { 
          isDeleted: false
        },
      }),
      prisma.labResult.count({
        where: {
          isDeleted: false,
          status: { in: ['PENDING', 'IN_PROGRESS'] },
        },
      }),
      prisma.labResult.count({
        where: {
          isDeleted: false,
          flagged: true,
        },
      }),
      
      // Users (admin only)
      userRole === 'ADMIN' ? prisma.user.count() : 0,
      userRole === 'ADMIN' ? prisma.user.count({ where: { isActive: true } }) : 0,
    ]);

    const analytics = {
      patients: {
        total: Number(totalPatients),
        newThisWeek: Number(newPatientsThisWeek),
        newThisMonth: Number(newPatientsThisMonth),
        growthRate: totalPatients > 0 ? Number((Number(newPatientsThisMonth) / Number(totalPatients)) * 100) : 0,
      },
      appointments: {
        total: Number(totalAppointments),
        today: Number(todayAppointments),
        thisWeek: Number(thisWeekAppointments),
        upcoming: Number(upcomingAppointments),
      },
      labResults: {
        total: Number(totalLabResults),
        pending: Number(pendingLabResults),
        flagged: Number(flaggedLabResults),
        flaggedPercentage: totalLabResults > 0 ? Number((Number(flaggedLabResults) / Number(totalLabResults)) * 100) : 0,
      },
      system: userRole === 'ADMIN' ? {
        totalUsers: Number(totalUsers),
        activeUsers: Number(activeUsers),
        activePercentage: totalUsers > 0 ? Number((Number(activeUsers) / Number(totalUsers)) * 100) : 0,
      } : null,
    };

    return {
      success: true,
      data: { analytics },
    };
  }

  /**
   * Get patient analytics
   */
  static async getPatientAnalytics(
    dateRange: { from: string; to: string },
    userId: string,
    userRole: string
  ): Promise<ApiResponse<{ analytics: any }>> {
    const fromDate = new Date(dateRange.from);
    const toDate = new Date(dateRange.to);
    
    const patientFilter = userRole === 'CLINICIAN' ? { createdBy: userId } : {};

    const [
      // Demographics
      genderDistribution,
      ageGroups,
      
      // Registration trends
      registrationTrend,
      
      // Activity metrics
      patientsWithAppointments,
      patientsWithLabResults,
      
      // Geographic distribution (if address data available)
      stateDistribution,
    ] = await Promise.all([
      // Gender distribution
      prisma.patient.groupBy({
        by: ['gender'],
        where: {
          isDeleted: false,
          createdAt: { gte: fromDate, lte: toDate },
          ...patientFilter,
        },
        _count: { gender: true },
      }),
      
      // Age groups (calculated from date of birth)
      prisma.$queryRaw`
        SELECT
          CASE
            WHEN (strftime('%Y', 'now') - strftime('%Y', "dateOfBirth")) < 18 THEN 'Under 18'
            WHEN (strftime('%Y', 'now') - strftime('%Y', "dateOfBirth")) BETWEEN 18 AND 30 THEN '18-30'
            WHEN (strftime('%Y', 'now') - strftime('%Y', "dateOfBirth")) BETWEEN 31 AND 50 THEN '31-50'
            WHEN (strftime('%Y', 'now') - strftime('%Y', "dateOfBirth")) BETWEEN 51 AND 70 THEN '51-70'
            ELSE 'Over 70'
          END as age_group,
          COUNT(*) as count
        FROM "patients"
        WHERE "isDeleted" = false
          AND "createdAt" >= ${fromDate}
          AND "createdAt" <= ${toDate}
          ${userRole === 'CLINICIAN' ? prisma.$queryRaw`AND "createdBy" = ${userId}` : prisma.$queryRaw``}
        GROUP BY age_group
        ORDER BY age_group
      `,
      
      // Registration trend (daily)
      prisma.$queryRaw`
        SELECT
          DATE("createdAt") as date,
          COUNT(*) as count
        FROM "patients"
        WHERE "isDeleted" = false
          AND "createdAt" >= ${fromDate}
          AND "createdAt" <= ${toDate}
          ${userRole === 'CLINICIAN' ? prisma.$queryRaw`AND "createdBy" = ${userId}` : prisma.$queryRaw``}
        GROUP BY DATE("createdAt")
        ORDER BY date
      `,
      
      // Patients with appointments
      prisma.patient.count({
        where: {
          isDeleted: false,
          createdAt: { gte: fromDate, lte: toDate },
          appointments: { some: { 
            isDeleted: false
          }},
          ...patientFilter,
        },
      }),
      
      // Patients with lab results
      prisma.patient.count({
        where: {
          isDeleted: false,
          createdAt: { gte: fromDate, lte: toDate },
          labResults: { some: { 
            isDeleted: false
          }},
          ...patientFilter,
        },
      }),
      
      // State distribution
      prisma.patient.groupBy({
        by: ['address'],
        where: {
          isDeleted: false,
          createdAt: { gte: fromDate, lte: toDate },
          address: { not: null },
          ...patientFilter,
        },
        _count: { address: true },
      }),
    ]);

    const totalPatients = await prisma.patient.count({
      where: {
        isDeleted: false,
        createdAt: { gte: fromDate, lte: toDate },
        ...patientFilter,
      },
    });

    const analytics = {
      demographics: {
        gender: genderDistribution.reduce((acc, item) => {
          acc[item.gender] = Number(item._count.gender);
          return acc;
        }, {} as Record<string, number>),
        ageGroups: Array.isArray(ageGroups) ? ageGroups.map(group => ({
          age_group: String(group.age_group),
          count: Number(group.count)
        })) : [],
      },
      trends: {
        registration: Array.isArray(registrationTrend) ? registrationTrend.map(trend => ({
          date: String(trend.date),
          count: Number(trend.count)
        })) : [],
      },
      engagement: {
        totalPatients: Number(totalPatients),
        withAppointments: Number(patientsWithAppointments),
        withLabResults: Number(patientsWithLabResults),
        appointmentRate: totalPatients > 0 ? Number((Number(patientsWithAppointments) / Number(totalPatients)) * 100) : 0,
        labResultRate: totalPatients > 0 ? Number((Number(patientsWithLabResults) / Number(totalPatients)) * 100) : 0,
      },
      geographic: {
        states: stateDistribution.slice(0, 10).map(state => ({
          address: String(state.address),
          _count: { address: Number(state._count.address) }
        })), // Top 10 states
      },
    };

    return {
      success: true,
      data: { analytics },
    };
  }

  /**
   * Get appointment analytics
   */
  static async getAppointmentAnalytics(
    dateRange: { from: string; to: string },
    userId: string,
    userRole: string
  ): Promise<ApiResponse<{ analytics: any }>> {
    const fromDate = new Date(dateRange.from);
    const toDate = new Date(dateRange.to);
    
    const appointmentFilter = userRole === 'CLINICIAN' ? { providerId: userId } : {};

    const [
      // Status distribution
      statusDistribution,
      
      // Type distribution
      typeDistribution,
      
      // Daily appointment trend
      dailyTrend,
      
      // Provider performance (admin only)
      providerStats,
      
      // Duration analysis
      durationStats,
      
      // Cancellation analysis
      cancellationStats,
    ] = await Promise.all([
      // Status distribution
      prisma.appointment.groupBy({
        by: ['status'],
        where: {
          isDeleted: false,
          date: { gte: fromDate, lte: toDate },
          ...appointmentFilter,
        },
        _count: { status: true },
      }),
      
      // Type distribution
      prisma.appointment.groupBy({
        by: ['type'],
        where: {
          isDeleted: false,
          date: { gte: fromDate, lte: toDate },
          ...appointmentFilter,
        },
        _count: { type: true },
      }),
      
      // Daily trend
      prisma.$queryRaw`
        SELECT
          DATE("date") as day,
          COUNT(*) as count
        FROM "appointments"
        WHERE "isDeleted" = false
          AND "date" >= ${fromDate}
          AND "date" <= ${toDate}
          ${userRole === 'CLINICIAN' ? prisma.$queryRaw`AND "providerId" = ${userId}` : prisma.$queryRaw``}
        GROUP BY DATE("date")
        ORDER BY day
      `,
      
      // Provider performance (admin only)
      userRole === 'ADMIN' ? prisma.$queryRaw`
        SELECT
          u."firstName" as first_name,
          u."lastName" as last_name,
          COUNT(a.id) as total_appointments,
          COUNT(CASE WHEN a.status = 'COMPLETED' THEN 1 END) as completed,
          COUNT(CASE WHEN a.status = 'CANCELLED' THEN 1 END) as cancelled,
          AVG(a.duration) as avg_duration
        FROM "users" u
        LEFT JOIN "appointments" a ON u.id = a."providerId"
          AND a."isDeleted" = false
          AND a."date" >= ${fromDate}
          AND a."date" <= ${toDate}
        WHERE u.role IN ('ADMIN', 'CLINICIAN')
          AND u."isActive" = true
        GROUP BY u.id, u."firstName", u."lastName"
        ORDER BY total_appointments DESC
      ` : [],
      
      // Duration statistics
      prisma.appointment.aggregate({
        where: {
          isDeleted: false,
          date: { gte: fromDate, lte: toDate },
          status: 'COMPLETED',
          ...appointmentFilter,
        },
        _avg: { duration: true },
        _min: { duration: true },
        _max: { duration: true },
      }),
      
      // Cancellation analysis
      prisma.$queryRaw`
        SELECT
          CAST(strftime('%w', "date") AS INTEGER) as day_of_week,
          COUNT(*) as total,
          COUNT(CASE WHEN status = 'CANCELLED' THEN 1 END) as cancelled
        FROM "appointments"
        WHERE "isDeleted" = false
          AND "date" >= ${fromDate}
          AND "date" <= ${toDate}
          ${userRole === 'CLINICIAN' ? prisma.$queryRaw`AND "providerId" = ${userId}` : prisma.$queryRaw``}
        GROUP BY CAST(strftime('%w', "date") AS INTEGER)
        ORDER BY day_of_week
      `,
    ]);

    const totalAppointments = await prisma.appointment.count({
      where: {
        isDeleted: false,
        date: { gte: fromDate, lte: toDate },
        ...appointmentFilter,
      },
    });

    const analytics = {
      overview: {
        total: totalAppointments,
        statusDistribution: statusDistribution.reduce((acc, item) => {
          acc[item.status] = item._count.status;
          return acc;
        }, {} as Record<string, number>),
        typeDistribution: typeDistribution.reduce((acc, item) => {
          acc[item.type] = item._count.type;
          return acc;
        }, {} as Record<string, number>),
      },
      trends: {
        daily: dailyTrend,
      },
      performance: {
        providers: userRole === 'ADMIN' ? providerStats : null,
        duration: {
          avg_minutes: Math.round(durationStats._avg.duration || 0),
          min_minutes: durationStats._min.duration || 0,
          max_minutes: durationStats._max.duration || 0
        },
        cancellationByDay: cancellationStats,
      },
    };

    return {
      success: true,
      data: { analytics },
    };
  }

  /**
   * Get lab result analytics
   */
  static async getLabResultAnalytics(
    dateRange: { from: string; to: string },
    userId: string,
    userRole: string
  ): Promise<ApiResponse<{ analytics: any }>> {
    const fromDate = new Date(dateRange.from);
    const toDate = new Date(dateRange.to);

    const [
      // Test type distribution
      testTypeDistribution,
      
      // Status distribution
      statusDistribution,
      
      // Flag analysis
      flaggedResults,
      
      // Turnaround time analysis
      turnaroundStats,
      
      // Monthly trend
      monthlyTrend,
    ] = await Promise.all([
      // Test type distribution
      prisma.labResult.groupBy({
        by: ['testType'],
        where: {
          isDeleted: false,
          testDate: { gte: fromDate, lte: toDate },
        },
        _count: { testType: true },
      }),

      // Status distribution
      prisma.labResult.groupBy({
        by: ['status'],
        where: {
          isDeleted: false,
          testDate: { gte: fromDate, lte: toDate },
        },
        _count: { status: true },
      }),
      
      // Flagged results analysis
      prisma.$queryRaw`
        SELECT
          "testType" as test_type,
          COUNT(*) as total,
          COUNT(CASE WHEN flags IS NOT NULL AND flags != '{}' THEN 1 END) as flagged
        FROM "lab_results"
        WHERE "isDeleted" = false
          AND "testDate" >= ${fromDate}
          AND "testDate" <= ${toDate}
        GROUP BY "testType"
        ORDER BY flagged DESC
      `,
      
      // Turnaround time (test date to creation)
      prisma.$queryRaw`
        SELECT
          AVG((julianday("createdAt") - julianday("testDate")) * 24) as avg_hours,
          MIN((julianday("createdAt") - julianday("testDate")) * 24) as min_hours,
          MAX((julianday("createdAt") - julianday("testDate")) * 24) as max_hours
        FROM "lab_results"
        WHERE "isDeleted" = false
          AND "testDate" >= ${fromDate}
          AND "testDate" <= ${toDate}
          AND status = 'COMPLETED'
      `,
      
      // Monthly trend
      prisma.$queryRaw`
        SELECT
          strftime('%Y-%m-01', "testDate") as month,
          COUNT(*) as total,
          COUNT(CASE WHEN flags IS NOT NULL AND flags != '{}' THEN 1 END) as flagged
        FROM "lab_results"
        WHERE "isDeleted" = false
          AND "testDate" >= ${fromDate}
          AND "testDate" <= ${toDate}
        GROUP BY strftime('%Y-%m-01', "testDate")
        ORDER BY month
      `,
    ]);

    const totalLabResults = await prisma.labResult.count({
      where: {
        isDeleted: false,
        testDate: { gte: fromDate, lte: toDate },
      },
    });

    const analytics = {
      overview: {
        total: totalLabResults,
        testTypeDistribution: testTypeDistribution.reduce((acc, item) => {
          acc[item.testType] = item._count.testType;
          return acc;
        }, {} as Record<string, number>),
        statusDistribution: statusDistribution.reduce((acc, item) => {
          acc[item.status] = item._count.status;
          return acc;
        }, {} as Record<string, number>),
      },
      quality: {
        flaggedByTestType: flaggedResults,
        turnaroundTime: turnaroundStats[0] || { avg_hours: 0, min_hours: 0, max_hours: 0 },
      },
      trends: {
        monthly: monthlyTrend,
      },
    };

    return {
      success: true,
      data: { analytics },
    };
  }

  /**
   * Get system analytics (admin only)
   */
  static async getSystemAnalytics(
    dateRange: { from: string; to: string },
    _userId: string,
    userRole: string
  ): Promise<ApiResponse<{ analytics: any }>> {
    if (userRole !== 'ADMIN') {
      return {
        success: false,
        error: 'Access denied. Admin role required.',
      };
    }

    const fromDate = new Date(dateRange.from);
    const toDate = new Date(dateRange.to);

    const [
      // User activity
      userActivity,
      
      // Audit log summary
      auditSummary,
      
      // System usage trends
      usageTrends,
      
      // Error analysis
      errorAnalysis,
    ] = await Promise.all([
      // User activity
      prisma.$queryRaw`
        SELECT
          u.role,
          COUNT(DISTINCT u.id) as total_users,
          COUNT(DISTINCT CASE WHEN u."lastLogin" >= ${subDays(new Date(), 7)} THEN u.id END) as active_last_7_days,
          COUNT(DISTINCT CASE WHEN u."lastLogin" >= ${subDays(new Date(), 30)} THEN u.id END) as active_last_30_days
        FROM "users" u
        WHERE u."isActive" = true
        GROUP BY u.role
      `,
      
      // Audit log summary
      prisma.auditLog.groupBy({
        by: ['action', 'entityType'],
        where: {
          timestamp: { gte: fromDate, lte: toDate },
        },
        _count: { action: true },
      }),
      
      // Daily usage trends
      prisma.$queryRaw`
        SELECT 
          DATE("timestamp") as date,
          "entityType",
          "action",
          COUNT(*) as count
        FROM "audit_logs" 
        WHERE "timestamp" >= ${fromDate}
          AND "timestamp" <= ${toDate}
        GROUP BY DATE("timestamp"), "entityType", "action"
        ORDER BY date
      `,
      
      // Error analysis (placeholder - would integrate with error logging)
      [],
    ]);

    const analytics = {
      users: {
        activity: userActivity,
      },
      audit: {
        summary: auditSummary.reduce((acc, item) => {
          const key = `${item.entityType}_${item.action}`;
          acc[key] = item._count.action;
          return acc;
        }, {} as Record<string, number>),
      },
      usage: {
        trends: usageTrends,
      },
      errors: {
        analysis: errorAnalysis,
      },
    };

    return {
      success: true,
      data: { analytics },
    };
  }
}
