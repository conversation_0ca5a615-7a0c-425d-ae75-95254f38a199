const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkProviders() {
  try {
    const providers = await prisma.user.findMany({
      where: {
        role: { in: ['ADMIN', 'CLINICIAN'] },
        isActive: true
      }
    });
    console.log('Current providers:', providers.length);
    providers.forEach(p => console.log(`- ${p.firstName} ${p.lastName} (${p.role}) - ID: ${p.id}`));
    
    if (providers.length === 0) {
      console.log('No providers found. Creating sample providers...');
      
      const bcrypt = require('bcrypt');
      const hashedPassword = await bcrypt.hash('password123', 10);
      
      const provider1 = await prisma.user.create({
        data: {
          username: 'dr.smith',
          email: '<EMAIL>',
          password: hashedPassword,
          firstName: '<PERSON>',
          lastName: '<PERSON>',
          role: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
          isActive: true
        }
      });
      
      const provider2 = await prisma.user.create({
        data: {
          username: 'dr.johnson',
          email: '<EMAIL>',
          password: hashedPassword,
          firstName: 'Sarah',
          lastName: 'Johnson',
          role: 'ADMIN',
          isActive: true
        }
      });
      
      console.log('Created providers:');
      console.log(`- Dr. ${provider1.firstName} ${provider1.lastName} (${provider1.role}) - ID: ${provider1.id}`);
      console.log(`- Dr. ${provider2.firstName} ${provider2.lastName} (${provider2.role}) - ID: ${provider2.id}`);
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkProviders();
