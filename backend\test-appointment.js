const axios = require('axios');

async function testAppointmentCreation() {
  try {
    const appointmentData = {
      patientId: '285cead6-a4ee-4a25-84f3-5afe1c1d9643',
      providerId: '99a887f3-1e98-4a6e-978a-1157c95af3f1',
      date: '2025-07-20T10:00:00Z',
      duration: 60,
      type: 'CONSULTATION'
    };

    console.log('Testing appointment creation with data:', appointmentData);

    const response = await axios.post('http://localhost:3002/api/appointments', appointmentData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('Success! Response:', response.data);
  } catch (error) {
    console.error('Error creating appointment:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Message:', error.message);
    }
  }
}

testAppointmentCreation();
