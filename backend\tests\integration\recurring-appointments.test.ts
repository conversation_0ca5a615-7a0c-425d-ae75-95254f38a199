import { RecurringAppointmentService } from '@/services/recurringAppointmentService';
import { createTestUser, createTestPatient, prisma } from '../setup';
import { addDays, addWeeks, addMonths } from 'date-fns';

describe('Recurring Appointments Integration Tests', () => {
  let testUser: any;
  let testPatient: any;

  beforeEach(async () => {
    testUser = await createTestUser('CLINICIAN');
    testPatient = await createTestPatient(testUser.id);
  });

  describe('Recurring Appointment Creation', () => {
    test('should create weekly recurring appointments correctly', async () => {
      const startDate = new Date();
      startDate.setHours(10, 0, 0, 0); // 10:00 AM
      
      const recurringData = {
        patientId: testPatient.id,
        providerId: testUser.id,
        startDate: startDate,
        endDate: addWeeks(startDate, 4), // 4 weeks from start
        duration: 60,
        type: 'THERAPY_SESSION',
        frequency: 'WEEKLY',
        interval: 1,
        dayOfWeek: startDate.getDay(), // Same day of week
        timeSlot: '10:00',
        notes: 'Weekly therapy sessions',
        maxOccurrences: 4
      };

      const result = await RecurringAppointmentService.createRecurringAppointment(
        recurringData,
        testUser.id,
        { ipAddress: '127.0.0.1', userAgent: 'test-agent' }
      );

      expect(result.success).toBe(true);
      expect(result.data.recurringAppointment.frequency).toBe('WEEKLY');
      expect(result.data.recurringAppointment.maxOccurrences).toBe(4);

      // Verify individual appointments were created
      const appointments = await prisma.appointment.findMany({
        where: {
          recurringAppointmentId: result.data.recurringAppointment.id
        },
        orderBy: { date: 'asc' }
      });

      expect(appointments.length).toBe(4);
      
      // Verify appointments are scheduled weekly
      for (let i = 1; i < appointments.length; i++) {
        const prevDate = new Date(appointments[i - 1].date);
        const currentDate = new Date(appointments[i].date);
        const daysDiff = Math.round((currentDate.getTime() - prevDate.getTime()) / (1000 * 60 * 60 * 24));
        expect(daysDiff).toBe(7); // Should be exactly 7 days apart
      }
    });

    test('should create monthly recurring appointments on specific day of month', async () => {
      const startDate = new Date();
      startDate.setDate(15); // 15th of the month
      startDate.setHours(14, 30, 0, 0); // 2:30 PM
      
      const recurringData = {
        patientId: testPatient.id,
        providerId: testUser.id,
        startDate: startDate,
        duration: 45,
        type: 'MEDICATION_REVIEW',
        frequency: 'MONTHLY',
        interval: 1,
        dayOfMonth: 15,
        timeSlot: '14:30',
        notes: 'Monthly medication review',
        maxOccurrences: 3
      };

      const result = await RecurringAppointmentService.createRecurringAppointment(
        recurringData,
        testUser.id
      );

      expect(result.success).toBe(true);

      // Verify appointments are created on the 15th of each month
      const appointments = await prisma.appointment.findMany({
        where: {
          recurringAppointmentId: result.data.recurringAppointment.id
        },
        orderBy: { date: 'asc' }
      });

      expect(appointments.length).toBe(3);
      
      appointments.forEach(appointment => {
        const appointmentDate = new Date(appointment.date);
        expect(appointmentDate.getDate()).toBe(15);
      });
    });

    test('should handle biweekly recurring appointments', async () => {
      const startDate = new Date();
      startDate.setHours(9, 0, 0, 0);
      
      const recurringData = {
        patientId: testPatient.id,
        providerId: testUser.id,
        startDate: startDate,
        duration: 90,
        type: 'PSYCHOLOGICAL_TESTING',
        frequency: 'BIWEEKLY',
        interval: 1,
        dayOfWeek: startDate.getDay(),
        timeSlot: '09:00',
        notes: 'Biweekly psychological assessment',
        maxOccurrences: 3
      };

      const result = await RecurringAppointmentService.createRecurringAppointment(
        recurringData,
        testUser.id
      );

      expect(result.success).toBe(true);

      const appointments = await prisma.appointment.findMany({
        where: {
          recurringAppointmentId: result.data.recurringAppointment.id
        },
        orderBy: { date: 'asc' }
      });

      expect(appointments.length).toBe(3);
      
      // Verify appointments are scheduled every 2 weeks
      for (let i = 1; i < appointments.length; i++) {
        const prevDate = new Date(appointments[i - 1].date);
        const currentDate = new Date(appointments[i].date);
        const daysDiff = Math.round((currentDate.getTime() - prevDate.getTime()) / (1000 * 60 * 60 * 24));
        expect(daysDiff).toBe(14); // Should be exactly 14 days apart
      }
    });

    test('should validate recurring appointment data', async () => {
      // Test missing required fields
      const invalidData = {
        patientId: testPatient.id,
        // Missing providerId
        startDate: new Date(),
        frequency: 'WEEKLY'
      };

      await expect(
        RecurringAppointmentService.createRecurringAppointment(
          invalidData as any,
          testUser.id
        )
      ).rejects.toThrow('Patient ID, provider ID, start date, and frequency are required');

      // Test invalid frequency
      const invalidFrequencyData = {
        patientId: testPatient.id,
        providerId: testUser.id,
        startDate: new Date(),
        frequency: 'INVALID_FREQUENCY'
      };

      await expect(
        RecurringAppointmentService.createRecurringAppointment(
          invalidFrequencyData as any,
          testUser.id
        )
      ).rejects.toThrow('Invalid recurrence frequency');

      // Test past start date
      const pastDateData = {
        patientId: testPatient.id,
        providerId: testUser.id,
        startDate: new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
        frequency: 'WEEKLY'
      };

      await expect(
        RecurringAppointmentService.createRecurringAppointment(
          pastDateData,
          testUser.id
        )
      ).rejects.toThrow('Invalid start date or date is in the past');
    });
  });

  describe('Recurring Appointment Management', () => {
    let recurringAppointment: any;

    beforeEach(async () => {
      const recurringData = {
        patientId: testPatient.id,
        providerId: testUser.id,
        startDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
        duration: 60,
        type: 'FOLLOW_UP',
        frequency: 'WEEKLY',
        interval: 1,
        notes: 'Test recurring appointment',
        maxOccurrences: 5
      };

      const result = await RecurringAppointmentService.createRecurringAppointment(
        recurringData,
        testUser.id
      );

      recurringAppointment = result.data.recurringAppointment;
    });

    test('should cancel recurring appointment series', async () => {
      const result = await RecurringAppointmentService.cancelRecurringAppointment(
        recurringAppointment.id,
        'Patient requested cancellation',
        true,
        testUser.id,
        testUser.role
      );

      expect(result.success).toBe(true);

      // Verify recurring appointment is marked as inactive
      const updatedRecurring = await prisma.recurringAppointment.findUnique({
        where: { id: recurringAppointment.id }
      });

      expect(updatedRecurring?.isActive).toBe(false);

      // Verify future appointments are cancelled
      const futureAppointments = await prisma.appointment.findMany({
        where: {
          recurringAppointmentId: recurringAppointment.id,
          date: { gte: new Date() }
        }
      });

      futureAppointments.forEach(appointment => {
        expect(appointment.status).toBe('CANCELLED');
      });
    });

    test('should delete recurring appointment', async () => {
      const result = await RecurringAppointmentService.deleteRecurringAppointment(
        recurringAppointment.id,
        testUser.id
      );

      expect(result.success).toBe(true);

      // Verify recurring appointment is soft deleted
      const deletedRecurring = await prisma.recurringAppointment.findUnique({
        where: { id: recurringAppointment.id }
      });

      expect(deletedRecurring?.isDeleted).toBe(true);
      expect(deletedRecurring?.isActive).toBe(false);
    });
  });
});
