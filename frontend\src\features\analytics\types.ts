// Analytics feature types
export interface DateRange {
  from: string;
  to: string;
}

export interface DashboardAnalytics {
  patients: {
    total: number;
    recent: number;
  };
  appointments: {
    total: number;
    upcoming: number;
  };
  labResults: {
    total: number;
    recent: number;
  };
  system: {
    totalUsers: number;
  };
}

export interface PatientAnalytics {
  demographics: {
    gender: Record<string, number>;
    ageGroups: Array<{
      ageGroup: string;
      count: number;
    }>;
  };
  trends: {
    registration: Array<{
      date: string;
      count: number;
    }>;
  };
  engagement: {
    totalPatients: number;
    withAppointments: number;
    withLabResults: number;
    appointmentRate: number;
    labResultRate: number;
  };
  geographic: {
    states: Array<{
      state: string;
      count: number;
    }>;
  };
}

export interface AppointmentAnalytics {
  overview: {
    total: number;
    statusDistribution: Record<string, number>;
    typeDistribution: Record<string, number>;
  };
  trends: {
    daily: Array<{
      date: string;
      count: number;
    }>;
  };
  performance: {
    providers?: Array<{
      providerId: string;
      providerName: string;
      totalAppointments: number;
      completedAppointments: number;
      cancelledAppointments: number;
      completionRate: number;
    }>;
    duration: {
      avg_minutes: number;
      min_minutes: number;
      max_minutes: number;
    };
    cancellationByDay: Array<{
      dayOfWeek: string;
      cancellations: number;
    }>;
  };
}

export interface LabResultAnalytics {
  overview: {
    total: number;
    testTypeDistribution: Record<string, number>;
    statusDistribution: Record<string, number>;
  };
  quality: {
    flaggedByTestType: Array<{
      testType: string;
      flagged: number;
      total: number;
      percentage: number;
    }>;
    turnaroundTime: {
      avg_hours: number;
      min_hours: number;
      max_hours: number;
    };
  };
  trends: {
    monthly: Array<{
      month: string;
      count: number;
    }>;
  };
}

export interface SystemAnalytics {
  users: {
    activity: Array<{
      role: string;
      total_users: number;
      active_last_7_days: number;
      active_last_30_days: number;
    }>;
  };
  audit: {
    summary: Record<string, number>;
  };
  usage: {
    trends: Array<{
      date: string;
      entity_type: string;
      action: string;
      count: number;
    }>;
  };
  errors: {
    analysis: any[];
  };
}

export interface ChartData {
  name: string;
  value: number;
  color?: string;
}

export interface TrendData {
  date: string;
  value: number;
}

export interface AnalyticsCard {
  title: string;
  value: number | string;
  change?: number;
  trend?: 'up' | 'down' | 'stable';
  icon: React.ComponentType<any>;
  color: string;
}
