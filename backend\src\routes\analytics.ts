import express from 'express';
import { AnalyticsController } from '../controllers/analyticsController';
import { authorize, authenticate } from '../middleware/auth';
import { asyncHandler } from '../utils/asyncHandler';

/**
 * Analytics routes
 * Base path: /api/analytics
 */

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Analytics
 *   description: Analytics and reporting
 */

/**
 * All analytics routes require authentication.
 */
router.use(authenticate);

/**
 * @swagger
 * /analytics/dashboard:
 *   get:
 *     summary: Get dashboard analytics overview
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard analytics data
 */
router.get('/dashboard', asyncHandler(AnalyticsController.getDashboardAnalytics));

/**
 * @swagger
 * /analytics/patients:
 *   get:
 *     summary: Get patient analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: from
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for analytics period
 *       - in: query
 *         name: to
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for analytics period
 *     responses:
 *       200:
 *         description: Patient analytics data
 */
router.get('/patients', asyncHandler(AnalyticsController.getPatientAnalytics));

/**
 * @swagger
 * /analytics/appointments:
 *   get:
 *     summary: Get appointment analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: from
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for analytics period
 *       - in: query
 *         name: to
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for analytics period
 *     responses:
 *       200:
 *         description: Appointment analytics data
 */
router.get('/appointments', asyncHandler(AnalyticsController.getAppointmentAnalytics));

/**
 * @swagger
 * /analytics/lab-results:
 *   get:
 *     summary: Get lab result analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: from
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for analytics period
 *       - in: query
 *         name: to
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for analytics period
 *     responses:
 *       200:
 *         description: Lab result analytics data
 */
router.get('/lab-results', asyncHandler(AnalyticsController.getLabResultAnalytics));

/**
 * @swagger
 * /analytics/system:
 *   get:
 *     summary: Get system analytics (admin only)
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: from
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for analytics period
 *       - in: query
 *         name: to
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for analytics period
 *     responses:
 *       200:
 *         description: System analytics data
 */
router.get('/system', authorize(['ADMIN']), asyncHandler(AnalyticsController.getSystemAnalytics));

export default router;
