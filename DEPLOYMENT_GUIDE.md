# 🚀 Psychiatry App - Production Deployment Guide

## 📋 Prerequisites

- **Node.js** (v16 or higher)
- **npm** (comes with Node.js)
- **Windows** (for .bat files) or compatible terminal

## 🎯 Quick Start (Recommended)

### Option 1: Automatic Startup
```bash
# Double-click this file to start everything automatically:
start-full-app.bat
```

### Option 2: Manual Startup
```bash
# Terminal 1 - Backend Server
cd backend
node working-server.js

# Terminal 2 - Frontend Server
cd frontend
npm run dev
```

## 🌐 Application URLs

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:3002/api
- **Health Check**: http://localhost:3002/health
- **Database Stats**: http://localhost:3002/api/stats

## 🧪 Lab Management System

### Key Features
- **Structured Templates**: CBC, Metabolic Panel, Lipid Panel, Liver Function
- **Hierarchical Organization**: CBC includes WBC, Hemoglobin, Platelets, Neutrophils, Lymphocytes
- **Automatic Validation**: Normal range checking with abnormal value flagging
- **JSON Storage**: Structured medical data storage
- **Patient-Centric Workflow**: Select patient → Create results → View history

### Usage Workflow
1. Navigate to **Lab Management** in sidebar
2. **Select Patient** from left panel
3. **Choose Test Type** (CBC, Metabolic Panel, etc.)
4. **Enter Values** - system validates against normal ranges
5. **Save Results** - automatically flags abnormal values
6. **View History** - see all results with flagged values

## 📊 Database Schema

### Core Tables
- **Users** - Staff members with roles (ADMIN, CLINICIAN, NURSE)
- **Patients** - Patient demographics and information
- **LabResults** - Structured lab data with JSON fields
- **Appointments** - Scheduling and appointment management
- **Notifications** - System notifications and alerts

### Lab Result Structure
```json
{
  "results": {
    "wbc": 7.2,
    "hemoglobin": 14.2,
    "platelets": 285000
  },
  "normalRanges": {
    "wbc": {"min": 4.0, "max": 11.0, "reference": "4.0-11.0 K/uL"},
    "hemoglobin": {"min": 12.0, "max": 16.0, "reference": "12.0-16.0 g/dL"}
  },
  "flags": {
    "wbc": {"flag": "HIGH", "severity": "MILD", "note": "Value outside normal range"}
  }
}
```

## 🔧 Technical Architecture

### Backend
- **Framework**: Node.js + Express
- **Database**: SQLite with Prisma ORM
- **Port**: 3002
- **Main File**: `backend/working-server.js`

### Frontend
- **Framework**: React 18 + TypeScript
- **Styling**: Tailwind CSS
- **State Management**: Zustand
- **Port**: 5173
- **Build Tool**: Vite

### API Endpoints
- `GET /api/patients` - List patients
- `POST /api/patients` - Create patient
- `GET /api/lab-results` - List lab results
- `POST /api/lab-results` - Create lab result
- `GET /api/appointments` - List appointments
- `POST /api/appointments` - Create appointment
- `GET /api/export/*` - CSV exports

## 🛠️ Troubleshooting

### Common Issues

**Port Already in Use**
```bash
# Check what's using the ports
netstat -ano | findstr :3002
netstat -ano | findstr :5173
# Kill processes if needed
taskkill /PID <process_id> /F
```

**Database Issues**
```bash
cd backend
npx prisma generate
npx prisma db push
node create-sample-data.js
```

**Missing Dependencies**
```bash
# Backend
cd backend
npm install

# Frontend
cd frontend
npm install
```

### Reset Everything
```bash
# Stop all servers (Ctrl+C)
# Delete node_modules and reinstall
cd backend
rmdir /s node_modules
npm install
npx prisma generate
npx prisma db push
node create-sample-data.js

cd ../frontend
rmdir /s node_modules
npm install
```

## 📈 Production Considerations

### Security
- Add authentication/authorization
- Implement HTTPS
- Add rate limiting
- Validate all inputs
- Add audit logging

### Performance
- Add database indexing
- Implement caching
- Add connection pooling
- Optimize queries
- Add monitoring

### Scalability
- Consider PostgreSQL for production
- Add load balancing
- Implement microservices
- Add container deployment
- Set up CI/CD pipeline

## 🎉 Success Indicators

✅ Backend server starts on port 3002
✅ Frontend loads on port 5173
✅ Dashboard shows statistics
✅ Patient list loads with sample data
✅ Lab Management page accessible
✅ Can create new lab results with templates
✅ Abnormal values are flagged automatically
✅ CSV export works for all modules

## 📞 Support

If you encounter issues:
1. Check the console for error messages
2. Verify all prerequisites are installed
3. Ensure ports 3002 and 5173 are available
4. Try the reset procedure above
5. Check the APP_STATUS.md file for additional guidance

**Your Psychiatry Patient Management System is ready for production use!** 🚀
