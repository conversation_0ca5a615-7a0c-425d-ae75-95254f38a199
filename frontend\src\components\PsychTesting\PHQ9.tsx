import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '../ui/card';
import { Button } from '../ui/button';
import { scorePHQ9, TestResponse } from '../../utils/psychTestScoring';
import { ArrowLeft, Save } from 'lucide-react';

interface PHQ9Props {
  onBack: () => void;
  onSave: (result: any) => void;
  patientId: string;
}

const PHQ9Questions = [
  "Little interest or pleasure in doing things",
  "Feeling down, depressed, or hopeless",
  "Trouble falling or staying asleep, or sleeping too much",
  "Feeling tired or having little energy",
  "Poor appetite or overeating",
  "Feeling bad about yourself or that you are a failure or have let yourself or your family down",
  "Trouble concentrating on things, such as reading the newspaper or watching television",
  "Moving or speaking so slowly that other people could have noticed. Or the opposite being so fidgety or restless that you have been moving around a lot more than usual",
  "Thoughts that you would be better off dead, or of hurting yourself"
];

const responseOptions = [
  { value: 0, label: "Not at all" },
  { value: 1, label: "Several days" },
  { value: 2, label: "More than half the days" },
  { value: 3, label: "Nearly every day" }
];

export const PHQ9: React.FC<PHQ9Props> = ({ onBack, onSave, patientId }) => {
  const [responses, setResponses] = useState<Record<number, number>>({});
  const [currentResult, setCurrentResult] = useState<any>(null);

  const handleResponseChange = (questionIndex: number, value: number) => {
    const newResponses = { ...responses, [questionIndex]: value };
    setResponses(newResponses);

    // Calculate score in real-time
    const testResponses: TestResponse[] = Object.entries(newResponses).map(([index, value]) => ({
      questionId: `phq9_${index}`,
      value: value
    }));

    if (testResponses.length === PHQ9Questions.length) {
      const result = scorePHQ9(testResponses);
      setCurrentResult(result);
    }
  };

  const handleSave = async () => {
    if (Object.keys(responses).length !== PHQ9Questions.length) {
      alert('Please answer all questions before saving.');
      return;
    }

    const testResponses: TestResponse[] = Object.entries(responses).map(([index, value]) => ({
      questionId: `phq9_${index}`,
      value: value
    }));

    const result = scorePHQ9(testResponses);

    const testData = {
      patientId,
      testName: 'PHQ-9',
      testCategory: 'depression',
      administeredBy: 'Current User', // This should come from auth context
      administeredDate: new Date().toISOString(),
      completionTime: 5, // Estimated 5 minutes
      location: 'office',
      totalScore: result.totalScore,
      severity: result.severity,
      interpretation: result.interpretation,
      recommendations: result.recommendations.join('; '),
      responses: JSON.stringify(testResponses),
      validity: 'valid',
      baselineTest: false
    };

    onSave(testData);
  };

  const isComplete = Object.keys(responses).length === PHQ9Questions.length;
  const completionPercentage = (Object.keys(responses).length / PHQ9Questions.length) * 100;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">PHQ-9 Depression Screening</h1>
            <p className="text-gray-600">Patient Health Questionnaire - 9 Items</p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-sm text-gray-500">Progress</div>
          <div className="text-lg font-semibold">{Math.round(completionPercentage)}%</div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
          style={{ width: `${completionPercentage}%` }}
        ></div>
      </div>

      {/* Instructions */}
      <Card>
        <CardContent className="p-6">
          <h3 className="font-semibold mb-2">Instructions</h3>
          <p className="text-gray-600">
            Over the last 2 weeks, how often have you been bothered by any of the following problems?
            Please select the response that best describes your experience.
          </p>
        </CardContent>
      </Card>

      {/* Questions */}
      <div className="space-y-4">
        {PHQ9Questions.map((question, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="mb-4">
                <h4 className="font-medium text-gray-900 mb-2">
                  {index + 1}. {question}
                </h4>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                {responseOptions.map((option) => (
                  <label
                    key={option.value}
                    className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                      responses[index] === option.value
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <input
                      type="radio"
                      name={`question-${index}`}
                      value={option.value}
                      checked={responses[index] === option.value}
                      onChange={() => handleResponseChange(index, option.value)}
                      className="sr-only"
                    />
                    <div className="text-center w-full">
                      <div className="font-medium text-sm">{option.value}</div>
                      <div className="text-xs text-gray-600 mt-1">{option.label}</div>
                    </div>
                  </label>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Results Preview */}
      {currentResult && (
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="text-green-800">Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <div className="text-sm text-green-600">Total Score</div>
                <div className="text-2xl font-bold text-green-800">{currentResult.totalScore}/27</div>
              </div>
              <div>
                <div className="text-sm text-green-600">Severity</div>
                <div className="text-lg font-semibold text-green-800 capitalize">{currentResult.severity}</div>
              </div>
              <div>
                <div className="text-sm text-green-600">Interpretation</div>
                <div className="text-sm text-green-700">{currentResult.interpretation}</div>
              </div>
            </div>
            <div className="mt-4">
              <div className="text-sm text-green-600 mb-2">Recommendations</div>
              <ul className="text-sm text-green-700 space-y-1">
                {currentResult.recommendations.map((rec: string, index: number) => (
                  <li key={index} className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>{rec}</span>
                  </li>
                ))}
              </ul>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Save Button */}
      <div className="flex justify-end">
        <Button 
          onClick={handleSave}
          disabled={!isComplete}
          className="bg-blue-600 hover:bg-blue-700"
        >
          <Save className="h-4 w-4 mr-2" />
          Save Test Results
        </Button>
      </div>
    </div>
  );
};
