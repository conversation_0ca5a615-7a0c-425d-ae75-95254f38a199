import React, { useState } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import type { PatientFormData } from '../types';
import { usePatient } from '../hooks/usePatient';
import PatientForm from './PatientForm';
import { useToast } from '../../../components/ui/Toast';
import LoadingSpinner from '../../../components/ui/LoadingSpinner';

const EditPatientPage: React.FC = () => {
  const { patientId } = useParams<{ patientId: string }>();
  const navigate = useNavigate();
  const { patient, loading: patientLoading, error: patientError, updatePatient, isUpdating } = usePatient(patientId!);
  const { success, error: showError } = useToast();
  const [error, setError] = useState<string | null>(null);

  if (patientLoading) {
    return <LoadingSpinner />;
  }

  if (patientError || !patient) {
    return (
      <div className="max-w-2xl mx-auto space-y-6">
        <div className="text-center py-8">
          <p className="text-red-600 mb-4">
            {patientError || 'Patient not found'}
          </p>
          <Link
            to="/patients"
            className="text-blue-600 hover:text-blue-800"
          >
            ← Back to Patients
          </Link>
        </div>
      </div>
    );
  }

  const handleSubmit = async (formData: PatientFormData) => {
    setError(null);

    try {
      await updatePatient(formData);
      success('Patient Updated', `${formData.firstName} ${formData.lastName} has been successfully updated.`);
      navigate(`/patients/${patientId}`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update patient';
      setError(errorMessage);
      showError('Failed to Update Patient', errorMessage);
    }
  };

  // Convert patient data to form data format
  const initialData: PatientFormData = {
    firstName: patient.firstName,
    lastName: patient.lastName,
    dateOfBirth: patient.dateOfBirth ? new Date(patient.dateOfBirth).toISOString().split('T')[0] : '',
    gender: patient.gender,
    phone: patient.phone || '',
    email: patient.email || '',
    address: typeof patient.address === 'string' 
      ? JSON.parse(patient.address || '{}')
      : patient.address || {
          street: '',
          city: '',
          state: '',
          zipCode: '',
          country: '',
        },
  };

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            to={`/patients/${patientId}`}
            className="text-gray-600 hover:text-gray-800 flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Patient Details
          </Link>
        </div>
      </div>

      <div>
        <h1 className="text-3xl font-bold text-gray-900">Edit Patient</h1>
        <p className="text-gray-600">Patient ID: {patient.patientId}</p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <PatientForm
          initialData={initialData}
          onSubmit={handleSubmit}
          loading={isUpdating}
          submitButtonText={isUpdating ? 'Updating...' : 'Update Patient'}
        />
      </div>
    </div>
  );
};

export default EditPatientPage;
