const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkAppointments() {
  try {
    const appointments = await prisma.appointment.findMany({
      where: { isDeleted: false },
      include: {
        patient: {
          select: {
            firstName: true,
            lastName: true,
            patientId: true
          }
        }
      },
      take: 10
    });
    console.log('Current appointments:', appointments.length);
    appointments.forEach(a => {
      console.log(`- ${a.patient?.firstName} ${a.patient?.lastName} - Date: ${a.date} - Type: ${a.type}`);
    });
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAppointments();
