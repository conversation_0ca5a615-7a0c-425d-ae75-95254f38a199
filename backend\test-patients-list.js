const axios = require('axios');

async function testPatientsList() {
  try {
    console.log('Testing patients list API...');

    const response = await axios.get('http://localhost:3002/api/patients', {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 5000
    });

    console.log('Success! Response status:', response.status);
    console.log('Response structure:');
    console.log('- success:', response.data.success);
    console.log('- data type:', typeof response.data.data);
    console.log('- data is array:', Array.isArray(response.data.data));
    if (Array.isArray(response.data.data)) {
      console.log('- data length:', response.data.data.length);
      if (response.data.data.length > 0) {
        console.log('- first patient keys:', Object.keys(response.data.data[0]));
      }
    }
  } catch (error) {
    console.error('Error fetching patients list:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Message:', error.message);
    }
  }
}

testPatientsList();
