import express from 'express';
import { AppointmentController } from '../controllers/appointmentController';
import { authorize, authenticate } from '../middleware/auth';
import { asyncHandler } from '../utils/asyncHandler';

/**
 * Appointment routes
 * Base path: /api/appointments
 */

const router = express.Router();

// All appointment routes require authentication
router.use(authenticate);

/**
 * @route   GET /api/appointments/stats
 * @desc    Get appointment statistics
 * @access  Private (All authenticated users)
 */
router.get('/stats', asyncHandler(AppointmentController.getAppointmentStats));

/**
 * @route   GET /api/appointments/providers/:providerId/availability
 * @desc    Get provider availability for a specific date
 * @access  Private (All authenticated users)
 * @query   date (required)
 */
router.get('/providers/:providerId/availability', asyncHandler(AppointmentController.getProviderAvailability));

/**
 * @route   GET /api/appointments
 * @desc    Get appointments with pagination, search, and filtering
 * @access  Private (All authenticated users)
 * @query   page, limit, patientId, providerId, status, type, dateFrom, dateTo, sortBy, sortOrder
 */
router.get('/', asyncHandler(AppointmentController.getAppointments));

/**
 * @route   POST /api/appointments
 * @desc    Create a new appointment
 * @access  Private (ADMIN, CLINICIAN, STAFF)
 * @body    Appointment data (patientId, providerId, date, duration, etc.)
 */
router.post('/', authorize(['ADMIN', 'CLINICIAN', 'STAFF']), asyncHandler(AppointmentController.createAppointment));

/**
 * @route   GET /api/appointments/:id
 * @desc    Get appointment by ID
 * @access  Private (All authenticated users)
 */
router.get('/:id', asyncHandler(AppointmentController.getAppointmentById));

/**
 * @route   PUT /api/appointments/:id
 * @desc    Update appointment
 * @access  Private (ADMIN, CLINICIAN, STAFF)
 */
router.put('/:id', authorize(['ADMIN', 'CLINICIAN', 'STAFF']), asyncHandler(AppointmentController.updateAppointment));

/**
 * @route   POST /api/appointments/:id/cancel
 * @desc    Cancel appointment
 * @access  Private (ADMIN, CLINICIAN, STAFF)
 * @body    { reason: string }
 */
router.post('/:id/cancel', authorize(['ADMIN', 'CLINICIAN', 'STAFF']), asyncHandler(AppointmentController.cancelAppointment));

export default router;
