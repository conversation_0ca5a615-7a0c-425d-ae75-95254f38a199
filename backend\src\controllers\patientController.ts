import { Response, NextFunction } from 'express';
import { z } from 'zod';
import { PatientService } from '@/services/patientService';
import { AuthRequest, CreatePatientData, UpdatePatientData } from '@/types';
import { createPatientSchema, updatePatientSchema } from '../utils/validation';

/**
 * Patient controller for handling HTTP requests related to patients
 */

const patientQuerySchema = z.object({
  page: z.string().optional(),
  limit: z.string().optional(),
  search: z.string().optional(),
  gender: z.enum(['MALE', 'FEMALE', 'NON_BINARY', 'PREFER_NOT_TO_SAY', 'OTHER']).optional(),
  isActive: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

export class PatientController {
  /**
   * Get patient statistics
   * GET /api/patients/stats
   */
  static async getPatientStats(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const result = await PatientService.getPatientStats(req.user!.id, req.user!.role);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create a new patient
   * POST /api/patients
   */
  static async createPatient(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const validatedData = createPatientSchema.parse(req.body) as CreatePatientData;

      const result = await PatientService.createPatient(
        validatedData,
        req.user!.id,
        { ipAddress: req.ip, userAgent: req.get('User-Agent') }
      );

      res.status(201).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all patients with pagination and filtering
   * GET /api/patients
   */
  static async getPatients(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const query = patientQuerySchema.parse(req.query);
      
      const result = await PatientService.getPatients(
        query,
        req.user!.id,
        req.user!.role
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get patient by ID
   * GET /api/patients/:id
   */
  static async getPatientById(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const result = await PatientService.getPatientById(
        req.params.id,
        req.user!.id,
        req.user!.role
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update a patient
   * PUT /api/patients/:id
   */
  static async updatePatient(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const validatedData = updatePatientSchema.parse(req.body) as UpdatePatientData;

      const result = await PatientService.updatePatient(
        req.params.id,
        validatedData,
        req.user!.id,
        req.user!.role,
        { ipAddress: req.ip, userAgent: req.get('User-Agent') }
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete a patient
   * DELETE /api/patients/:id
   */
  static async deletePatient(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const result = await PatientService.deletePatient(
        req.params.id,
        req.user!.id,
        req.user!.role,
        { ipAddress: req.ip, userAgent: req.get('User-Agent') }
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Search for patients
   * GET /api/patients/search
   */
  static async searchPatients(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const { term } = req.query;
      if (!term || typeof term !== 'string') {
        res.status(400).json({ success: false, error: 'Search term is required' });
        return;
      }

      const result = await PatientService.searchPatients(term, req.user!.id, req.user!.role);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }
}
