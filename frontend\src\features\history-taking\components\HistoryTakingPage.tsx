import React, { useState, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { <PERSON>, Card<PERSON>eader, Card<PERSON><PERSON>le, CardContent, Button, Badge } from '../../../components/ui';
import { Brain, Heart, ArrowLeft, ArrowRight, Save, CheckCircle, AlertCircle } from 'lucide-react';
import MoodDisordersAssessment from './MoodDisordersAssessment';
import PsychoticDisordersAssessment from './PsychoticDisordersAssessment';
import type { DisorderAssessment, AssessmentSession } from '../types';
import { historyTakingService } from '../services/historyTakingService';
import { handleApiError } from '../../../lib/api';

interface AssessmentStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  component: React.ComponentType<any>;
  category: string;
}

const HistoryTakingPage: React.FC = () => {
  const { patientId } = useParams<{ patientId: string }>();
  const navigate = useNavigate();

  const [currentStep, setCurrentStep] = useState(0);
  const [assessments, setAssessments] = useState<Record<string, Partial<DisorderAssessment>>>({});
  const [sessionNotes, setSessionNotes] = useState('');
  const [riskAssessment, setRiskAssessment] = useState({
    suicidalIdeation: false,
    homicidalIdeation: false,
    selfHarm: false,
    substanceUse: false,
    level: 'low' as const
  });
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const assessmentSteps: AssessmentStep[] = useMemo(() => [
    {
      id: 'mood-disorders',
      title: 'Mood Disorders',
      description: 'Assess for depressive and bipolar disorders',
      icon: <Heart className="h-5 w-5 text-red-500" />,
      component: MoodDisordersAssessment,
      category: 'mood'
    },
    {
      id: 'psychotic-disorders',
      title: 'Psychotic Disorders',
      description: 'Assess for schizophrenia spectrum disorders',
      icon: <Brain className="h-5 w-5 text-purple-500" />,
      component: PsychoticDisordersAssessment,
      category: 'psychotic'
    },
    // Additional assessment modules can be added here
  ], []);

  const currentStepData = assessmentSteps[currentStep];
  const CurrentComponent = currentStepData?.component;

  const handleAssessmentChange = (stepId: string, assessment: Partial<DisorderAssessment>) => {
    setAssessments(prev => ({
      ...prev,
      [stepId]: assessment
    }));
  };

  const getStepStatus = (stepIndex: number) => {
    const step = assessmentSteps[stepIndex];
    const assessment = assessments[step.id];
    
    if (!assessment || !assessment.criteria) return 'not-started';
    
    const completedCriteria = assessment.criteria.filter(c => c.present !== undefined).length;
    const totalCriteria = assessment.criteria.length;
    
    if (completedCriteria === 0) return 'not-started';
    if (completedCriteria < totalCriteria) return 'in-progress';
    return 'completed';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'in-progress':
        return <div className="h-4 w-4 rounded-full bg-yellow-500" />;
      default:
        return <div className="h-4 w-4 rounded-full border-2 border-gray-300" />;
    }
  };

  const canProceed = () => {
    return currentStep < assessmentSteps.length - 1;
  };

  const canGoBack = () => {
    return currentStep > 0;
  };

  const handleNext = () => {
    if (canProceed()) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (canGoBack()) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleSaveSession = async () => {
    try {
      console.log('Attempting to save session for patientId:', patientId);
      setIsSaving(true);
      setError(null);
      
      // Validate that we have at least one assessment
      const assessmentValues = Object.values(assessments).filter(a => a.disorderId);
      if (assessmentValues.length === 0) {
        setError('At least one assessment must be completed before saving');
        setIsSaving(false);
        return;
      }
      
      const session: Partial<AssessmentSession> = {
        patientId: patientId!,
        sessionDate: new Date().toISOString(),
        assessments: assessmentValues as DisorderAssessment[],
        clinicalImpression: sessionNotes,
        riskAssessment,
        status: 'completed',
        duration: 60 // TODO: Calculate actual duration
      };

      // Save to backend
      await historyTakingService.saveAssessmentSession(patientId!, session);
      
      // Navigate back to patient details
      navigate(`/patients/${patientId}`);
    } catch (error) {
      console.error('Error saving assessment:', error);
      setError(handleApiError(error));
      setIsSaving(false);
    }
  };
  
  const handleSaveDraft = async () => {
    try {
      console.log('Attempting to save draft for patientId:', patientId);
      setIsSaving(true);
      setError(null);
      
      const assessmentValues = Object.values(assessments).filter(a => a.disorderId);
      if (assessmentValues.length === 0) {
        setError('At least one assessment must be started before saving a draft');
        setIsSaving(false);
        return;
      }
      
      const session: Partial<AssessmentSession> = {
        patientId: patientId!,
        sessionDate: new Date().toISOString(),
        assessments: assessmentValues as DisorderAssessment[],
        clinicalImpression: sessionNotes,
        riskAssessment,
        status: 'in-progress',
        duration: 60
      };

      // Save draft to backend
      await historyTakingService.saveAssessmentSession(patientId!, session);
      
      // Show success message but stay on the page
      setError(null);
      setIsSaving(false);
    } catch (error) {
      console.error('Error saving draft:', error);
      setError(handleApiError(error));
      setIsSaving(false);
    }
  };

  const getOverallProgress = () => {
    const totalSteps = assessmentSteps.length;
    const completedSteps = assessmentSteps.filter((_, index) => getStepStatus(index) === 'completed').length;
    return Math.round((completedSteps / totalSteps) * 100);
  };

  if (!patientId) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Patient ID is required for assessment</p>
        <Button onClick={() => navigate('/patients')} className="mt-4">
          Back to Patients
        </Button>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={() => navigate(`/patients/${patientId}`)}
            leftIcon={<ArrowLeft className="h-4 w-4" />}
          >
            Back to Patient
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Psychiatric Assessment</h1>
            <p className="text-gray-600">Patient ID: {patientId}</p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-sm text-gray-500">Overall Progress</div>
          <div className="text-2xl font-bold text-gray-900">{getOverallProgress()}%</div>
        </div>
      </div>

      {/* Progress Steps */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            {assessmentSteps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <button
                  onClick={() => setCurrentStep(index)}
                  className={`flex items-center space-x-3 p-3 rounded-lg transition-colors ${
                    index === currentStep
                      ? 'bg-blue-50 border-2 border-blue-500'
                      : 'hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(getStepStatus(index))}
                    {step.icon}
                  </div>
                  <div className="text-left">
                    <div className="font-medium text-gray-900">{step.title}</div>
                    <div className="text-xs text-gray-500">{step.description}</div>
                  </div>
                </button>
                {index < assessmentSteps.length - 1 && (
                  <div className="w-8 h-px bg-gray-300 mx-2" />
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Current Assessment */}
      {CurrentComponent && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                {currentStepData.icon}
                <span>{currentStepData.title} Assessment</span>
                <Badge variant="outline">Step {currentStep + 1} of {assessmentSteps.length}</Badge>
              </CardTitle>
            </CardHeader>
          </Card>

          <CurrentComponent
            patientId={patientId}
            onAssessmentChange={(assessment: Partial<DisorderAssessment>) => 
              handleAssessmentChange(currentStepData.id, assessment)
            }
            initialAssessment={assessments[currentStepData.id]}
          />
        </div>
      )}

      {/* Risk Assessment */}
      <Card>
        <CardHeader>
          <CardTitle>Risk Assessment</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              { key: 'suicidalIdeation', label: 'Suicidal Ideation' },
              { key: 'homicidalIdeation', label: 'Homicidal Ideation' },
              { key: 'selfHarm', label: 'Self-Harm' },
              { key: 'substanceUse', label: 'Substance Use' }
            ].map(item => (
              <label key={item.key} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={riskAssessment[item.key as keyof typeof riskAssessment] as boolean}
                  onChange={(e) => setRiskAssessment(prev => ({
                    ...prev,
                    [item.key]: e.target.checked
                  }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">{item.label}</span>
              </label>
            ))}
          </div>
          
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Overall Risk Level
            </label>
            <select
              value={riskAssessment.level}
              onChange={(e) => setRiskAssessment(prev => ({
                ...prev,
                level: e.target.value as any
              }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="low">Low Risk</option>
              <option value="moderate">Moderate Risk</option>
              <option value="high">High Risk</option>
              <option value="imminent">Imminent Risk</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Clinical Notes */}
      <Card>
        <CardHeader>
          <CardTitle>Clinical Impression & Notes</CardTitle>
        </CardHeader>
        <CardContent>
          <textarea
            value={sessionNotes}
            onChange={(e) => setSessionNotes(e.target.value)}
            placeholder="Enter clinical impression, treatment recommendations, and additional notes..."
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            rows={6}
          />
        </CardContent>
      </Card>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start mb-4">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}
      
      {/* Navigation */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={!canGoBack() || isSaving}
          leftIcon={<ArrowLeft className="h-4 w-4" />}
        >
          Previous
        </Button>

        <div className="flex space-x-3">
          <Button
            variant="secondary"
            onClick={handleSaveDraft}
            disabled={isSaving}
            leftIcon={<Save className="h-4 w-4" />}
          >
            {isSaving ? 'Saving...' : 'Save Draft'}
          </Button>
          
          {canProceed() ? (
            <Button
              onClick={handleNext}
              disabled={isSaving}
              rightIcon={<ArrowRight className="h-4 w-4" />}
            >
              Next Assessment
            </Button>
          ) : (
            <Button
              onClick={handleSaveSession}
              disabled={isSaving}
              leftIcon={<CheckCircle className="h-4 w-4" />}
            >
              {isSaving ? 'Saving...' : 'Complete Assessment'}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default HistoryTakingPage;
