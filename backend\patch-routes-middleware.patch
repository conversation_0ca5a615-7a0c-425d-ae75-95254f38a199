diff --git a/src/middleware/auth.ts b/src/middleware/auth.ts
index 1234567..abcdefg 100644
--- a/src/middleware/auth.ts
+++ b/src/middleware/auth.ts
@@ -178,7 +178,7 @@ export const requirePermission = (permission: string) => {
  */
 export const optionalAuth = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
   try {
-    const token = req.header('Authorization')?.replace('Bearer ', '');
+    const token = req.header('Authorization')?.replace('Bearer ', '') || '';
     
     if (!token) {
       return next();
diff --git a/src/minimal-server.ts b/src/minimal-server.ts
index 2345678..bcdefgh 100644
--- a/src/minimal-server.ts
+++ b/src/minimal-server.ts
@@ -92,7 +92,7 @@ app.get('/health', (req, res) => {
 });
 
 // Error handling middleware
-app.use((err: any, req: Request, res: Response, next: NextFunction) => {
+app.use((err: any, req: Request, res: Response, next: NextFunction): void => {
   console.error('Error:', err);
   res.status(err.status || 500).json({
     success: false,
diff --git a/src/routes/auth.ts b/src/routes/auth.ts
index 3456789..cdefghi 100644
--- a/src/routes/auth.ts
+++ b/src/routes/auth.ts
@@ -32,7 +32,7 @@ router.post('/register', asyncHandler(AuthController.register));
  *       400:
  *         description: Invalid input
  */
-router.post('/login', asyncHandler(AuthController.login));
+router.post('/login', asyncHandler(AuthController.login as any));
 
 /**
  * @swagger
@@ -49,7 +49,7 @@ router.post('/login', asyncHandler(AuthController.login));
  *       401:
  *         description: Invalid token
  */
-router.post('/logout', authenticateToken, asyncHandler(AuthController.logout));
+router.post('/logout', authenticateToken, asyncHandler(AuthController.logout as any));
 
 /**
  * @swagger
@@ -66,7 +66,7 @@ router.post('/logout', authenticateToken, asyncHandler(AuthController.logout));
  *       401:
  *         description: Invalid refresh token
  */
-router.post('/refresh', asyncHandler(AuthController.refreshToken));
+router.post('/refresh', asyncHandler(AuthController.refreshToken as any));
 
 export default router;
diff --git a/src/routes/notifications.ts b/src/routes/notifications.ts
index 4567890..defghij 100644
--- a/src/routes/notifications.ts
+++ b/src/routes/notifications.ts
@@ -48,7 +48,7 @@ router.post('/', authenticateToken, asyncHandler(NotificationController.createNot
  *       401:
  *         description: Unauthorized
  */
-router.get('/', authenticateToken, asyncHandler(NotificationController.getNotifications));
+router.get('/', authenticateToken, asyncHandler(NotificationController.getNotifications as any));
 
 /**
  * @swagger
@@ -65,7 +65,7 @@ router.get('/', authenticateToken, asyncHandler(NotificationController.getNotific
  *       404:
  *         description: Notification not found
  */
-router.get('/:id', authenticateToken, asyncHandler(NotificationController.getNotificationById));
+router.get('/:id', authenticateToken, asyncHandler(NotificationController.getNotificationById as any));
 
 /**
  * @swagger
@@ -82,7 +82,7 @@ router.get('/:id', authenticateToken, asyncHandler(NotificationController.getNoti
  *       404:
  *         description: Notification not found
  */
-router.patch('/:id/read', authenticateToken, asyncHandler(NotificationController.markAsRead));
+router.patch('/:id/read', authenticateToken, asyncHandler(NotificationController.markAsRead as any));
 
 /**
  * @swagger
@@ -96,7 +96,7 @@ router.patch('/:id/read', authenticateToken, asyncHandler(NotificationController.
  *       401:
  *         description: Unauthorized
  */
-router.patch('/read-all', authenticateToken, asyncHandler(NotificationController.markAllAsRead));
+router.patch('/read-all', authenticateToken, asyncHandler(NotificationController.markAllAsRead as any));
 
 /**
  * @swagger
@@ -113,7 +113,7 @@ router.patch('/read-all', authenticateToken, asyncHandler(NotificationController.
  *       404:
  *         description: Notification not found
  */
-router.delete('/:id', authenticateToken, asyncHandler(NotificationController.deleteNotification));
+router.delete('/:id', authenticateToken, asyncHandler(NotificationController.deleteNotification as any));
 
 /**
  * @swagger
@@ -130,7 +130,7 @@ router.delete('/:id', authenticateToken, asyncHandler(NotificationController.dele
  *       404:
  *         description: Appointment not found
  */
-router.post('/appointment-reminders/:appointmentId', authenticateToken, asyncHandler(NotificationController.createAppointmentReminders));
+router.post('/appointment-reminders/:appointmentId', authenticateToken, asyncHandler(NotificationController.createAppointmentReminders as any));
 
 /**
  * @swagger
@@ -147,7 +147,7 @@ router.post('/appointment-reminders/:appointmentId', authenticateToken, asyncHan
  *       404:
  *         description: Lab result not found
  */
-router.post('/lab-result/:labResultId', authenticateToken, asyncHandler(NotificationController.sendLabResultNotification));
+router.post('/lab-result/:labResultId', authenticateToken, asyncHandler(NotificationController.sendLabResultNotification as any));
 
 /**
  * @swagger
@@ -161,6 +161,6 @@ router.post('/lab-result/:labResultId', authenticateToken, asyncHandler(Notifica
  *       401:
  *         description: Unauthorized
  */
-router.get('/stats/me', authenticateToken, asyncHandler(NotificationController.getNotificationStats));
+router.get('/stats/me', authenticateToken, asyncHandler(NotificationController.getNotificationStats as any));
 
 export default router;
diff --git a/src/routes/patients.ts b/src/routes/patients.ts
index 5678901..defghik 100644
--- a/src/routes/patients.ts
+++ b/src/routes/patients.ts
@@ -19,7 +19,7 @@ const router = Router();
  *       401:
  *         description: Unauthorized
  */
-router.post('/', authenticateToken, asyncHandler(PatientController.createPatient));
+router.post('/', authenticateToken, asyncHandler(PatientController.createPatient as any));
 
 /**
  * @swagger
@@ -36,7 +36,7 @@ router.post('/', authenticateToken, asyncHandler(PatientController.createPatient)
  *       401:
  *         description: Unauthorized
  */
-router.get('/', authenticateToken, asyncHandler(PatientController.getPatients));
+router.get('/', authenticateToken, asyncHandler(PatientController.getPatients as any));
 
 /**
  * @swagger
@@ -53,7 +53,7 @@ router.get('/', authenticateToken, asyncHandler(PatientController.getPatients));
  *       404:
  *         description: Patient not found
  */
-router.get('/:id', authenticateToken, asyncHandler(PatientController.getPatientById));
+router.get('/:id', authenticateToken, asyncHandler(PatientController.getPatientById as any));
 
 /**
  * @swagger
@@ -70,7 +70,7 @@ router.get('/:id', authenticateToken, asyncHandler(PatientController.getPatientB
  *       404:
  *         description: Patient not found
  */
-router.put('/:id', authenticateToken, asyncHandler(PatientController.updatePatient));
+router.put('/:id', authenticateToken, asyncHandler(PatientController.updatePatient as any));
 
 /**
  * @swagger
@@ -87,7 +87,7 @@ router.put('/:id', authenticateToken, asyncHandler(PatientController.updatePatie
  *       404:
  *         description: Patient not found
  */
-router.delete('/:id', authenticateToken, asyncHandler(PatientController.deletePatient));
+router.delete('/:id', authenticateToken, asyncHandler(PatientController.deletePatient as any));
 
 /**
  * @swagger
@@ -104,6 +104,6 @@ router.delete('/:id', authenticateToken, asyncHandler(PatientController.deletePa
  *       404:
  *         description: Patient not found
  */
-router.get('/:id/appointments', authenticateToken, asyncHandler(PatientController.getPatientAppointments));
+router.get('/:id/appointments', authenticateToken, asyncHandler(PatientController.getPatientAppointments as any));
 
 export default router;