import { describe, it, expect, beforeEach } from 'vitest'
import { prisma, createTestUser, createTestPatient } from './setup'

describe('Database Operations', () => {
  beforeEach(async () => {
    // Database is cleaned before each test in setup.ts
  })

  describe('User Operations', () => {
    it('should create user', async () => {
      const user = await createTestUser('CLINICIAN')
      
      expect(user.id).toBeDefined()
      expect(user.username).toContain('testuser_')
      expect(user.email).toContain('test_')
      expect(user.role).toBe('CLINICIAN')
      expect(user.isActive).toBe(true)
    })

    it('should find user by id', async () => {
      const user = await createTestUser('ADMIN')
      
      const foundUser = await prisma.user.findUnique({
        where: { id: user.id }
      })
      
      expect(foundUser).toBeDefined()
      expect(foundUser?.id).toBe(user.id)
      expect(foundUser?.role).toBe('ADMIN')
    })

    it('should update user', async () => {
      const user = await createTestUser('STAFF')
      
      const updatedUser = await prisma.user.update({
        where: { id: user.id },
        data: { firstName: 'Updated' }
      })
      
      expect(updatedUser.firstName).toBe('Updated')
      expect(updatedUser.id).toBe(user.id)
    })
  })

  describe('Patient Operations', () => {
    it('should create patient', async () => {
      const user = await createTestUser()
      const patient = await createTestPatient(user.id)
      
      expect(patient.id).toBeDefined()
      expect(patient.patientId).toContain('P-TEST-')
      expect(patient.firstName).toBe('John')
      expect(patient.lastName).toBe('Doe')
      expect(patient.createdBy).toBe(user.id)
      expect(patient.isActive).toBe(true)
      expect(patient.isDeleted).toBe(false)
    })

    it('should find patient by id', async () => {
      const user = await createTestUser()
      const patient = await createTestPatient(user.id)
      
      const foundPatient = await prisma.patient.findUnique({
        where: { id: patient.id }
      })
      
      expect(foundPatient).toBeDefined()
      expect(foundPatient?.id).toBe(patient.id)
      expect(foundPatient?.firstName).toBe('John')
    })

    it('should list active patients', async () => {
      const user = await createTestUser()
      await createTestPatient(user.id)
      await createTestPatient(user.id)
      
      const patients = await prisma.patient.findMany({
        where: { 
          isActive: true,
          isDeleted: false 
        }
      })
      
      expect(patients).toHaveLength(2)
      expect(patients.every(p => p.isActive)).toBe(true)
      expect(patients.every(p => !p.isDeleted)).toBe(true)
    })
  })

  describe('Lab Result Operations', () => {
    it('should create lab result', async () => {
      const user = await createTestUser()
      const patient = await createTestPatient(user.id)
      
      const labResult = await prisma.labResult.create({
        data: {
          patientId: patient.id,
          testType: 'CBC',
          testDate: new Date(),
          orderedBy: 'Dr. Test',
          labName: 'Test Lab',
          results: JSON.stringify({
            WBC: { value: 7.5, unit: 'K/uL', normalRange: '4.0-11.0', isNormal: true }
          }),
          status: 'COMPLETED',
          createdBy: user.id,
          isDeleted: false,
        },
      })
      
      expect(labResult.id).toBeDefined()
      expect(labResult.testType).toBe('CBC')
      expect(labResult.patientId).toBe(patient.id)
      expect(labResult.status).toBe('COMPLETED')
    })
  })

  describe('Appointment Operations', () => {
    it('should create appointment', async () => {
      const user = await createTestUser()
      const patient = await createTestPatient(user.id)
      
      const appointment = await prisma.appointment.create({
        data: {
          patientId: patient.id,
          providerId: user.id,
          date: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
          duration: 60,
          type: 'INITIAL_CONSULTATION',
          status: 'SCHEDULED',
          notes: 'Test appointment',
        },
      })
      
      expect(appointment.id).toBeDefined()
      expect(appointment.patientId).toBe(patient.id)
      expect(appointment.providerId).toBe(user.id)
      expect(appointment.type).toBe('INITIAL_CONSULTATION')
      expect(appointment.status).toBe('SCHEDULED')
    })
  })
})
