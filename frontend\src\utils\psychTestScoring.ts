// Psychological Test Scoring Algorithms
// Based on questionnaire.md specifications

export interface TestResponse {
  questionId: string;
  value: number;
}

export interface TestResult {
  totalScore: number;
  severity: string;
  interpretation: string;
  recommendations: string[];
  subscaleScores?: Record<string, number>;
}

// PHQ-9 Depression Screening
export const scorePHQ9 = (responses: TestResponse[]): TestResult => {
  const totalScore = responses.reduce((sum, response) => sum + response.value, 0);
  
  let severity: string;
  let interpretation: string;
  let recommendations: string[] = [];

  if (totalScore >= 0 && totalScore <= 4) {
    severity = 'minimal';
    interpretation = 'Minimal depression symptoms. No treatment indicated.';
    recommendations = ['Monitor symptoms', 'Lifestyle modifications'];
  } else if (totalScore >= 5 && totalScore <= 9) {
    severity = 'mild';
    interpretation = 'Mild depression symptoms. Consider watchful waiting or brief intervention.';
    recommendations = ['Psychoeducation', 'Lifestyle modifications', 'Follow-up in 2-4 weeks'];
  } else if (totalScore >= 10 && totalScore <= 14) {
    severity = 'moderate';
    interpretation = 'Moderate depression symptoms. Treatment recommended.';
    recommendations = ['Psychotherapy', 'Consider antidepressant medication', 'Regular monitoring'];
  } else if (totalScore >= 15 && totalScore <= 19) {
    severity = 'moderately severe';
    interpretation = 'Moderately severe depression symptoms. Active treatment recommended.';
    recommendations = ['Psychotherapy and medication', 'Close monitoring', 'Consider specialist referral'];
  } else {
    severity = 'severe';
    interpretation = 'Severe depression symptoms. Immediate treatment required.';
    recommendations = ['Immediate psychiatric evaluation', 'Combination therapy', 'Safety assessment'];
  }

  return {
    totalScore,
    severity,
    interpretation,
    recommendations
  };
};

// GAD-7 Anxiety Screening
export const scoreGAD7 = (responses: TestResponse[]): TestResult => {
  const totalScore = responses.reduce((sum, response) => sum + response.value, 0);
  
  let severity: string;
  let interpretation: string;
  let recommendations: string[] = [];

  if (totalScore >= 0 && totalScore <= 4) {
    severity = 'minimal';
    interpretation = 'Minimal anxiety symptoms. No treatment indicated.';
    recommendations = ['Monitor symptoms', 'Stress management techniques'];
  } else if (totalScore >= 5 && totalScore <= 9) {
    severity = 'mild';
    interpretation = 'Mild anxiety symptoms. Consider brief intervention.';
    recommendations = ['Relaxation techniques', 'Lifestyle modifications', 'Follow-up in 2-4 weeks'];
  } else if (totalScore >= 10 && totalScore <= 14) {
    severity = 'moderate';
    interpretation = 'Moderate anxiety symptoms. Treatment recommended.';
    recommendations = ['Cognitive behavioral therapy', 'Consider medication', 'Regular monitoring'];
  } else {
    severity = 'severe';
    interpretation = 'Severe anxiety symptoms. Active treatment required.';
    recommendations = ['Immediate treatment', 'Combination therapy', 'Consider specialist referral'];
  }

  return {
    totalScore,
    severity,
    interpretation,
    recommendations
  };
};

// Y-BOCS OCD Assessment
export const scoreYBOCS = (responses: TestResponse[]): TestResult => {
  // Y-BOCS has obsessions (items 1-5) and compulsions (items 6-10)
  const obsessionScore = responses.slice(0, 5).reduce((sum, response) => sum + response.value, 0);
  const compulsionScore = responses.slice(5, 10).reduce((sum, response) => sum + response.value, 0);
  const totalScore = obsessionScore + compulsionScore;
  
  let severity: string;
  let interpretation: string;
  let recommendations: string[] = [];

  if (totalScore >= 0 && totalScore <= 7) {
    severity = 'subclinical';
    interpretation = 'Subclinical OCD symptoms. No significant impairment.';
    recommendations = ['Monitor symptoms', 'Psychoeducation about OCD'];
  } else if (totalScore >= 8 && totalScore <= 15) {
    severity = 'mild';
    interpretation = 'Mild OCD symptoms with some interference.';
    recommendations = ['Cognitive behavioral therapy', 'Exposure and response prevention', 'Regular monitoring'];
  } else if (totalScore >= 16 && totalScore <= 23) {
    severity = 'moderate';
    interpretation = 'Moderate OCD symptoms with significant interference.';
    recommendations = ['Intensive CBT/ERP', 'Consider SSRI medication', 'Regular assessment'];
  } else if (totalScore >= 24 && totalScore <= 31) {
    severity = 'severe';
    interpretation = 'Severe OCD symptoms with substantial impairment.';
    recommendations = ['Intensive treatment', 'Combination therapy', 'Consider specialist referral'];
  } else {
    severity = 'extreme';
    interpretation = 'Extreme OCD symptoms with severe impairment.';
    recommendations = ['Immediate intensive treatment', 'Specialist care', 'Consider inpatient treatment'];
  }

  return {
    totalScore,
    severity,
    interpretation,
    recommendations,
    subscaleScores: {
      obsessions: obsessionScore,
      compulsions: compulsionScore
    }
  };
};

// MMSE Cognitive Screening
export const scoreMMSE = (responses: TestResponse[]): TestResult => {
  const totalScore = responses.reduce((sum, response) => sum + response.value, 0);
  
  let severity: string;
  let interpretation: string;
  let recommendations: string[] = [];

  if (totalScore >= 24 && totalScore <= 30) {
    severity = 'normal';
    interpretation = 'Normal cognitive function. No cognitive impairment detected.';
    recommendations = ['Routine monitoring', 'Maintain cognitive activities'];
  } else if (totalScore >= 18 && totalScore <= 23) {
    severity = 'mild impairment';
    interpretation = 'Mild cognitive impairment. Further evaluation recommended.';
    recommendations = ['Comprehensive neuropsychological testing', 'Medical evaluation', 'Cognitive rehabilitation'];
  } else if (totalScore >= 10 && totalScore <= 17) {
    severity = 'moderate impairment';
    interpretation = 'Moderate cognitive impairment. Significant functional impact.';
    recommendations = ['Neurological evaluation', 'Caregiver support', 'Safety assessment'];
  } else {
    severity = 'severe impairment';
    interpretation = 'Severe cognitive impairment. Substantial care needs.';
    recommendations = ['Immediate medical evaluation', 'Comprehensive care planning', 'Caregiver education'];
  }

  return {
    totalScore,
    severity,
    interpretation,
    recommendations
  };
};

// PANSS Psychosis Assessment
export const scorePANSS = (responses: TestResponse[]): TestResult => {
  // PANSS has 3 subscales: Positive (7 items), Negative (7 items), General (16 items)
  const positiveScore = responses.slice(0, 7).reduce((sum, response) => sum + response.value, 0);
  const negativeScore = responses.slice(7, 14).reduce((sum, response) => sum + response.value, 0);
  const generalScore = responses.slice(14, 30).reduce((sum, response) => sum + response.value, 0);
  const totalScore = positiveScore + negativeScore + generalScore;
  
  let severity: string;
  let interpretation: string;
  let recommendations: string[] = [];

  if (totalScore >= 30 && totalScore <= 58) {
    severity = 'minimal';
    interpretation = 'Minimal psychotic symptoms. Stable condition.';
    recommendations = ['Continue current treatment', 'Regular monitoring', 'Psychosocial support'];
  } else if (totalScore >= 59 && totalScore <= 75) {
    severity = 'mild';
    interpretation = 'Mild psychotic symptoms. Some functional impairment.';
    recommendations = ['Optimize medication', 'Psychosocial interventions', 'Regular assessment'];
  } else if (totalScore >= 76 && totalScore <= 95) {
    severity = 'moderate';
    interpretation = 'Moderate psychotic symptoms. Significant impairment.';
    recommendations = ['Medication adjustment', 'Intensive case management', 'Family support'];
  } else if (totalScore >= 96 && totalScore <= 116) {
    severity = 'marked';
    interpretation = 'Marked psychotic symptoms. Substantial impairment.';
    recommendations = ['Intensive treatment', 'Consider hospitalization', 'Crisis intervention'];
  } else {
    severity = 'severe';
    interpretation = 'Severe psychotic symptoms. Severe functional impairment.';
    recommendations = ['Immediate intensive treatment', 'Hospitalization likely needed', 'Safety assessment'];
  }

  return {
    totalScore,
    severity,
    interpretation,
    recommendations,
    subscaleScores: {
      positive: positiveScore,
      negative: negativeScore,
      general: generalScore
    }
  };
};

// PCL-5 PTSD Assessment
export const scorePCL5 = (responses: TestResponse[]): TestResult => {
  const totalScore = responses.reduce((sum, response) => sum + response.value, 0);
  
  // PCL-5 subscales
  const reexperiencing = responses.slice(0, 5).reduce((sum, response) => sum + response.value, 0);
  const avoidance = responses.slice(5, 7).reduce((sum, response) => sum + response.value, 0);
  const cognitionMood = responses.slice(7, 14).reduce((sum, response) => sum + response.value, 0);
  const arousal = responses.slice(14, 20).reduce((sum, response) => sum + response.value, 0);
  
  let severity: string;
  let interpretation: string;
  let recommendations: string[] = [];

  // PCL-5 cutoff score is typically 33
  if (totalScore < 33) {
    severity = 'below threshold';
    interpretation = 'PTSD symptoms below clinical threshold. Monitor for changes.';
    recommendations = ['Psychoeducation about trauma', 'Stress management', 'Follow-up as needed'];
  } else if (totalScore >= 33 && totalScore <= 49) {
    severity = 'moderate';
    interpretation = 'Moderate PTSD symptoms. Clinical evaluation recommended.';
    recommendations = ['Trauma-focused therapy', 'Consider medication', 'Regular monitoring'];
  } else if (totalScore >= 50 && totalScore <= 64) {
    severity = 'severe';
    interpretation = 'Severe PTSD symptoms. Active treatment required.';
    recommendations = ['Intensive trauma therapy', 'Medication evaluation', 'Safety assessment'];
  } else {
    severity = 'extreme';
    interpretation = 'Extreme PTSD symptoms. Immediate intervention needed.';
    recommendations = ['Immediate psychiatric evaluation', 'Crisis intervention', 'Intensive treatment'];
  }

  return {
    totalScore,
    severity,
    interpretation,
    recommendations,
    subscaleScores: {
      reexperiencing,
      avoidance,
      cognitionMood,
      arousal
    }
  };
};

// Generic scoring function that routes to specific test scorers
export const scoreTest = (testName: string, responses: TestResponse[]): TestResult => {
  switch (testName.toUpperCase()) {
    case 'PHQ-9':
      return scorePHQ9(responses);
    case 'GAD-7':
      return scoreGAD7(responses);
    case 'Y-BOCS':
      return scoreYBOCS(responses);
    case 'MMSE':
      return scoreMMSE(responses);
    case 'PANSS':
      return scorePANSS(responses);
    case 'PCL-5':
      return scorePCL5(responses);
    default:
      throw new Error(`Scoring not implemented for test: ${testName}`);
  }
};
