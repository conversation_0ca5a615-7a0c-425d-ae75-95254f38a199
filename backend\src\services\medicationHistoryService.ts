import { prisma } from '../utils/database';
import { ApiResponse, PaginatedResponse } from '../types';

export interface CreateMedicationHistoryData {
  patientId: string;
  medicationName: string;
  genericName?: string;
  brandName?: string;
  strength: string;
  dosage: string;
  frequency: string;
  route: string;
  indication: string;
  startDate: string;
  endDate?: string;
  duration?: string;
  prescribedBy: string;
  prescriberId?: string;
  pharmacy?: string;
  sideEffects?: string;
  effectiveness?: string;
  adherence?: string;
  adherenceNotes?: string;
  discontinuedReason?: string;
  allergicReaction?: boolean;
  interactions?: string;
  monitoring?: string;
  prn?: boolean;
  notes?: string;
  isActive?: boolean;
}

export interface UpdateMedicationHistoryData extends Partial<CreateMedicationHistoryData> {}

export interface MedicationHistoryFilters {
  patientId?: string;
  medicationName?: string;
  isActive?: boolean;
}

export class MedicationHistoryService {
  static async createMedicationHistory(
    data: CreateMedicationHistoryData,
    createdBy: string,
    auditContext: { ipAddress?: string; userAgent?: string }
  ): Promise<ApiResponse<any>> {
    try {
      // Verify patient exists
      const patient = await prisma.patient.findUnique({
        where: { id: data.patientId },
      });

      if (!patient) {
        return {
          success: false,
          error: 'Patient not found',
        };
      }

      // Parse dates
      const startDate = new Date(data.startDate);
      const endDate = data.endDate ? new Date(data.endDate) : null;

      // Create medication history
      const medicationHistory = await prisma.medicationHistory.create({
        data: {
          patientId: data.patientId,
          medicationName: data.medicationName.trim(),
          genericName: data.genericName?.trim() || null,
          brandName: data.brandName?.trim() || null,
          strength: data.strength.trim(),
          dosage: data.dosage.trim(),
          frequency: data.frequency.trim(),
          route: data.route.trim(),
          indication: data.indication.trim(),
          startDate,
          endDate,
          duration: data.duration?.trim() || null,
          prescribedBy: data.prescribedBy.trim(),
          prescriberId: data.prescriberId || null,
          pharmacy: data.pharmacy?.trim() || null,
          sideEffects: data.sideEffects || null,
          effectiveness: data.effectiveness || null,
          adherence: data.adherence || null,
          adherenceNotes: data.adherenceNotes?.trim() || null,
          discontinuedReason: data.discontinuedReason?.trim() || null,
          allergicReaction: data.allergicReaction || false,
          interactions: data.interactions || null,
          monitoring: data.monitoring?.trim() || null,
          prn: data.prn || false,
          notes: data.notes?.trim() || null,
          isActive: data.isActive !== false,
        },
        include: {
          patient: {
            select: {
              id: true,
              patientId: true,
              firstName: true,
              lastName: true,
            },
          },
          prescriber: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              role: true,
            },
          },
        },
      });

      // TODO: Add audit logging when AuditService is implemented

      return {
        success: true,
        data: medicationHistory,
        message: 'Medication history created successfully',
      };
    } catch (error) {
      console.error('Error creating medication history:', error);
      return {
        success: false,
        error: 'Failed to create medication history',
      };
    }
  }

  static async getAllMedicationHistory(
    filters: MedicationHistoryFilters,
    page: number = 1,
    limit: number = 10
  ): Promise<PaginatedResponse<any>> {
    try {
      const skip = (page - 1) * limit;

      const where: any = {};

      if (filters.patientId) {
        where.patientId = filters.patientId;
      }

      if (filters.medicationName) {
        where.medicationName = {
          contains: filters.medicationName,
          mode: 'insensitive',
        };
      }

      if (filters.isActive !== undefined) {
        where.isActive = filters.isActive;
      }

      const [medicationHistory, total] = await Promise.all([
        prisma.medicationHistory.findMany({
          where,
          include: {
            patient: {
              select: {
                id: true,
                patientId: true,
                firstName: true,
                lastName: true,
              },
            },
            prescriber: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                role: true,
              },
            },
          },
          orderBy: { startDate: 'desc' },
          skip,
          take: limit,
        }),
        prisma.medicationHistory.count({ where }),
      ]);

      return {
        success: true,
        data: medicationHistory,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching medication history:', error);
      return {
        success: false,
        error: 'Failed to fetch medication history',
        data: [],
        pagination: { page, limit, total: 0, pages: 0 },
      };
    }
  }

  static async getMedicationHistoryById(id: string): Promise<ApiResponse<any>> {
    try {
      const medicationHistory = await prisma.medicationHistory.findUnique({
        where: { id },
        include: {
          patient: {
            select: {
              id: true,
              patientId: true,
              firstName: true,
              lastName: true,
            },
          },
          prescriber: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              role: true,
            },
          },
        },
      });

      if (!medicationHistory) {
        return {
          success: false,
          error: 'Medication history not found',
        };
      }

      return {
        success: true,
        data: medicationHistory,
      };
    } catch (error) {
      console.error('Error fetching medication history:', error);
      return {
        success: false,
        error: 'Failed to fetch medication history',
      };
    }
  }

  static async getMedicationHistoryByPatient(
    patientId: string,
    isActive?: boolean,
    limit: number = 20
  ): Promise<ApiResponse<any[]>> {
    try {
      const where: any = { patientId };

      if (isActive !== undefined) {
        where.isActive = isActive;
      }

      const medicationHistory = await prisma.medicationHistory.findMany({
        where,
        orderBy: { startDate: 'desc' },
        take: limit,
        include: {
          patient: {
            select: {
              id: true,
              patientId: true,
              firstName: true,
              lastName: true,
            },
          },
          prescriber: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              role: true,
            },
          },
        },
      });

      return {
        success: true,
        data: medicationHistory,
      };
    } catch (error) {
      console.error('Error fetching patient medication history:', error);
      return {
        success: false,
        error: 'Failed to fetch patient medication history',
        data: [],
      };
    }
  }

  static async updateMedicationHistory(
    id: string,
    data: UpdateMedicationHistoryData,
    updatedBy: string,
    auditContext: { ipAddress?: string; userAgent?: string }
  ): Promise<ApiResponse<any>> {
    try {
      // Get existing medication for audit
      const existingMedication = await prisma.medicationHistory.findUnique({
        where: { id },
      });

      if (!existingMedication) {
        return {
          success: false,
          error: 'Medication history not found',
        };
      }

      // Prepare update data
      const updateData: any = {};

      // Only update fields that are provided
      Object.keys(data).forEach(key => {
        const value = data[key as keyof UpdateMedicationHistoryData];
        if (value !== undefined) {
          if (key === 'startDate' || key === 'endDate') {
            updateData[key] = value ? new Date(value as string) : null;
          } else if (typeof value === 'string') {
            updateData[key] = value.trim();
          } else {
            updateData[key] = value;
          }
        }
      });

      // Update medication history
      const updatedMedication = await prisma.medicationHistory.update({
        where: { id },
        data: updateData,
        include: {
          patient: {
            select: {
              id: true,
              patientId: true,
              firstName: true,
              lastName: true,
            },
          },
          prescriber: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              role: true,
            },
          },
        },
      });

      // TODO: Add audit logging when AuditService is implemented

      return {
        success: true,
        data: updatedMedication,
        message: 'Medication history updated successfully',
      };
    } catch (error) {
      console.error('Error updating medication history:', error);
      return {
        success: false,
        error: 'Failed to update medication history',
      };
    }
  }

  static async deleteMedicationHistory(
    id: string,
    deletedBy: string,
    auditContext: { ipAddress?: string; userAgent?: string }
  ): Promise<ApiResponse<any>> {
    try {
      // Get existing medication for audit
      const existingMedication = await prisma.medicationHistory.findUnique({
        where: { id },
      });

      if (!existingMedication) {
        return {
          success: false,
          error: 'Medication history not found',
        };
      }

      // Delete medication history
      await prisma.medicationHistory.delete({
        where: { id },
      });

      // TODO: Add audit logging when AuditService is implemented

      return {
        success: true,
        message: 'Medication history deleted successfully',
      };
    } catch (error) {
      console.error('Error deleting medication history:', error);
      return {
        success: false,
        error: 'Failed to delete medication history',
      };
    }
  }
}
