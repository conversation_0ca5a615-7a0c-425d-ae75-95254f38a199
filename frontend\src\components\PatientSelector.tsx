import React, { useEffect } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { usePatientStore } from '../store/patientStore';
import { Users, Search, User } from 'lucide-react';
import { format } from 'date-fns';

export const PatientSelector = () => {
  const { 
    patients, 
    selectedPatient, 
    loading, 
    error, 
    fetchPatients, 
    selectPatient 
  } = usePatientStore();
  
  const [searchTerm, setSearchTerm] = React.useState('');

  useEffect(() => {
    fetchPatients();
  }, [fetchPatients]);

  const filteredPatients = patients.filter(patient =>
    `${patient.firstName} ${patient.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.patientId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-slate-600">Loading patients...</p>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchPatients} variant="outline">
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Users className="h-5 w-5 text-blue-600" />
          <span>Select Patient</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
            <Input
              placeholder="Search patients..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Selected Patient */}
          {selectedPatient && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <User className="h-8 w-8 text-blue-600" />
                <div>
                  <h3 className="font-medium text-blue-900">
                    {selectedPatient.firstName} {selectedPatient.lastName}
                  </h3>
                  <p className="text-sm text-blue-700">
                    ID: {selectedPatient.patientId} • DOB: {format(new Date(selectedPatient.dateOfBirth), 'MMM dd, yyyy')}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Patient List */}
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {filteredPatients.length === 0 ? (
              <div className="text-center py-8">
                <Users className="h-8 w-8 text-slate-400 mx-auto mb-3" />
                <p className="text-slate-600">No patients found.</p>
              </div>
            ) : (
              filteredPatients.map((patient) => (
                <div
                  key={patient.id}
                  className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                    selectedPatient?.id === patient.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-slate-200 hover:border-slate-300 hover:bg-slate-50'
                  }`}
                  onClick={() => selectPatient(patient)}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-slate-900">
                        {patient.firstName} {patient.lastName}
                      </h4>
                      <p className="text-sm text-slate-600">
                        ID: {patient.patientId}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-slate-600">
                        DOB: {format(new Date(patient.dateOfBirth), 'MMM dd, yyyy')}
                      </p>
                      <p className="text-xs text-slate-500">
                        {patient.gender}
                      </p>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
